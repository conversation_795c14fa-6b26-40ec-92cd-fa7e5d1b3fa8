let {harvest_or_termination__details_yesField_noPlanting} = require("./fixed_working_examples/land_management_activities/surveyStackExamples");
const builder = require("convention_builder");
const { randomUUID } = require('crypto');

let conventionEntityID = randomUUID();

let moreEntities = [
    {
        type: "taxonomy_term--convention",
        id: conventionEntityID,
        attributes: {
            name: "Harvest",
            external_uri: "https://gitlab.com/api/v4/projects/44311021/jobs/artifacts/main/raw/output/collection/conventions/log--harvest--harvest/schema.json?job=copy_schemas"
        }
    },
];

let example_array = [ ... harvest_or_termination__details_yesField_noPlanting, ... moreEntities ];

let logHarvests = example_array.filter( d => /^log--harvest/.test(d.type) );

logHarvests.forEach( log => {
    log
        .relationships
        .convention = {
            data: [
                {
                    type: "taxonomy_term--convention",
                    id: conventionEntityID
                }
            ]
        };
} );

let preparedExample = new builder.ArrayDataOrganizer({exampleArray: example_array});

await preparedExample.initialize();
/*
 errors: [
    {
      instancePath: '',
      schemaPath: '#/required',
      keyword: 'required',
      params: [Object],
      message: "must have required property 'log--harvest--harvest'"
    },
    {
      instancePath: '',
      schemaPath: '#/required',
      keyword: 'required',
      params: [Object],
      message: "must have required property 'asset--plant--planting'"
    }
  ],
*/


preparedExample.instances[0].unrecognizedEntities[0] // this is the log--harvest--harvest 

preparedExample.instances[0].unrecognizedEntities[0].allTests[0].errors

/* 
[
  {
    instancePath: '',
    schemaPath: '#/required',
    keyword: 'required',
    params: { missingProperty: 'id' },
    message: "must have required property 'id'"
  },
  {
    instancePath: '/attributes/timestamp',
    schemaPath: '#/properties/attributes/properties/timestamp/format',
    keyword: 'format',
    params: { format: 'date-time' },
    message: 'must match format "date-time"'
  },
]
*/

let terminationRejectedCandidate = preparedExample.instances[0].unrecognizedEntities.filter( e => e.entity.type == "log--harvest" )[0];

terminationRejectedCandidate.entity.id = randomUUID();
terminationRejectedCandidate.entity.attributes.timestamp = new Date( 1000 * parseFloat( terminationRejectedCandidate.entity.attributes.timestamp ) ).toISOString();

preparedExample.instances = [];
await preparedExample.initialize();

/*
[
  {
    instancePath: '',
    schemaPath: '#/required',
    keyword: 'required',
    params: { missingProperty: 'asset--plant--planting' },
    message: "must have required property 'asset--plant--planting'"
  },
  {
    instancePath: '/log--harvest--harvest/relationships/asset/data',
    schemaPath: '#/properties/log--harvest--harvest/properties/relationships/properties/asset/properties/data/contains',
    keyword: 'contains',
    params: { minContains: 1 },
    message: 'must contain at least 1 valid item(s)'
  }
]
*/

// has no data in the asset and thus is not valid 
// presumably the asset--plant 
