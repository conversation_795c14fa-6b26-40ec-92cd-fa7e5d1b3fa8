let {mowing_example_yesField_noPlanting} = require("./fixed_working_examples/land_management_activities/surveyStackExamples");
const builder = require("convention_builder");
const { randomUUID } = require('crypto');

let conventionEntityID = randomUUID();

let moreEntities = [
    // first taxonomy term
    {
        type: "taxonomy_term--convention",
        id: conventionEntityID,
        attributes: {
            name: "Mowing Convention Example",
            external_uri: "https://gitlab.com/api/v4/projects/44311021/jobs/artifacts/main/raw/output/collection/conventions/log--activity--mowing/schema.json?job=copy_schemas"
        }
    },
];

let example_array = [ ... mowing_example_yesField_noPlanting, ... moreEntities ];

let logActivities = example_array.filter( d => /^log--activity/.test(d.type) );
logActivities.forEach( log => {
    // add convention relationship
    log
        .relationships
        .convention = {
            data: [
                {
                    type: "taxonomy_term--convention",
                    id: conventionEntityID
                }
            ]
        };
} );

let preparedExample = new builder.ArrayDataOrganizer({exampleArray: example_array});

await preparedExample.initialize();

/*
errors: [
    {
      instancePath: '',
      schemaPath: '#/required',
      keyword: 'required',
      params: [Object],
      message: "must have required property 'log--activity--mowing'"
    },
    {
      instancePath: '',
      schemaPath: '#/required',
      keyword: 'required',
      params: [Object],
      message: "must have required property 'asset--plant--planting'"
    }
  ],
*/

// I couldn't find any asset--plants anywhere in the unrecognized entities. 
// There is an asset-land entity? 
// Figure out next steps on this

// This is looking inside of the mowing property requirements 
/* 
[
  {
    instancePath: '',
    schemaPath: '#/required',
    keyword: 'required',
    params: { missingProperty: 'id' },
    message: "must have required property 'id'"
  },
  {
    instancePath: '/attributes/timestamp',
    schemaPath: '#/properties/attributes/properties/timestamp/type',
    keyword: 'type',
    params: { type: 'string' },
    message: 'must be string'
  }
]
*/
let rakingRejectedCandidate = preparedExample.instances[0].unrecognizedEntities.filter( e => e.entity.type == "log--activity" )[0];

rakingRejectedCandidate.entity.id = randomUUID();

rakingRejectedCandidate.entity.attributes.timestamp = new Date( 1000 * parseFloat( rakingRejectedCandidate.entity.attributes.timestamp ) ).toISOString();

await preparedExample.structureInstances();

/* 
  errors: [
    {
      instancePath: '',
      schemaPath: '#/required',
      keyword: 'required',
      params: [Object],
      message: "must have required property 'asset--plant--planting'"
    },
    {
      instancePath: '/log--activity--mowing/relationships',
      schemaPath: '#/properties/log--activity--mowing/properties/relationships/additionalProperties',
      keyword: 'additionalProperties',
      params: [Object],
      message: 'must NOT have additional properties'
    },
    {
      instancePath: '/log--activity--mowing/relationships/asset/data',
      schemaPath: '#/properties/log--activity--mowing/properties/relationships/properties/asset/properties/data/contains',
      keyword: 'contains',
      params: [Object],
      message: 'must contain at least 1 valid item(s)'
    }
  ],
*/
/*
errors: [
    {
      instancePath: '',
      schemaPath: '#/required',
      keyword: 'required',
      params: [Object],
      message: "must have required property 'asset--plant--planting'"
    },
    {
      instancePath: '/log--activity--mowing/relationships',
      schemaPath: '#/properties/log--activity--mowing/properties/relationships/additionalProperties',
      keyword: 'additionalProperties',
      params: [Object],
      message: 'must NOT have additional properties'
    },
    {
      instancePath: '/log--activity--mowing/relationships/asset/data',
      schemaPath: '#/properties/log--activity--mowing/properties/relationships/properties/asset/properties/data/contains',
      keyword: 'contains',
      params: [Object],
      message: 'must contain at least 1 valid item(s)'
    }
  ],
*/

// There are no entities which are asset--plant 
// There are also no assets in preparedExample2.instances[1].identifiedEntities[1].entity.relationships (asset.data)
// I suspect this should be the asset--plant which would fix the 1st and 3rd errors
