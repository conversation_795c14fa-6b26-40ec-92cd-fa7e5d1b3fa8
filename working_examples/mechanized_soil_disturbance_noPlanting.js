let {mechanized_soil_noplanting_field} = require("./fixed_working_examples/land_management_activities/surveyStackExamples");
const builder = require("convention_builder");
const { randomUUID } = require('crypto');

let conventionEntityID = randomUUID();

let moreEntities = [
    {
        type: "taxonomy_term--convention",
        id: conventionEntityID,
        attributes: {
            name: "log--activity--tillage",
            external_uri: "https://gitlab.com/api/v4/projects/44311021/jobs/artifacts/main/raw/output/collection/conventions/log--activity--tillage/schema.json?job=copy_schemas"
        }
    },
];

let example_array = [ ... mechanized_soil_noplanting_field, ... moreEntities ];

let tillageLogs = example_array.filter( d => /^log--activity/.test(d.type) );

tillageLogs.forEach( asset => {
    asset
        .relationships
        .convention = {
            data: [
                {
                    type: "taxonomy_term--convention",
                    id: conventionEntityID
                }
            ]
        };
} );

let preparedExample = new builder.ArrayDataOrganizer({exampleArray: example_array});

await preparedExample.initialize();

/*

  errors: [
    {
      instancePath: '',
      schemaPath: '#/required',
      keyword: 'required',
      params: [Object],
      message: "must have required property 'log--activity--tillage'"
    },
    {
      instancePath: '',
      schemaPath: '#/required',
      keyword: 'required',
      params: [Object],
      message: "must have required property 'asset--plant--planting'"
    }
  ],
*/
let tillageLogRejectedCandidate = preparedExample.instances[0].unrecognizedEntities.filter( e => e.entity.type == 'log--activity' )[0];

tillageLogRejectedCandidate.entity.id = randomUUID();

tillageLogRejectedCandidate.entity.attributes.timestamp = new Date( 1000 * parseFloat( tillageLogRejectedCandidate.entity.attributes.timestamp ) ).toISOString();

preparedExample.instances = [];
await preparedExample.structureInstances();

/*
 errors: [
    {
      instancePath: '',
      schemaPath: '#/required',
      keyword: 'required',
      params: [Object],
      message: "must have required property 'asset--plant--planting'"
    },
    {
      instancePath: '/log--activity--tillage/relationships',
      schemaPath: '#/properties/log--activity--tillage/properties/relationships/additionalProperties',
      keyword: 'additionalProperties',
      params: [Object],
      message: 'must NOT have additional properties'
    },
    {
      instancePath: '/log--activity--tillage/relationships/asset/data',
      schemaPath: '#/properties/log--activity--tillage/properties/relationships/properties/asset/properties/data/contains',
      keyword: 'contains',
      params: [Object],
      message: 'must contain at least 1 valid item(s)'
    }
  ],
*/
