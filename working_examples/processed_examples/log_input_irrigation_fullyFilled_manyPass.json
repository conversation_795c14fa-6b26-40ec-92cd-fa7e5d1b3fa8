{"id": "bcb30904-b156-4230-a6ba-86b6e1c61374", "type": "object", "quantity--material--total_water": {"type": "quantity--material", "id": "402ee58b-72e8-4466-a797-6f599060e2d2", "attributes": {"value": {"decimal": 5}, "label": "irrigation"}, "relationships": {"units": {"data": [{"type": "taxonomy_term--unit", "name": "in", "id": "b0e287f4-6ddb-4e2c-b4c4-8ac09423aafe"}]}, "material_type": {"data": [{"type": "taxonomy_term--material_type", "name": "water", "id": "fafa8f10-5713-4e62-be3f-710a0adab62b"}]}}}, "log--input--irrigation": {"type": "log--input", "attributes": {"name": "irrigation surface 5 in", "status": "done", "notes": {"value": "watered bok choy with the river", "format": "default"}, "source": "surface", "timestamp": "2024-05-22T00:00:00.000Z"}, "relationships": {"asset": {"data": [{"type": "asset--plant", "id": "78b37a0b-8de2-43e2-a61e-25f7a2f85590", "name": "78b37a0b-8de2-43e2-a61e-25f7a2f85590"}]}, "category": {"data": [{"type": "taxonomy_term--log_category", "name": "irrigation", "id": "4d7229f9-b13b-4498-91ce-b0f918d463b8"}]}, "location": {"data": [{"type": "asset--land", "id": "f6cde1b6-42d2-421a-8a08-fcb63a632e59", "name": "f6cde1b6-42d2-421a-8a08-fcb63a632e59"}]}, "quantity": {"data": [{"type": "quantity--standard", "id": "2083692b-42df-4175-a4cf-46394fde870e", "name": "2083692b-42df-4175-a4cf-46394fde870e"}, {"type": "quantity--material", "id": "402ee58b-72e8-4466-a797-6f599060e2d2", "name": "402ee58b-72e8-4466-a797-6f599060e2d2"}, {"type": "quantity--standard", "id": "4cd84fe8-4e5e-482d-9026-ccc6d7496f0c", "name": "4cd84fe8-4e5e-482d-9026-ccc6d7496f0c"}]}, "convention": {"data": [{"type": "taxonomy_term--convention", "id": "2dc1e947-fe9b-46a9-a454-519b4a223cb3", "carrier_entity": "3d70b5e6-6428-47cc-96ee-dbb6936b129f", "convention_entity": {"type": "taxonomy_term--convention", "id": "2dc1e947-fe9b-46a9-a454-519b4a223cb3", "attributes": {"name": "Irrigation", "external_uri": "https://gitlab.com/api/v4/projects/44311021/jobs/artifacts/main/raw/output/collection/conventions/log--input--irrigation/schema.json?job=copy_schemas"}}}]}}, "id": "3d70b5e6-6428-47cc-96ee-dbb6936b129f"}, "asset--plant--planting": {"type": "asset--plant", "id": "78b37a0b-8de2-43e2-a61e-25f7a2f85590", "attributes": {"name": "78b37a0b-8de2-43e2-a61e-25f7a2f85590", "status": "active"}}, "taxonomy_term--unit--%": {"type": "taxonomy_term--unit", "id": "24959d06-3a72-4660-b612-6b7aa9f412e3", "attributes": {"name": "%"}}, "convention_taxonomy": {"type": "taxonomy_term--convention", "id": "2dc1e947-fe9b-46a9-a454-519b4a223cb3", "attributes": {"name": "Irrigation", "external_uri": "https://gitlab.com/api/v4/projects/44311021/jobs/artifacts/main/raw/output/collection/conventions/log--input--irrigation/schema.json?job=copy_schemas"}}}