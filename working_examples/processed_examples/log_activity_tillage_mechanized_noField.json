{"id": "4f1282c6-500d-4ee5-bd6a-dce6ba60af96", "type": "object", "log--activity--tillage": {"type": "log--activity", "attributes": {"timestamp": "2024-06-03T00:00:00.000Z", "name": "tillage", "status": "done", "notes": {"value": "", "format": "default"}}, "relationships": {"asset": {"data": [{"type": "asset--plant", "id": "00744151-b96f-4a4a-aeaa-d8b5ce3bf134", "name": "00744151-b96f-4a4a-aeaa-d8b5ce3bf134"}]}, "category": {"data": [{"type": "taxonomy_term--log_category", "name": "tillage", "id": "07e2eb7e-ad11-4e85-a6c4-2f73cf2392e4"}]}, "quantity": {"data": [{"type": "quantity--standard", "id": "c856b2c1-c67b-4b8b-bada-8788358c1fac", "name": "c856b2c1-c67b-4b8b-bada-8788358c1fac"}]}, "convention": {"data": [{"type": "taxonomy_term--convention", "id": "8ae7c124-3565-4220-b283-a2180feb159c", "carrier_entity": "b061ec34-3d68-49f9-a649-8dfab05d8f22", "convention_entity": {"type": "taxonomy_term--convention", "id": "8ae7c124-3565-4220-b283-a2180feb159c", "attributes": {"name": "Tillage", "external_uri": "https://gitlab.com/api/v4/projects/44311021/jobs/artifacts/main/raw/output/collection/conventions/log--activity--tillage/schema.json?job=copy_schemas"}}}]}}, "id": "b061ec34-3d68-49f9-a649-8dfab05d8f22"}, "asset--plant--planting": {"type": "asset--plant", "id": "00744151-b96f-4a4a-aeaa-d8b5ce3bf134", "attributes": {"name": "00744151-b96f-4a4a-aeaa-d8b5ce3bf134", "status": "active"}}, "taxonomy_term--unit--%": {"type": "taxonomy_term--unit", "id": "f30e0ea6-4b9c-4882-92a7-3c6b41388ab5", "attributes": {"name": "%"}}, "convention_taxonomy": {"type": "taxonomy_term--convention", "id": "8ae7c124-3565-4220-b283-a2180feb159c", "attributes": {"name": "Tillage", "external_uri": "https://gitlab.com/api/v4/projects/44311021/jobs/artifacts/main/raw/output/collection/conventions/log--activity--tillage/schema.json?job=copy_schemas"}}}