{"id": "678af095-e4c9-48be-98a9-15f9c5d996fd", "type": "object", "asset--plant--planting": {"type": "asset--plant", "id": "7887daeb-e24e-41d7-a549-62eb87a5df6f", "attributes": {"name": "24-05-20 , sweet clover", "status": "active", "geometry": null, "notes": {"value": "", "format": "default"}}, "relationships": {"plant_type": {"data": [{"type": "taxonomy_term--plant_type", "name": "sweet_clover", "id": "04d26779-7677-4fcf-b3e0-6f7e138224a7"}]}, "season": {"data": [{"type": "taxonomy_term--season", "name": "2024", "id": "ffa79e04-ff8c-4313-84d7-e4f0ec38fb2b"}]}, "convention": {"data": [{"type": "taxonomy_term--convention", "id": "f2bf797f-c8a0-47e0-88ec-3f2fda8dfb43", "carrier_entity": "7887daeb-e24e-41d7-a549-62eb87a5df6f", "convention_entity": {"type": "taxonomy_term--convention", "id": "f2bf797f-c8a0-47e0-88ec-3f2fda8dfb43", "attributes": {"name": "Plant", "external_uri": "https://gitlab.com/api/v4/projects/44311021/jobs/artifacts/main/raw/output/collection/conventions/asset--plant--planting/schema.json?job=copy_schemas"}}}]}}}}