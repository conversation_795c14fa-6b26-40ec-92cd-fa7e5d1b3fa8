{"id": "120ab14f-d1be-4634-bfa5-d4c91cb5fb59", "type": "object", "quantity--standard--area_percentage": {"type": "quantity--standard", "id": "ca270eb6-fed3-4c7f-b9dd-82e55c8d8b79", "attributes": {"value": {"decimal": 100}, "label": "area"}, "relationships": {"units": {"data": [{"type": "taxonomy_term--unit", "name": "%", "id": "227e971d-c84b-44b3-b987-a35e127ef6f4"}]}}}, "log--input--fertilizer": {"type": "log--input", "attributes": {"name": "fertilizer worm castings", "status": "done", "notes": {"value": "", "format": "default"}, "method": "topdress", "source": "", "timestamp": "2024-06-03T00:00:00.000Z"}, "relationships": {"asset": {"data": [{"type": "asset--plant", "id": "78b37a0b-8de2-43e2-a61e-25f7a2f85590", "name": "78b37a0b-8de2-43e2-a61e-25f7a2f85590"}]}, "category": {"data": [{"type": "taxonomy_term--log_category", "name": "amendment", "id": "66ba6cd9-b2ab-4b3a-9e96-37351ddccfed"}]}, "quantity": {"data": [{"type": "quantity--standard", "id": "ca270eb6-fed3-4c7f-b9dd-82e55c8d8b79", "name": "ca270eb6-fed3-4c7f-b9dd-82e55c8d8b79"}, {"type": "quantity--material", "id": "22b379c6-e7fe-4a07-b619-1dae1366904a", "name": "22b379c6-e7fe-4a07-b619-1dae1366904a"}]}, "convention": {"data": [{"type": "taxonomy_term--convention", "id": "43592a47-5709-4833-a6f5-9346b00cab4e", "convention_entity": {"type": "taxonomy_term--convention", "id": "43592a47-5709-4833-a6f5-9346b00cab4e", "attributes": {"name": "Fertilizer", "external_uri": "https://gitlab.com/api/v4/projects/44311021/jobs/artifacts/main/raw/output/collection/conventions/log--input--fertilizer/schema.json?job=copy_schemas"}}}]}}, "id": "521da89f-7cf8-41a7-9c1b-66c4c9984704"}, "asset--plant--planting": {"type": "asset--plant", "id": "78b37a0b-8de2-43e2-a61e-25f7a2f85590", "attributes": {"name": "78b37a0b-8de2-43e2-a61e-25f7a2f85590", "status": "active"}}, "taxonomy_term--unit--%": {"type": "taxonomy_term--unit", "id": "227e971d-c84b-44b3-b987-a35e127ef6f4", "attributes": {"name": "%"}}, "taxonomy_term--material_type--fertilizer": {"type": "taxonomy_term--material_type", "id": "ef745e95-3180-4581-8b59-79733f1e77c0", "attributes": {"name": "worm_castings"}}, "convention_taxonomy": {"type": "taxonomy_term--convention", "id": "43592a47-5709-4833-a6f5-9346b00cab4e", "attributes": {"name": "Fertilizer", "external_uri": "https://gitlab.com/api/v4/projects/44311021/jobs/artifacts/main/raw/output/collection/conventions/log--input--fertilizer/schema.json?job=copy_schemas"}}}