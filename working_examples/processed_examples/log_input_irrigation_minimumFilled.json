{"id": "fb628f84-4cf5-4e35-a50b-21d08d499f10", "type": "object", "quantity--material--total_water": {"type": "quantity--material", "id": "a2d7bce6-ded1-4953-957f-84a74b82d9aa", "attributes": {"label": "irrigation"}, "relationships": {"material_type": {"data": [{"type": "taxonomy_term--material_type", "name": "water", "id": "8249da8b-17cc-4217-99b6-bec52457e06f"}]}}}, "log--input--irrigation": {"type": "log--input", "attributes": {"name": "irrigation", "status": "done", "notes": {"value": "", "format": "default"}, "source": "", "timestamp": "2024-06-04T00:00:00.000Z"}, "relationships": {"asset": {"data": [{"type": "asset--plant", "id": "0c3705d9-4d5b-46ae-ac79-f1223e2bf87c", "name": "0c3705d9-4d5b-46ae-ac79-f1223e2bf87c"}]}, "category": {"data": [{"type": "taxonomy_term--log_category", "name": "irrigation", "id": "0102706f-617c-4ab1-91cc-48265985e441"}]}, "location": {"data": [{"type": "asset--land", "id": "f6cde1b6-42d2-421a-8a08-fcb63a632e59", "name": "f6cde1b6-42d2-421a-8a08-fcb63a632e59"}]}, "quantity": {"data": [{"type": "quantity--standard", "id": "c8858820-e8c9-45a8-b564-b4a39a35ccf6", "name": "c8858820-e8c9-45a8-b564-b4a39a35ccf6"}, {"type": "quantity--material", "id": "a2d7bce6-ded1-4953-957f-84a74b82d9aa", "name": "a2d7bce6-ded1-4953-957f-84a74b82d9aa"}]}, "convention": {"data": [{"type": "taxonomy_term--convention", "id": "1076ca75-bc9e-4822-949d-722bb7de290d", "carrier_entity": "4ea825e0-bded-4d06-90af-505503a21b54", "convention_entity": {"type": "taxonomy_term--convention", "id": "1076ca75-bc9e-4822-949d-722bb7de290d", "attributes": {"name": "Irrigation", "external_uri": "https://gitlab.com/api/v4/projects/44311021/jobs/artifacts/main/raw/output/collection/conventions/log--input--irrigation/schema.json?job=copy_schemas"}}}]}}, "id": "4ea825e0-bded-4d06-90af-505503a21b54"}, "asset--plant--planting": {"type": "asset--plant", "id": "0c3705d9-4d5b-46ae-ac79-f1223e2bf87c", "attributes": {"name": "0c3705d9-4d5b-46ae-ac79-f1223e2bf87c", "status": "active"}}, "taxonomy_term--unit--%": {"type": "taxonomy_term--unit", "id": "1500885f-9ddf-4c37-a5bf-38addcb2a10a", "attributes": {"name": "%"}}, "convention_taxonomy": {"type": "taxonomy_term--convention", "id": "1076ca75-bc9e-4822-949d-722bb7de290d", "attributes": {"name": "Irrigation", "external_uri": "https://gitlab.com/api/v4/projects/44311021/jobs/artifacts/main/raw/output/collection/conventions/log--input--irrigation/schema.json?job=copy_schemas"}}}