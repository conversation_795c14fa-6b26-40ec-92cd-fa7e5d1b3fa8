{"id": "dbc93034-ebc7-45e7-b7e2-1f8ed804d315", "type": "object", "quantity--material--total_water": {"type": "quantity--material", "id": "7d9895e4-437e-466e-aef4-2ee98dbc7dd4", "attributes": {"value": {"decimal": 3}, "label": "irrigation"}, "relationships": {"units": {"data": [{"type": "taxonomy_term--unit", "name": "in", "id": "29863007-f04c-4c7e-9d78-620ad9640fd5"}]}, "material_type": {"data": [{"type": "taxonomy_term--material_type", "name": "water", "id": "ee6fc4ce-2fb4-4f83-937c-a92a82f18b81"}]}}}, "log--input--irrigation": {"type": "log--input", "attributes": {"name": "irrigation well 3 in", "status": "done", "notes": {"value": "watered jimbo's bell peppers", "format": "default"}, "source": "well", "timestamp": "2024-02-07T00:00:00.000Z"}, "relationships": {"asset": {"data": [{"type": "asset--plant", "id": "0c3705d9-4d5b-46ae-ac79-f1223e2bf87c", "name": "0c3705d9-4d5b-46ae-ac79-f1223e2bf87c"}]}, "category": {"data": [{"type": "taxonomy_term--log_category", "name": "irrigation", "id": "80651aa0-afa2-48a7-b9ea-d5553ae1d84c"}]}, "location": {"data": [{"type": "asset--land", "id": "1b7ed6dd-af01-4600-83f0-4eccf093da26", "name": "1b7ed6dd-af01-4600-83f0-4eccf093da26"}]}, "quantity": {"data": [{"type": "quantity--standard", "id": "b04319d9-584c-481c-974c-50f1938b981d", "name": "b04319d9-584c-481c-974c-50f1938b981d"}, {"type": "quantity--material", "id": "7d9895e4-437e-466e-aef4-2ee98dbc7dd4", "name": "7d9895e4-437e-466e-aef4-2ee98dbc7dd4"}, {"type": "quantity--standard", "id": "5b5b1169-45fb-483b-ad34-392ed6d1c185", "name": "5b5b1169-45fb-483b-ad34-392ed6d1c185"}]}, "convention": {"data": [{"type": "taxonomy_term--convention", "id": "2a3a8aeb-00bc-4a32-89c6-34f6502586f9", "carrier_entity": "3b496d17-0acd-48cc-807d-b4e37309a242", "convention_entity": {"type": "taxonomy_term--convention", "id": "2a3a8aeb-00bc-4a32-89c6-34f6502586f9", "attributes": {"name": "Irrigation", "external_uri": "https://gitlab.com/api/v4/projects/44311021/jobs/artifacts/main/raw/output/collection/conventions/log--input--irrigation/schema.json?job=copy_schemas"}}}]}}, "id": "3b496d17-0acd-48cc-807d-b4e37309a242"}, "asset--plant--planting": {"type": "asset--plant", "id": "0c3705d9-4d5b-46ae-ac79-f1223e2bf87c", "attributes": {"name": "0c3705d9-4d5b-46ae-ac79-f1223e2bf87c", "status": "active"}}, "taxonomy_term--unit--%": {"type": "taxonomy_term--unit", "id": "413cd441-cdfe-494b-a4e2-50f3e222e50c", "attributes": {"name": "%"}}, "convention_taxonomy": {"type": "taxonomy_term--convention", "id": "2a3a8aeb-00bc-4a32-89c6-34f6502586f9", "attributes": {"name": "Irrigation", "external_uri": "https://gitlab.com/api/v4/projects/44311021/jobs/artifacts/main/raw/output/collection/conventions/log--input--irrigation/schema.json?job=copy_schemas"}}}