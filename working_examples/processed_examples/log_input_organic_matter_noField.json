{"id": "6dd7f82d-97fc-46c1-b280-8e6c5c0fe0cc", "type": "object", "quantity--standard--area_percentage": {"type": "quantity--standard", "id": "17fed508-48d4-48ab-83f6-5fcc6b4e88c8", "attributes": {"value": {"decimal": 100}, "label": "area"}, "relationships": {"units": {"data": [{"type": "taxonomy_term--unit", "name": "%", "id": "52f89120-224c-4cc9-a37f-a7dc16ae453e"}]}}}, "quantity--material--rate": {"type": "quantity--material", "id": "0b657873-1ce6-4ac9-877c-369382a834c0", "attributes": {"label": "organic_matter_inputs"}, "relationships": {"material_type": {"data": [{"type": "taxonomy_term--material_type", "name": "manure_poultry_compost", "id": "8550d320-a476-4e17-bd65-ca89910c6464"}, {"type": "taxonomy_term--material_type", "name": "mushroom_compost", "id": "86679643-54ea-4e85-bde7-ebc2bfdf49cf"}]}}}, "log--input--organic_matter": {"type": "log--input", "attributes": {"timestamp": "2024-01-15T00:00:00.000Z", "name": "manure poultry compost, mushroom compost", "status": "done", "notes": {"value": "", "format": "default"}}, "relationships": {"asset": {"data": [{"type": "asset--plant", "id": "78b37a0b-8de2-43e2-a61e-25f7a2f85590", "name": "78b37a0b-8de2-43e2-a61e-25f7a2f85590"}]}, "category": {"data": [{"type": "taxonomy_term--log_category", "name": "amendment", "id": "c96b2c97-904a-4b22-8814-00f89a4fe221"}]}, "quantity": {"data": [{"type": "quantity--standard", "id": "17fed508-48d4-48ab-83f6-5fcc6b4e88c8", "name": "17fed508-48d4-48ab-83f6-5fcc6b4e88c8"}, {"type": "quantity--material", "id": "0b657873-1ce6-4ac9-877c-369382a834c0", "name": "0b657873-1ce6-4ac9-877c-369382a834c0"}]}, "convention": {"data": [{"type": "taxonomy_term--convention", "id": "e25ed890-a80f-4fd2-b9c0-f91c36d3d5e2", "carrier_entity": "99adada0-5da8-4315-ba9c-b616fa90cdeb", "convention_entity": {"type": "taxonomy_term--convention", "id": "e25ed890-a80f-4fd2-b9c0-f91c36d3d5e2", "attributes": {"name": "Organic Matter", "external_uri": "https://gitlab.com/api/v4/projects/44311021/jobs/artifacts/main/raw/output/collection/conventions/log--input--organic_matter/schema.json?job=copy_schemas"}}}]}}, "id": "99adada0-5da8-4315-ba9c-b616fa90cdeb"}, "asset--plant--planting": {"type": "asset--plant", "id": "78b37a0b-8de2-43e2-a61e-25f7a2f85590", "attributes": {"name": "78b37a0b-8de2-43e2-a61e-25f7a2f85590", "status": "active"}}, "taxonomy_term--unit--%": {"type": "taxonomy_term--unit", "id": "52f89120-224c-4cc9-a37f-a7dc16ae453e", "attributes": {"name": "%"}}, "taxonomy_term--material_type--organic_matter": {"type": "taxonomy_term--material_type", "id": "86679643-54ea-4e85-bde7-ebc2bfdf49cf", "attributes": {"name": "mushroom_compost"}}, "taxonomy_term--log_category--amendment": {"type": "taxonomy_term--log_category", "id": "c96b2c97-904a-4b22-8814-00f89a4fe221", "attributes": {"name": "amendment"}}, "convention_taxonomy": {"type": "taxonomy_term--convention", "id": "e25ed890-a80f-4fd2-b9c0-f91c36d3d5e2", "attributes": {"name": "Organic Matter", "external_uri": "https://gitlab.com/api/v4/projects/44311021/jobs/artifacts/main/raw/output/collection/conventions/log--input--organic_matter/schema.json?job=copy_schemas"}}}