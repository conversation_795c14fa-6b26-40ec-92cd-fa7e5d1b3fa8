{"id": "d9983c5d-cf12-42f6-9d0a-a47bd08a5967", "type": "object", "quantity--standard--area_percentage": {"type": "quantity--standard", "id": "34e6ad0e-74d0-47e7-a834-bbced97dad4e", "attributes": {"value": {"decimal": 100}, "label": "area"}, "relationships": {"units": {"data": [{"type": "taxonomy_term--unit", "name": "%", "id": "2f0c74e5-9551-43bb-9496-d884ae5a05b2"}]}}}, "log--activity--solarization": {"type": "log--activity", "attributes": {"name": "end solarization or tarping", "status": "done", "notes": {"value": "", "format": "default"}, "timestamp": "2024-02-08T00:00:00.000Z"}, "relationships": {"asset": {"data": [{"type": "asset--plant", "id": "00744151-b96f-4a4a-aeaa-d8b5ce3bf134", "name": "00744151-b96f-4a4a-aeaa-d8b5ce3bf134"}]}, "category": {"data": [{"type": "taxonomy_term--log_category", "name": "weed_control", "id": "10a5030d-3c8b-4a8b-80ca-439f8858789c"}, {"type": "taxonomy_term--log_category", "name": "seeding", "id": "5c1f307e-e48f-463c-b159-4b0b82da3666"}]}, "quantity": {"data": [{"type": "quantity--standard", "id": "34e6ad0e-74d0-47e7-a834-bbced97dad4e", "name": "34e6ad0e-74d0-47e7-a834-bbced97dad4e"}]}, "convention": {"data": [{"type": "taxonomy_term--convention", "id": "07381a1d-f6f9-412c-ad51-9d9fe322d4d6", "carrier_entity": "6747110b-e7f7-4c20-92e3-73c4b24679e7", "convention_entity": {"type": "taxonomy_term--convention", "id": "07381a1d-f6f9-412c-ad51-9d9fe322d4d6", "attributes": {"name": "Solarization", "external_uri": "https://gitlab.com/api/v4/projects/44311021/jobs/artifacts/main/raw/output/collection/conventions/log--activity--solarization/schema.json?job=copy_schemas"}}}]}}, "id": "6747110b-e7f7-4c20-92e3-73c4b24679e7"}, "plant_asset": {"type": "asset--plant", "id": "00744151-b96f-4a4a-aeaa-d8b5ce3bf134", "attributes": {"name": "00744151-b96f-4a4a-aeaa-d8b5ce3bf134", "status": "active"}}, "taxonomy_term--log_category": {"type": "taxonomy_term--log_category", "id": "5c1f307e-e48f-463c-b159-4b0b82da3666", "attributes": {"name": "seeding"}}, "convention_taxonomy": {"type": "taxonomy_term--convention", "id": "07381a1d-f6f9-412c-ad51-9d9fe322d4d6", "attributes": {"name": "Solarization", "external_uri": "https://gitlab.com/api/v4/projects/44311021/jobs/artifacts/main/raw/output/collection/conventions/log--activity--solarization/schema.json?job=copy_schemas"}}, "taxonomy_term--log_category--weed_control": {"type": "taxonomy_term--log_category", "id": "10a5030d-3c8b-4a8b-80ca-439f8858789c", "attributes": {"name": "weed_control"}}}