{"id": "ee200c59-db62-4d71-8587-70191861e231", "type": "object", "quantity--material--yield": {"type": "quantity--material", "id": "9d084865-089c-471c-b54b-434eea1a5a48", "attributes": {"value": {"decimal": 35}, "label": "yield"}, "relationships": {"units": {"data": [{"type": "taxonomy_term--unit", "name": "bunch", "id": "e4dfc4d5-4a5f-49a6-b13b-54495eb4a229"}]}, "material_type": {"data": [{"type": "taxonomy_term--material_type", "name": "bok_choy_bok_choi_pak_choy", "id": "34b8357e-7bdb-458b-bb13-b3461cc5295a"}]}}}, "log--harvest--harvest": {"type": "log--harvest", "attributes": {"timestamp": "2024-05-06T00:00:00.000Z", "name": "harvest bok choy bok choi pak choy 35 bunch", "status": "done", "is_termination": true, "notes": {"value": "pak choy here", "format": "default"}}, "relationships": {"asset": {"data": [{"type": "asset--plant", "id": "78b37a0b-8de2-43e2-a61e-25f7a2f85590", "name": "78b37a0b-8de2-43e2-a61e-25f7a2f85590"}]}, "category": {"data": [{"type": "taxonomy_term--log_category", "name": "termination", "id": "f3596f4a-aaf3-4232-8b4c-569a03d26e80"}, {"type": "taxonomy_term--log_category", "name": "harvest", "id": "04792c8b-fcbb-4f8a-bb55-20b6da4fe96b"}]}, "location": {"data": [{"type": "asset--land", "id": "0f8cc336-df06-4d07-9617-20ce97ce0401", "name": "0f8cc336-df06-4d07-9617-20ce97ce0401"}]}, "quantity": {"data": [{"type": "quantity--standard", "id": "f608eb33-4268-4a6f-9cb4-b344ec4862c9", "name": "f608eb33-4268-4a6f-9cb4-b344ec4862c9"}, {"type": "quantity--material", "id": "9d084865-089c-471c-b54b-434eea1a5a48", "name": "9d084865-089c-471c-b54b-434eea1a5a48"}, {"type": "quantity--standard", "id": "d4c94112-cc56-4708-87f4-41730f3199fe", "name": "d4c94112-cc56-4708-87f4-41730f3199fe"}]}, "convention": {"data": [{"type": "taxonomy_term--convention", "id": "02372bb6-e8e6-454a-98fc-cbbd540c4fd0", "carrier_entity": "5b347f53-9138-4bcf-af57-1031bb612fa8", "convention_entity": {"type": "taxonomy_term--convention", "id": "02372bb6-e8e6-454a-98fc-cbbd540c4fd0", "attributes": {"name": "Harvest", "external_uri": "https://gitlab.com/api/v4/projects/44311021/jobs/artifacts/main/raw/output/collection/conventions/log--harvest--harvest/schema.json?job=copy_schemas"}}}]}}, "id": "5b347f53-9138-4bcf-af57-1031bb612fa8"}, "asset--plant--planting": {"type": "asset--plant", "id": "78b37a0b-8de2-43e2-a61e-25f7a2f85590", "attributes": {"name": "78b37a0b-8de2-43e2-a61e-25f7a2f85590", "status": "active"}}, "taxonomy_term--unit--%": {"type": "taxonomy_term--unit", "id": "1ab5f33a-3261-44c7-a880-9a6d4e7bd0eb", "attributes": {"name": "%"}}, "taxonomy_term--unit--yield": {"type": "taxonomy_term--unit", "id": "e4dfc4d5-4a5f-49a6-b13b-54495eb4a229", "attributes": {"name": "bunch"}}, "taxonomy_term--log_category": {"type": "taxonomy_term--log_category", "id": "04792c8b-fcbb-4f8a-bb55-20b6da4fe96b", "attributes": {"name": "harvest"}}, "convention_taxonomy": {"type": "taxonomy_term--convention", "id": "02372bb6-e8e6-454a-98fc-cbbd540c4fd0", "attributes": {"name": "Harvest", "external_uri": "https://gitlab.com/api/v4/projects/44311021/jobs/artifacts/main/raw/output/collection/conventions/log--harvest--harvest/schema.json?job=copy_schemas"}}}