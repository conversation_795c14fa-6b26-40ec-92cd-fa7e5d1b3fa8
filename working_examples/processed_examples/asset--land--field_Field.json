{"id": "e80335c9-8a48-4a1b-9128-be61c1dae03a", "type": "object", "asset--land--create_field": {"type": "asset--land", "id": "9eb09e98-7531-433d-8323-5628f600c864", "attributes": {"name": "new_field_example", "status": "active", "intrinsic_geometry": {"value": "GEOMETRYCOLLECTION(POLYGON ((-58.10067061294254 2.1804178031026282, -59.286637392831054 -1.5860417183772455, -52.93822698283956 -0.9583348609234292, -52.41500634465345 1.2041770971739254, -56.42636457074697 2.7380036087330524, -58.10067061294254 2.1804178031026282)))"}, "land_type": "field", "is_location": true, "is_fixed": true, "flag": []}, "relationships": {"convention": {"data": [{"type": "taxonomy_term--convention", "id": "8c1e08cd-b448-44c5-be85-bbc87f9f560f", "carrier_entity": "9eb09e98-7531-433d-8323-5628f600c864", "convention_entity": {"type": "taxonomy_term--convention", "id": "8c1e08cd-b448-44c5-be85-bbc87f9f560f", "attributes": {"name": "Field", "external_uri": "https://gitlab.com/api/v4/projects/44311021/jobs/artifacts/main/raw/output/collection/conventions/asset--land--field/schema.json?job=copy_schemas"}}}]}}}, "convention_taxonomy": {"type": "taxonomy_term--convention", "id": "8c1e08cd-b448-44c5-be85-bbc87f9f560f", "attributes": {"name": "Field", "external_uri": "https://gitlab.com/api/v4/projects/44311021/jobs/artifacts/main/raw/output/collection/conventions/asset--land--field/schema.json?job=copy_schemas"}}}