{"id": "5e7b245c-f5a1-4c7c-8d1b-79f51ec5e10a", "type": "object", "quantity--standard--area_percentage": {"type": "quantity--standard", "id": "09b96a3e-85eb-41c5-9a09-771e80041c1b", "attributes": {"value": {"decimal": 100}, "label": "area"}, "relationships": {"units": {"data": [{"type": "taxonomy_term--unit", "name": "%", "id": "3e3b0401-936e-4da0-9109-cf0d61d478ec"}]}}}, "log--activity--flaming": {"type": "log--activity", "attributes": {"name": "flaming", "status": "done", "notes": {"value": "", "format": "default"}, "timestamp": "2024-04-22T00:00:00.000Z"}, "relationships": {"asset": {"data": [{"type": "asset--plant", "id": "2b567309-ea70-46b4-8a09-bac6e3a4ad3e", "name": "2b567309-ea70-46b4-8a09-bac6e3a4ad3e"}]}, "category": {"data": [{"type": "taxonomy_term--log_category", "name": "weed_control", "id": "a626d29d-6601-454d-9dd7-3075fda40968"}, {"type": "taxonomy_term--log_category", "name": "pest_disease_control", "id": "f38fdba7-c6ec-4a5e-bdf1-13d97c3e0741"}]}, "quantity": {"data": [{"type": "quantity--standard", "id": "09b96a3e-85eb-41c5-9a09-771e80041c1b", "name": "09b96a3e-85eb-41c5-9a09-771e80041c1b"}]}, "convention": {"data": [{"type": "taxonomy_term--convention", "id": "a76055cc-f5b6-4f85-8766-25173714df38", "carrier_entity": "546ded46-267c-44ef-9a4e-b1a82ccd6c3d", "convention_entity": {"type": "taxonomy_term--convention", "id": "a76055cc-f5b6-4f85-8766-25173714df38", "attributes": {"name": "Flaming", "external_uri": "https://gitlab.com/api/v4/projects/44311021/jobs/artifacts/main/raw/output/collection/conventions/log--activity--flaming/schema.json?job=copy_schemas"}}}]}}, "id": "546ded46-267c-44ef-9a4e-b1a82ccd6c3d"}, "asset--plant--planting": {"type": "asset--plant", "id": "2b567309-ea70-46b4-8a09-bac6e3a4ad3e", "attributes": {"name": "2b567309-ea70-46b4-8a09-bac6e3a4ad3e", "status": "active"}}, "taxonomy_term--log_category": {"type": "taxonomy_term--log_category", "id": "f38fdba7-c6ec-4a5e-bdf1-13d97c3e0741", "attributes": {"name": "pest_disease_control"}}, "convention_taxonomy": {"type": "taxonomy_term--convention", "id": "a76055cc-f5b6-4f85-8766-25173714df38", "attributes": {"name": "Flaming", "external_uri": "https://gitlab.com/api/v4/projects/44311021/jobs/artifacts/main/raw/output/collection/conventions/log--activity--flaming/schema.json?job=copy_schemas"}}, "taxonomy_term--log_category--weed_control": {"type": "taxonomy_term--log_category", "id": "a626d29d-6601-454d-9dd7-3075fda40968", "attributes": {"name": "weed_control"}}}