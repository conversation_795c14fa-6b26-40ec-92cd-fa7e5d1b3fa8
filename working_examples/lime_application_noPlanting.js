let {lime_app_noPlanting} = require(`${__dirname}/working_examples/fixed_working_examples/land_management_activities/surveyStackExamples`);
const builder = require("convention_builder");
const { randomUUID } = require('crypto');

//__dirname = path.resolve();
let conventionEntityID = randomUUID();

let moreEntities = [
    {
        type: "taxonomy_term--convention",
        id: conventionEntityID,
        attributes: {
            name: "<PERSON><PERSON>",
            external_uri: "https://gitlab.com/api/v4/projects/44311021/jobs/artifacts/main/raw/output/collection/conventions/log--input--lime/schema.json?job=copy_schemas"
        }
    },
];

let example_array = [ ... lime_app_noPlanting, ... moreEntities ];

let logInputs = example_array.filter( d => /^log--input/.test(d.type) );
logInputs.forEach( log => {
    log
        .relationships
        .convention = {
            data: [
                {
                    type: "taxonomy_term--convention",
                    id: conventionEntityID
                }
            ]
        };
} );

let preparedExample = new builder.ArrayDataOrganizer({exampleArray: example_array});

await preparedExample.initialize();

/*
 errors: [
    {
      instancePath: '',
      schemaPath: '#/required',
      keyword: 'required',
      params: [Object],
      message: "must have required property 'log--input--lime'"
    },
    {
      instancePath: '',
      schemaPath: '#/required',
      keyword: 'required',
      params: [Object],
      message: "must have required property 'taxonomy_term--material_type--lime'"
    },
    {
      instancePath: '',
      schemaPath: '#/required',
      keyword: 'required',
      params: [Object],
      message: "must have required property 'asset--plant--planting'"
    }
  ],
*/

let limeInputsRejectedCandidate = preparedExample.instances[0].unrecognizedEntities.filter( e => e.entity.type == "log--input" )[0];
limeInputsRejectedCandidate.entity.id = randomUUID();

limeInputsRejectedCandidate.entity.attributes.timestamp = new Date( 1000 * parseFloat( limeInputsRejectedCandidate.entity.attributes.timestamp ) ).toISOString();

preparedExample.instances = [];
await preparedExample.structureInstances();

/* 
[
  {
    instancePath: '',
    schemaPath: '#/required',
    keyword: 'required',
    params: { missingProperty: 'taxonomy_term--material_type--lime' },
    message: "must have required property 'taxonomy_term--material_type--lime'"
  },
  {
    instancePath: '',
    schemaPath: '#/required',
    keyword: 'required',
    params: { missingProperty: 'asset--plant--planting' },
    message: "must have required property 'asset--plant--planting'"
  },
  {
    instancePath: '/log--input--lime/relationships',
    schemaPath: '#/properties/log--input--lime/properties/relationships/required',
    keyword: 'required',
    params: { missingProperty: 'asset' },
    message: "must have required property 'asset'"
  },
  {
    instancePath: '/log--input--lime/relationships',
    schemaPath: '#/properties/log--input--lime/properties/relationships/additionalProperties',
    keyword: 'additionalProperties',
    params: { additionalProperty: 'convention' },
    message: 'must NOT have additional properties'
  }
]
*/