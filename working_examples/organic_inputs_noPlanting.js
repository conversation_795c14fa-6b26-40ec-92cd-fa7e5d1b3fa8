let {organic_matter_inputs_noPlanting} = require("./fixed_working_examples/land_management_activities/surveyStackExamples");
const builder = require("convention_builder");
const { randomUUID } = require('crypto');

let conventionEntityID = randomUUID();

let moreEntities = [
    {
        type: "taxonomy_term--convention",
        id: conventionEntityID,
        attributes: {
            name: "log--input--organic_matter",
            external_uri: "https://gitlab.com/api/v4/projects/44311021/jobs/artifacts/main/raw/output/collection/conventions/log--input--organic_matter/schema.json?job=copy_schemas"
        }
    },
];

let example_array = [ ... organic_matter_inputs_noPlanting, ... moreEntities ];

let logInputs = example_array.filter( d => /^log--input/.test(d.type) );
logInputs.forEach( log => {
    log
        .relationships
        .convention = {
            data: [
                {
                    type: "taxonomy_term--convention",
                    id: conventionEntityID
                }
            ]
        };
} );

let preparedExample = new builder.ArrayDataOrganizer({exampleArray: example_array});

await preparedExample.initialize();

// no ambiguous entities 
// this example did not have a planting, so that's probably the deal with this error
/*
  errors: [
    {
      instancePath: '',
      schemaPath: '#/required',
      keyword: 'required',
      params: [Object],
      message: "must have required property 'log--input--organic_matter'"
    },
    {
      instancePath: '',
      schemaPath: '#/required',
      keyword: 'required',
      params: [Object],
      message: "must have required property 'asset--plant--planting'"
    }
  ],
*/

let organicInputsInputsRejectedCandidate = preparedExample.instances[0].unrecognizedEntities.filter( e => e.entity.type == "log--input" )[0];

organicInputsInputsRejectedCandidate.entity.id = randomUUID();

organicInputsInputsRejectedCandidate.entity.attributes.timestamp = new Date( 1000 * parseFloat( organicInputsInputsRejectedCandidate.entity.attributes.timestamp ) ).toISOString();

preparedExample.instances = [];
await preparedExample.structureInstances();
/*
[
  {
    instancePath: '',
    schemaPath: '#/required',
    keyword: 'required',
    params: { missingProperty: 'asset--plant--planting' },
    message: "must have required property 'asset--plant--planting'"
  },
  {
    instancePath: '/log--input--organic_matter/relationships',
    schemaPath: '#/properties/log--input--organic_matter/properties/relationships/additionalProperties',
    keyword: 'additionalProperties',
    params: { additionalProperty: 'convention' },
    message: 'must NOT have additional properties'
  },
  {
    instancePath: '/log--input--organic_matter/relationships/asset/data',
    schemaPath: '#/properties/log--input--organic_matter/properties/relationships/properties/asset/properties/data/contains',
    keyword: 'contains',
    params: { minContains: 1 },
    message: 'must contain at least 1 valid item(s)'
  }
]*/