let {npk_fertilizer_noPlanting} = require("./fixed_working_examples/land_management_activities/surveyStackExamples");
const builder = require("convention_builder");
const { randomUUID } = require('crypto');

let conventionEntityID = randomUUID();

let moreEntities = [
    {
        type: "taxonomy_term--convention",
        id: conventionEntityID,
        attributes: {
            name: "log--input--npk_fertilizer",
            external_uri: "https://gitlab.com/api/v4/projects/44311021/jobs/artifacts/main/raw/output/collection/conventions/log--input--npk_fertilizer/schema.json?job=copy_schemas"
        }
    },
];

let example_array = [ ... npk_fertilizer_noPlanting, ... moreEntities ];

let logInputs = example_array.filter( d => /^log--input/.test(d.type) );
logInputs.forEach( log => {
    log
        .relationships
        .convention = {
            data: [
                {
                    type: "taxonomy_term--convention",
                    id: conventionEntityID
                }
            ]
        };
} );

let preparedExample = new builder.ArrayDataOrganizer({exampleArray: example_array});

await preparedExample.initialize();

/*
[
  {
    instancePath: '',
    schemaPath: '#/required',
    keyword: 'required',
    params: { missingProperty: 'log--input--npk_fertilizer' },
    message: "must have required property 'log--input--npk_fertilizer'"
  },
  {
    instancePath: '',
    schemaPath: '#/required',
    keyword: 'required',
    params: { missingProperty: 'asset--plant--planting' },
    message: "must have required property 'asset--plant--planting'"
  }
]
*/

let npkFertilizerRejectedCandidate = preparedExample.instances[0].unrecognizedEntities.filter( e => e.entity.type == "log--input" )[0];

npkFertilizerRejectedCandidate.entity.id = randomUUID();

npkFertilizerRejectedCandidate.entity.attributes.timestamp = new Date( 1000 * parseFloat( npkFertilizerRejectedCandidate.entity.attributes.timestamp ) ).toISOString();

preparedExample.instances = [];
await preparedExample.structureInstances();

/*
 errors: [
    {
      instancePath: '',
      schemaPath: '#/required',
      keyword: 'required',
      params: [Object],
      message: "must have required property 'asset--plant--planting'"
    },
    {
      instancePath: '/log--input--npk_fertilizer/relationships',
      schemaPath: '#/properties/log--input--npk_fertilizer/properties/relationships/additionalProperties',
      keyword: 'additionalProperties',
      params: [Object],
      message: 'must NOT have additional properties'
    },
    {
      instancePath: '/log--input--npk_fertilizer/relationships/asset/data',
      schemaPath: '#/properties/log--input--npk_fertilizer/properties/relationships/properties/asset/properties/data/contains',
      keyword: 'contains',
      params: [Object],
      message: 'must contain at least 1 valid item(s)'
    }
*/