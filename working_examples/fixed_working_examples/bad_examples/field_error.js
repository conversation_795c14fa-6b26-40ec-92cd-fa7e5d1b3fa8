const builder = require('convention_builder');
const { randomUUID } = require('crypto');


// First, we are generating a bunch of data for the examples 

// These were created with surveystack and thus were created with surveystack
// Find a better solution for this but
// Field visuals because it's shape based 

/* 
Valid patterns for field are a single polygon, a single multipolygon or geometry collection containing one polygon or one multipolygon
Invalid patterns are point, multipoint, or geometry collection of point/multipoint, multiple polygons, multiple multipolygon or a geometry collection containing multiple poloygon/multipolygon
*/


// Good Examples that should be passing and are considered valid 

// create_field_1 https://www.dropbox.com/scl/fi/ksel2geiuc63g2fhss9pu/land_shape_1.PNG?rlkey=g7i7ooj1c0inolno52iea12ts&st=c0e0z7pr&dl=0 
// 1 polygon in South America
// This should be considered a valid example
let create_field_1 = [{"type":"asset--land","id":"7cf141aa-2c46-4e69-8e20-7528a2e5ff0b","attributes":{"convention":["asset--land--field"],"name":"new_field_77","status":"active","intrinsic_geometry":{"value":"GEOMETRYCOLLECTION(POLYGON ((-66.0457592436238 4.08656813785818, -69.33829418054958 4.168668000037172, -71.2315017692819 2.5252653017128353, -71.3961285161282 -0.7664288107477262, -67.85665345893298 -0.9310374392216971, -64.15255165489147 0.6328628104183593, -66.0457592436238 4.08656813785818)))"},"land_type":"field","is_location":true,"is_fixed":true,"flag":[]}}]
let geometry_1 = "GEOMETRYCOLLECTION(POLYGON ((-66.0457592436238 4.08656813785818, -69.33829418054958 4.168668000037172, -71.2315017692819 2.5252653017128353, -71.3961285161282 -0.7664288107477262, -67.85665345893298 -0.9310374392216971, -64.15255165489147 0.6328628104183593, -66.0457592436238 4.08656813785818)))";

// 1 polygon with a hole in the middle https://www.dropbox.com/scl/fi/cxxzbednlnwd6lizrreac/land_shape_6.PNG?rlkey=st7wgen0ahv87tf031lxf1um0&st=ho221pz1&dl=0 
// this should be considered a valid example 
let create_field_6 = [{"type":"asset--land","id":"2ee160cc-8a85-41ed-828f-a8d559980136","attributes":{"convention":["asset--land--field"],"name":"land_shape_5","status":"active","intrinsic_geometry":{"value":"MULTIPOLYGON (((20 35, 45 20, 30 5, 10 10, 10 30, 20 35), (30 20, 20 25, 20 15, 30 20)))"},"land_type":"field","is_location":true,"is_fixed":true,"flag":[]}}]
//let geometry_2 = "MULTIPOLYGON (((20 35, 45 20, 30 5, 10 10, 10 30, 20 35), (30 20, 20 25, 20 15, 30 20)))";
// This one is currently not successful (should be passing, is failing)

let geometry_9 = "POLYGON ((30 10, 40 40, 20 40, 10 20, 30 10))";
let geometry_10 = "MULTIPOLYGON (((30 20, 45 40, 10 40, 30 20)))";


// Bad Examples that are failing and have errors and are considered invalid

// create_field_2 https://www.dropbox.com/scl/fi/m5ug4uvmj6kn87xftrcb4/land_shape_2.PNG?rlkey=it1jnbx4nk33p3ovd439lihlf&st=d6vnrtt8&dl=0
// 2 detached polygons in North America
// This should be considered an invalid example
let create_field_2 = [{"type":"asset--land","id":"c77e01a2-f714-4f79-b092-556d5599d0d2","attributes":{"convention":["asset--land--field"],"name":"new_field_2","status":"active","intrinsic_geometry":{"value":"GEOMETRYCOLLECTION(POLYGON ((-83.44069158685268 30.85568282083193, -83.51629229949708 30.68029202084179, -83.44069158685268 30.615251425621153, -83.12316859374624 30.660784436137106, -82.9190466696064 30.810241833413713, -82.63176396155771 31.076089730470642, -82.73004488799542 31.199036384145614, -82.91148659834195 31.289526572858904, -83.14584880753956 31.101986478798537, -83.44069158685268 30.85568282083193)),POLYGON ((-82.14035932936919 31.392837667890106, -82.32180103971572 31.186102138952208, -82.102558973047 30.972432209820965, -81.78503597994055 31.101986478798537, -81.70943526729617 31.354109302603774, -82.14035932936919 31.392837667890106)))"},"land_type":"field","is_location":true,"is_fixed":true,"flag":["transitionalorganic"]}}]
let geometry_3 = "GEOMETRYCOLLECTION(POLYGON ((-83.44069158685268 30.85568282083193, -83.51629229949708 30.68029202084179, -83.44069158685268 30.615251425621153, -83.12316859374624 30.660784436137106, -82.9190466696064 30.810241833413713, -82.63176396155771 31.076089730470642, -82.73004488799542 31.199036384145614, -82.91148659834195 31.289526572858904, -83.14584880753956 31.101986478798537, -83.44069158685268 30.85568282083193)),POLYGON ((-82.14035932936919 31.392837667890106, -82.32180103971572 31.186102138952208, -82.102558973047 30.972432209820965, -81.78503597994055 31.101986478798537, -81.70943526729617 31.354109302603774, -82.14035932936919 31.392837667890106)))";

// create_field_3 https://www.dropbox.com/scl/fi/jdrjkbsbb5thl3cf2tp6t/land_shape_3.PNG?rlkey=8gt6tyk5blzbisfpw1n4yrz5i&st=zjbg9of1&dl=0
// 1 horizontal line segment in Africa
// this should be considered an invalid example
let create_field_3 = [{"type":"asset--land","id":"88328b88-cb96-4af9-828f-1dc460b3a930","attributes":{"convention":["asset--land--field"],"name":"field_3","status":"active","intrinsic_geometry":{"value":"GEOMETRYCOLLECTION(POLYGON ((13.072614607466218 1.9290057741197728, 27.18800012028184 1.6850572687754521, 13.072614607466218 1.9290057741197728, 14.783407066398365 1.899440856424718, 13.072614607466218 1.9290057741197728)))"},"land_type":"field","is_location":true,"is_fixed":true,"flag":["hydroponic"]}}]
//let geometry_4 = "GEOMETRYCOLLECTION(POLYGON ((13.072614607466218 1.9290057741197728, 27.18800012028184 1.6850572687754521, 13.072614607466218 1.9290057741197728, 14.783407066398365 1.899440856424718, 13.072614607466218 1.9290057741197728)))";
// not successful (should be failing, is passing)

// create_field_4 https://www.dropbox.com/scl/fi/26dxqtqhbo8wfa413jarb/land_shape_4.PNG?rlkey=lex3tz3jqnyqtdalw1dqgqxmx&st=flz3cvlh&dl=0 
// 2 connected line segments in Europe
// this should be considered an invalid example
let create_field_4 = [{"type":"asset--land","id":"8a8560cc-2ee1-41ed-8944-a8da18c80136","attributes":{"convention":["asset--land--field"],"name":"land_shape_4","status":"active","intrinsic_geometry":{"value":"GEOMETRYCOLLECTION(POLYGON ((1.2168113010133947 47.61513934865914, 1.1666588431328555 47.574050247279644, 1.0563676124813766 47.48357691183409, 0.4180611613190548 46.9568988202644, 1.5200998419142078 47.862931462142484, 0.10694352167101029 48.17926815539968, 1.5200998419142078 47.862931462142484, 1.5200998419142078 47.862931462142484, 1.4354211023680246 47.799423486711305, 1.2168113010133947 47.61513934865914)))"},"land_type":"field","is_location":true,"is_fixed":true,"flag":["greenhouse"]}}]
//let geometry_5 = "GEOMETRYCOLLECTION(POLYGON ((1.2168113010133947 47.61513934865914, 1.1666588431328555 47.574050247279644, 1.0563676124813766 47.48357691183409, 0.4180611613190548 46.9568988202644, 1.5200998419142078 47.862931462142484, 0.10694352167101029 48.17926815539968, 1.5200998419142078 47.862931462142484, 1.5200998419142078 47.862931462142484, 1.4354211023680246 47.799423486711305, 1.2168113010133947 47.61513934865914)))";
// not successful (should be failing, is passing)

// these are examples not created with surveystack that are still invalid

// create_field_5 https://www.dropbox.com/scl/fi/9azuh7g4s2lyo5wwwii8c/land_shape_5.PNG?rlkey=ww58w382zho97a42l7lvddesh&st=avq1j7h1&dl=0 
// 1 polygon with a hole in the middle, 1 separate triangle 
let create_field_5 = [{"type":"asset--land","id":"2ee160cc-8a85-41ed-828f-a8d559980136","attributes":{"convention":["asset--land--field"],"name":"land_shape_5","status":"active","intrinsic_geometry":{"value":"MULTIPOLYGON (((40 40, 20 45, 45 30, 40 40)), ((20 35, 45 20, 30 5, 10 10, 10 30, 20 35), (30 20, 20 25, 20 15, 30 20)))"},"land_type":"field","is_location":true,"is_fixed":true,"flag":[]}}]
let geometry_6 = "MULTIPOLYGON (((40 40, 20 45, 45 30, 40 40)), ((20 35, 45 20, 30 5, 10 10, 10 30, 20 35), (30 20, 20 25, 20 15, 30 20)))";


let geometry_7 =  "MULTIPOINT ((-73.935242 40.730610), (-74.935242 41.730610))";
let geometry_8 = "POINT (-73.935242 40.730610)";


let good_geometry = [geometry_1, geometry_9, geometry_10 ];

let bad_geometry = [geometry_3, geometry_6, geometry_7, geometry_8 ];

// Building examples for testing 

// ** don't forget this ** 
//__dirname = path.resolve();

let directory_name = path.join(__dirname, '..', '..', '..', 'output', 'collection', 'conventions', 'asset--land--field', 'object.json');
let base_schema_dir_name = path.join(__dirname, '..', '..', '..', 'input', 'collection');

let intendedConvention = JSON.parse( fs.readFileSync(directory_name) );
intendedConvention.gitlabProjectId = 'undefined';

let convention = new builder.ConventionSchema( intendedConvention );

for (var i = 0; i < good_geometry.length; i++) {

    let conventionID = randomUUID();
    let assetID = randomUUID();

    let createFieldExam = [
        {
            type: 'asset--land',
            id: assetID,
            attributes: {
                name: 'new_field_example',
                status: 'active',
                geometry: good_geometry[i],
                land_type: 'field',
                is_location: true,
                is_fixed: true,
                flag: []
            },
            relationships: { 
                convention: {
                    data: [{
                        type: 'taxonomy_term--convention',
                        id: conventionID,
                        attributes: {
                            name: 'Field',
                            external_uri: 'https://gitlab.com/api/v4/projects/44311021/jobs/artifacts/main/raw/output/collection/conventions/asset--land--field/schema.json?job=copy_schemas'
                        }
                    }]
                }
            }
        },
        {
            type: 'taxonomy_term--convention',
            id: conventionID,
            attributes: {
                name: 'Field',
                external_uri: 'https://gitlab.com/api/v4/projects/44311021/jobs/artifacts/main/raw/output/collection/conventions/asset--land--field/schema.json?job=copy_schemas'
            }
        }
    ];


    let new_example = new builder.ArrayDataOrganizer({
        exampleArray: createFieldExam,
        predefinedConventions: [ JSON.parse( fs.readFileSync(directory_name) ) ], 
        baseSchemataFolder: base_schema_dir_name
    });
    
    await new_example.initialize();
    let assembledData = new_example.instances[new_example.instances.length - 1].assembled_entity;

    convention.addExample({ object: assembledData, is_valid_example: true });

}


for (var i = 0; i < bad_geometry.length; i++) {

    let conventionID = randomUUID();
    let assetID = randomUUID();

    let createFieldExam = [
        {
            type: 'asset--land',
            id: assetID,
            attributes: {
                name: 'new_field_example',
                status: 'active',
                geometry: bad_geometry[i],
                land_type: 'field',
                is_location: true,
                is_fixed: true,
                flag: []
            },
            relationships: { 
                convention: {
                    data: [{
                        type: 'taxonomy_term--convention',
                        id: conventionID,
                        attributes: {
                            name: 'Field',
                            external_uri: 'https://gitlab.com/api/v4/projects/44311021/jobs/artifacts/main/raw/output/collection/conventions/asset--land--field/schema.json?job=copy_schemas'
                        }
                    }]
                }
            }
        },
        {
            type: 'taxonomy_term--convention',
            id: conventionID,
            attributes: {
                name: 'Field',
                external_uri: 'https://gitlab.com/api/v4/projects/44311021/jobs/artifacts/main/raw/output/collection/conventions/asset--land--field/schema.json?job=copy_schemas'
            }
        }
    ];


    let new_example = new builder.ArrayDataOrganizer({
        exampleArray: createFieldExam,
        predefinedConventions: [ JSON.parse( fs.readFileSync(directory_name) ) ], 
        baseSchemataFolder: base_schema_dir_name
    });
    
    await new_example.initialize();
    let assembledData = new_example.instances[new_example.instances.length - 1].assembled_entity;

    convention.addExample({ object: assembledData, is_valid_example: false });

}


let test = convention.testExamples();
test.sucess; 
// true

