let {harvest_or_termination__details_yesPlanting_noField} = require("./surveyStackExamples");
const builder = require("convention_builder");
const { randomUUID } = require('crypto');

// Use this for the Node REPL if necessary based on your setup
//__dirname = path.resolve();

// Add the convention relationship
let conventionEntityID = randomUUID();

let moreEntities = [
    {
        type: "taxonomy_term--convention",
        id: conventionEntityID,
        attributes: {
            name: "Harvest",
            external_uri: "https://gitlab.com/api/v4/projects/44311021/jobs/artifacts/main/raw/output/collection/conventions/log--harvest--harvest/schema.json?job=copy_schemas"
        }
    },
];

let example_array = [ ... harvest_or_termination__details_yesPlanting_noField, ... moreEntities ];

let logHarvests = example_array.filter( d => /^log--harvest/.test(d.type) );

logHarvests.forEach( log => {
    log
        .relationships
        .convention = {
            data: [
                {
                    type: "taxonomy_term--convention",
                    id: conventionEntityID
                }
            ]
        };
} );

let directory_name = path.join(__dirname, '..', '..', '..', 'output', 'collection', 'conventions', 'log--harvest--harvest', 'object.json');
let base_schema_dir_name = path.join(__dirname, '..', '..', '..', 'input', 'collection');

let preparedExample = new builder.ArrayDataOrganizer(
    {
        exampleArray: example_array,
        predefinedConventions: [ JSON.parse( fs.readFileSync(directory_name) ) ],
        baseSchemataFolder: base_schema_dir_name
    });

await preparedExample.initialize();

let lastInstance = preparedExample.instances[preparedExample.instances.length - 1];

lastInstance.errors;

/*
[
  {
    instancePath: '',
    schemaPath: '#/required',
    keyword: 'required',
    params: { missingProperty: 'log--harvest--harvest' },
    message: "must have required property 'log--harvest--harvest'"
  }
]
*/

lastInstance.ambiguousEntities.filter( d => d.possibleOverlays.includes('log--harvest--harvest') );
// [], empty array no log--harvest entities in ambiguous entities, so the log--harvest must be unrecognized  

lastInstance.unrecognizedEntities.find( d => d.entity.attributes.name.includes('harvest') ).allTests.find( d => d.overlay.includes('log--harvest--harvest') ).errors;
/*
{
  overlay: 'log--harvest--harvest',
  errors: [
    {
      instancePath: '',
      schemaPath: '#/required',
      keyword: 'required',
      params: [Object],
      message: "must have required property 'id'"
    },
    {
      instancePath: '/attributes/timestamp',
      schemaPath: '#/properties/attributes/properties/timestamp/format',
      keyword: 'format',
      params: [Object],
      message: 'must match format "date-time"'
    }
  ]
}
*/

// Let's tackle these one by one
// First, let's get the entity, then add an ID

let terminationRejectedCandidate = lastInstance.unrecognizedEntities.find( e => e.entity.type == "log--harvest" );

terminationRejectedCandidate.entity.id = randomUUID();

// Next, let's change the date time format

terminationRejectedCandidate.entity.attributes.timestamp = new Date( 1000 * parseFloat( terminationRejectedCandidate.entity.attributes.timestamp ) ).toISOString();

// Let's check where we are at
preparedExample.instances = [];
await preparedExample.initialize();

lastInstance = preparedExample.instances[preparedExample.instances.length - 1];

lastInstance.is_valid;
// true 

// is_valid preparation
let intendedConvention = JSON.parse( fs.readFileSync(directory_name) );
intendedConvention.gitlabProjectId = 'undefined';

let convention = new builder.ConventionSchema( intendedConvention );

let assembledData = lastInstance.assembled_entity;


// add our example, assembledData, into the object.
convention.addExample({ object: assembledData, is_valid_example: true });

// test all examples with the testExamples method.
let test = convention.testExamples();
test.success;
// true

// Store your object
let file_directory_name = path.join(__dirname, '..', '..', '..', 'working_examples', 'processed_examples', 'log_harvest_harvest_noField.json');

fs.writeFileSync( file_directory_name, JSON.stringify(assembledData) );  