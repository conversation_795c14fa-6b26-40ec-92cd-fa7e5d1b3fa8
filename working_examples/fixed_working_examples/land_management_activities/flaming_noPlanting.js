let {flaming_example_YesField_NoPlanting} = require(`./surveyStackExamples`);
const builder = require("convention_builder");
const { randomUUID } = require('crypto');

// Use this for the Node REPL if necessary based on your setup
//__dirname = path.resolve();

// Add the convention relationship
let conventionEntityID = randomUUID();

let moreEntities = [
    {
        type: "taxonomy_term--convention",
        id: conventionEntityID,
        attributes: {
            name: "Flaming",
            external_uri: "https://gitlab.com/api/v4/projects/44311021/jobs/artifacts/main/raw/output/collection/conventions/log--activity--flaming/schema.json?job=copy_schemas"
        }
    },
];

let example_array = [ ... flaming_example_YesField_NoPlanting, ... moreEntities ];

let logActivities = example_array.filter( d => /^log--activity/.test(d.type) );
logActivities.forEach( log => {
    log
        .relationships
        .convention = {
            data: [
                {
                    type: "taxonomy_term--convention",
                    id: conventionEntityID
                }
            ]
        };
} );

let preparedExample = new builder.ArrayDataOrganizer({exampleArray: example_array});

await preparedExample.initialize();


// is_valid is false,
preparedExample.instances;
/* 
[
  {
    identifiedEntities: [ [Object], [Object], [Object] ],
    unrecognizedEntities: [ [Object], [Object] ],
    ambiguousEntities: [ [Object] ],
    errors: [ [Object], [Object] ],
    **is_valid: false,**
    assembled_entity: {
      id: 'ad50c529-03ea-4c7c-a7d7-1e44b0fb8005',
      type: 'object',
      'quantity--standard--area_percentage': [Object],
      'taxonomy_term--unit--%': [Object],
      convention_taxonomy: [Object]
    }
  }
]
*/

// let's look at the errors 
let lastInstance = preparedExample.instances[preparedExample.instances.length - 1];

lastInstance.errors;
/*
[
  // ID and timestamp problem here 
  {
    instancePath: '',
    schemaPath: '#/required',
    keyword: 'required',
    params: { missingProperty: 'log--activity--flaming' },
    message: "must have required property 'log--activity--flaming'"
  },

// both taxonomy_term--log_category and taxonomy_term--log_category--weed_control pass with no errors and is thus ambiguous
  {
    instancePath: '',
    schemaPath: '#/required',
    keyword: 'required',
    params: { missingProperty: 'taxonomy_term--log_category--weed_control' },
    message: "must have required property 'taxonomy_term--log_category--weed_control'"
  }
]
*/

let flamingRejectedCandidate = lastInstance.unrecognizedEntities[0];

flamingRejectedCandidate.entity.id = randomUUID();

flamingRejectedCandidate.entity.attributes.timestamp = new Date( 1000 * parseFloat( flamingRejectedCandidate.entity.attributes.timestamp ) ).toISOString();

preparedExample.instances = [];
await preparedExample.structureInstances();

// The taxonomy_term is an ambiguous entity
preparedExample.instances[0].errors;
/* 
[
  {
    instancePath: '',
    schemaPath: '#/required',
    keyword: 'required',
    params: { missingProperty: 'taxonomy_term--log_category--weed_control' },
    message: "must have required property 'taxonomy_term--log_category--weed_control'"
  }
]
*/

// Now let's fix the ambiguous entity by telling it which one we want here 
let directory_name = path.join(__dirname, '..', '..', '..', 'output', 'collection', 'conventions', 'log--activity--flaming', 'object.json');
let base_schema_dir_name = path.join(__dirname, '..', '..', '..', 'input', 'collection');

let intendedConvention = JSON.parse( fs.readFileSync(directory_name) );
intendedConvention.gitlabProjectId = 'undefined';

let preppedExample = new builder.ArrayDataOrganizer(
    {
        exampleArray: example_array,
        predefinedConventions: [ JSON.parse( fs.readFileSync(directory_name) ) ],
        baseSchemataFolder: base_schema_dir_name
    });

await preppedExample.initialize();

let assembledData = preppedExample.instances[0].assembled_entity;
assembledData['taxonomy_term--log_category--weed_control'] = preppedExample.instances[0].ambiguousEntities[0].entity;

let convention = new builder.ConventionSchema( intendedConvention );

// add our example, assembledData, into the object.
convention.addExample({ object: assembledData, is_valid_example: true });

// test all examples with the testExamples method.
let test = convention.testExamples();

// It self perceives as 'fine'.
test.success;

let file_directory_name = path.join(__dirname, '..', '..', '..', 'working_examples', 'processed_examples', 'log_activity_flaming_noPlanting.json');
// Store your object
fs.writeFileSync( file_directory_name, JSON.stringify(assembledData) );  

