let {pesticide_herbicide_fungicide_example_yesPlanting_noField} = require("./surveyStackExamples");
const builder = require("convention_builder");
const { randomUUID } = require('crypto');

//__dirname = path.resolve();
let conventionEntityID = randomUUID();

let moreEntities = [
    {
        type: "taxonomy_term--convention",
        id: conventionEntityID,
        attributes: {
            name: "Herbicide/pesticide",
            external_uri: "https://gitlab.com/api/v4/projects/44311021/jobs/artifacts/main/raw/output/collection/conventions/log--input--herbicide_or_pesticide/schema.json?job=copy_schemas"
        }
    },
];

let example_array = [ ... pesticide_herbicide_fungicide_example_yesPlanting_noField, ... moreEntities ];

let logInputs = example_array.filter( d => /^log--input/.test(d.type) );

logInputs.forEach( log => {
    log
        .relationships
        .convention = {
            data: [
                {
                    type: "taxonomy_term--convention",
                    id: conventionEntityID
                }
            ]
        };
} );

// Let's feed our data into the importer
let preparedExample = new builder.ArrayDataOrganizer({exampleArray: example_array});

// initialize our objects so they retrieve the conventions from the net and perform their operations.
await preparedExample.initialize();

preparedExample.instances[0].errors.length;
//4

// This is using the convention locally instead of grabbing it from the internet 
let directory_name = path.join(__dirname, '..', '..', '..','output', 'collection', 'conventions', 'log--input--herbicide_or_pesticide', 'object.json');
let base_schema_dir_name = path.join(__dirname, '..', '..', '..','input', 'collection');

let intendedConvention = JSON.parse( fs.readFileSync(directory_name) );
intendedConvention.gitlabProjectId = 'undefined';

let preppedExample = new builder.ArrayDataOrganizer(
    {
        exampleArray: example_array,
        predefinedConventions: [ JSON.parse( fs.readFileSync(directory_name) ) ],
        baseSchemataFolder: base_schema_dir_name
    });

await preppedExample.initialize();

// The label should be constant. The place to inform what's been applied should be another entity.
let quantityMaterials = example_array.filter( d => d.type == "quantity--material" );
// Fixing labels for tt quantities.
quantityMaterials.forEach( d => d.attributes.label = "application_rate" );

let fungicideLogRejectedCandidates = preppedExample.instances[0].unrecognizedEntities.filter( e => e.entity.type == "log--input" );

let fungicideLogCandidate = fungicideLogRejectedCandidates[0];

let fungicideFailedTest = fungicideLogCandidate.allTests.find( t => t.overlay ==  'log--input--herbicide_or_pesticide');

fungicideFailedTest;
/*
{
  overlay: 'log--input--herbicide_or_pesticide',
  errors: [
    {
      instancePath: '',
      schemaPath: '#/required',
      keyword: 'required',
      params: [Object],
      message: "must have required property 'id'"
    },
    {
      instancePath: '/attributes/timestamp',
      schemaPath: '#/properties/attributes/properties/timestamp/type',
      keyword: 'type',
      params: [Object],
      message: 'must be string'
    }
  ]
}
*/


fungicideLogCandidate.entity.id = randomUUID();

fungicideLogCandidate.entity.attributes.timestamp = new Date( 1000 * parseFloat( fungicideLogCandidate.entity.attributes.timestamp ) ).toISOString();

preppedExample.instances = [];
await preppedExample.structureInstances();

preppedExample.instances[0].errors;
/*
[
  {
    instancePath: '',
    schemaPath: '#/required',
    keyword: 'required',
    params: {
      missingProperty: 'taxonomy_term--log_category--pest_disease_control'
    },
    message: "must have required property 'taxonomy_term--log_category--pest_disease_control'"
  }
]
*/

// Set the taxonomy_term to be the more precise one
// 'taxonomy_term--log_category', 'taxonomy_term--log_category--pest_disease_control'
let assembledData = preppedExample.instances[0].assembled_entity;
assembledData['taxonomy_term--log_category--pest_disease_control'] = preppedExample.instances[0].ambiguousEntities.find( d => d.possibleOverlays.includes('taxonomy_term--log_category--pest_disease_control') ).entity;

let convention = new builder.ConventionSchema( intendedConvention );

// add our example, assembledData, into the object.
// convention.validExamples = [];
convention.addExample({ object: assembledData, is_valid_example: true });

// test all examples with the testExamples method.
let test = convention.testExamples();
test.success;
// true

let file_directory_name = path.join(__dirname, '..', '..', '..', 'working_examples', 'processed_examples', 'log_input_herbicide_or_pesticide_noField.json');
fs.writeFileSync( file_directory_name, JSON.stringify(assembledData) );  