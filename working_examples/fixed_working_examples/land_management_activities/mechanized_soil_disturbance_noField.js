let {mechanized_soil_planting_nofield} = require("./surveyStackExamples");
const builder = require("convention_builder");
const { randomUUID } = require('crypto');

// Use this for the Node REPL if necessary based on your setup
//__dirname = path.resolve();

// Add the convention relationship
let conventionEntityID = randomUUID();

let moreEntities = [
    {
        type: "taxonomy_term--convention",
        id: conventionEntityID,
        attributes: {
            name: "Tillage",
            external_uri: "https://gitlab.com/api/v4/projects/44311021/jobs/artifacts/main/raw/output/collection/conventions/log--activity--tillage/schema.json?job=copy_schemas"
        }
    },
];

let example_array = [ ... mechanized_soil_planting_nofield, ... moreEntities ];

let tillageLogs = example_array.filter( d => /^log--activity/.test(d.type) );

tillageLogs.forEach( asset => {
    asset
        .relationships
        .convention = {
            data: [
                {
                    type: "taxonomy_term--convention",
                    id: conventionEntityID
                }
            ]
        };
} );

let directory_name = path.join(__dirname, '..', '..', '..', 'output', 'collection', 'conventions', 'log--activity--tillage', 'object.json');
let base_schema_dir_name = path.join(__dirname, '..', '..', '..', 'input', 'collection');

let preparedExample = new builder.ArrayDataOrganizer(
    {
        exampleArray: example_array,
        predefinedConventions: [ JSON.parse( fs.readFileSync(directory_name) ) ],
        baseSchemataFolder: base_schema_dir_name
    });

await preparedExample.initialize();

let lastInstance = preparedExample.instances[preparedExample.instances.length - 1];

lastInstance.errors;
/*
 errors: [
    {
      instancePath: '',
      schemaPath: '#/required',
      keyword: 'required',
      params: [Object],
      message: "must have required property 'log--activity--tillage'"
    }
  ],
*/

lastInstance.unrecognizedEntities.filter( d => d.entity.type.includes('log--activity') ).filter( d => d.entity.attributes.name.includes('tillage') )[0].allTests.find(d => d.overlay.includes('log--activity--tillage')).errors;
/*
{
  overlay: 'log--activity--tillage',
  errors: [
    {
      instancePath: '',
      schemaPath: '#/required',
      keyword: 'required',
      params: [Object],
      message: "must have required property 'id'"
    },
    {
      instancePath: '/attributes/timestamp',
      schemaPath: '#/properties/attributes/properties/timestamp/format',
      keyword: 'format',
      params: [Object],
      message: 'must match format "date-time"'
    }
  ]
}
*/

let tillageRejectedCandidate = lastInstance.unrecognizedEntities.filter( d => d.entity.type.includes('log--activity') ).find( d => d.entity.attributes.name.includes('tillage') );

tillageRejectedCandidate.entity.id = randomUUID();

tillageRejectedCandidate.entity.attributes.timestamp = new Date( 1000 * parseFloat( tillageRejectedCandidate.entity.attributes.timestamp ) ).toISOString();

preparedExample.instances = [];
await preparedExample.structureInstances();

lastInstance = preparedExample.instances[preparedExample.instances.length - 1];

lastInstance.is_valid;
// true

//is_valid prep!
let intendedConvention = JSON.parse( fs.readFileSync(directory_name) );
intendedConvention.gitlabProjectId = 'undefined';


let convention = new builder.ConventionSchema( intendedConvention );

let assembledData = lastInstance.assembled_entity;

// add our example, assembledData, into the object.
convention.addExample({ object: assembledData, is_valid_example: true });

// test all examples with the testExamples method.
let test = convention.testExamples();
test.success;
// true

// Store your object
let file_directory_name = path.join(__dirname, '..', '..', '..', 'working_examples', 'processed_examples', 'log_activity_tillage_mechanized_noField.json');
fs.writeFileSync( file_directory_name, JSON.stringify(assembledData) );  