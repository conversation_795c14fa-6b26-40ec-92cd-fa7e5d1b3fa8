let {flaming_example_filledFull} = require(`./surveyStackExamples`);
const builder = require("convention_builder");
const { randomUUID } = require('crypto');

// Use this for the Node REPL if necessary based on your setup
//__dirname = path.resolve();

// Add the convention relationship
let conventionEntityID = randomUUID();

let moreEntities = [
    {
        type: "taxonomy_term--convention",
        id: conventionEntityID,
        attributes: {
            name: "Flaming",
            external_uri: "https://gitlab.com/api/v4/projects/44311021/jobs/artifacts/main/raw/output/collection/conventions/log--activity--flaming/schema.json?job=copy_schemas"
        }
    },
];

let example_array = [ ... flaming_example_filledFull, ... moreEntities ];

let logActivities = example_array.filter( d => /^log--activity/.test(d.type) );

logActivities.forEach( log => {
    log
        .relationships
        .convention = {
            data: [
                {
                    type: "taxonomy_term--convention",
                    id: conventionEntityID
                }
            ]
        };
} );

let directory_name = path.join(__dirname, '..', '..', '..', 'output', 'collection', 'conventions', 'log--activity--flaming', 'object.json');
let base_schema_dir_name = path.join(__dirname, '..', '..', '..', 'input', 'collection');

let preparedExample = new builder.ArrayDataOrganizer(
    {
        exampleArray: example_array,
        predefinedConventions: [ JSON.parse( fs.readFileSync(directory_name) ) ],
        baseSchemataFolder: base_schema_dir_name
    });

await preparedExample.initialize();

// These are the errors, let's tackle log--activity first
let lastInstance = preparedExample.instances[preparedExample.instances.length - 1];

lastInstance.errors;
/*
  errors: [
    {
      instancePath: '',
      schemaPath: '#/required',
      keyword: 'required',
      params: [Object],
      message: "must have required property 'log--activity--flaming'"
    },
    {
      instancePath: '',
      schemaPath: '#/required',
      keyword: 'required',
      params: [Object],
      message: "must have required property 'taxonomy_term--log_category--weed_control'"
    }
  ],
*/

// it appears to be missing ID and timestamp issues
lastInstance.unrecognizedEntities.find( d => d.entity.attributes.name.includes('flaming') ).allTests.find( d => d.overlay.includes('log--activity--flaming') ).errors;

/*
[
  {
    instancePath: '',
    schemaPath: '#/required',
    keyword: 'required',
    params: { missingProperty: 'id' },
    message: "must have required property 'id'"
  },
  {
    instancePath: '/attributes/timestamp',
    schemaPath: '#/properties/attributes/properties/timestamp/type',
    keyword: 'type',
    params: { type: 'string' },
    message: 'must be string'
  }
]
*/

let rejectedCandidates = lastInstance.unrecognizedEntities.filter( e => e.entity.type == "log--activity" );

// double check each are actually flaming 
let flameRejectedCandidates = rejectedCandidates.filter( d => /^flaming/.test(d.entity.attributes.name) );

// There's going to be a few of these because of the multiple events, so we'll have to do this in a loop
// This is because it's a many pass example (and thus has multiple of these logs)
flameRejectedCandidates.forEach( log => {
    log.entity.id = randomUUID();
    log.entity.attributes.timestamp = new Date( 1000 * parseFloat( log.entity.attributes.timestamp ) ).toISOString();
} );

preparedExample.instances = [];
await preparedExample.structureInstances();

lastInstance = preparedExample.instances[preparedExample.instances.length - 1];

lastInstance.errors;

/*
 errors: [
    {
      instancePath: '',
      schemaPath: '#/required',
      keyword: 'required',
      params: [Object],
      message: "must have required property 'taxonomy_term--log_category--weed_control'"
    },
*/

// This is an ambiguous entity, we will force it to be taxonomy_term--log_category--weed_control, as that is more precise
lastInstance.ambiguousEntities;
/*
[
  {
    passedTests: [ [Object], [Object] ],
    possibleOverlays: [
      'taxonomy_term--log_category',
      'taxonomy_term--log_category--weed_control'
    ],
    allTests: [ [Object], [Object], [Object], [Object], [Object], [Object] ],
    amountPassed: 2,
    entity: {
      type: 'taxonomy_term--log_category',
      id: '8da52e96-8df8-4a6f-93bf-9c8378ba42ce',
      attributes: [Object]
    }
  }
]
*/

let assembledData = lastInstance.assembled_entity;

assembledData['taxonomy_term--log_category--weed_control'] = lastInstance.ambiguousEntities.find( d => d.possibleOverlays.includes('taxonomy_term--log_category--weed_control') ).entity;

let intendedConvention = JSON.parse( fs.readFileSync(directory_name) );
intendedConvention.gitlabProjectId = 'undefined';

let convention = new builder.ConventionSchema( intendedConvention );

// add our example, assembledData, into the object.
convention.addExample({ object: assembledData, is_valid_example: true });

// test all examples with the testExamples method.
let test = convention.testExamples();
test.success;
// true

let file_directory_name = path.join(__dirname, '..', '..', '..', 'working_examples', 'processed_examples', 'log_activity_flaming_fullyFilled_manyPass.json');

// Store your object
fs.writeFileSync( file_directory_name, JSON.stringify(assembledData) );  