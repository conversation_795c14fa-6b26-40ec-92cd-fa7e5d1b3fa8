let {flaming_details_minFilled} = require(`./surveyStackExamples`);
const builder = require("convention_builder");
const { randomUUID } = require('crypto');

// Use this for the Node REPL if necessary based on your setup
//__dirname = path.resolve();

// Add the convention relationship
let conventionEntityID = randomUUID();

let moreEntities = [
    {
        type: "taxonomy_term--convention",
        id: conventionEntityID,
        attributes: {
            name: "Flaming",
            external_uri: "https://gitlab.com/api/v4/projects/44311021/jobs/artifacts/main/raw/output/collection/conventions/log--activity--flaming/schema.json?job=copy_schemas"
        }
    },
];

let example_array = [ ... flaming_details_minFilled, ... moreEntities ];

let logActivities = example_array.filter( d => /^log--activity/.test(d.type) );

logActivities.forEach( log => {
    log
        .relationships
        .convention = {
            data: [
                {
                    type: "taxonomy_term--convention",
                    id: conventionEntityID
                }
            ]
        };
} );

let directory_name = path.join(__dirname, '..', '..', '..', 'output', 'collection', 'conventions', 'log--activity--flaming', 'object.json');
let base_schema_dir_name = path.join(__dirname, '..', '..', '..', 'input', 'collection');

let preparedExample = new builder.ArrayDataOrganizer(
    {
        exampleArray: example_array,
        predefinedConventions: [ JSON.parse( fs.readFileSync(directory_name) ) ],
        baseSchemataFolder: base_schema_dir_name
    });

await preparedExample.initialize();

let lastInstance = preparedExample.instances[preparedExample.instances.length - 1];

lastInstance.errors;
/*
[
  {
    instancePath: '',
    schemaPath: '#/required',
    keyword: 'required',
    params: { missingProperty: 'log--activity--flaming' },
    message: "must have required property 'log--activity--flaming'"
  },
  {
    instancePath: '',
    schemaPath: '#/required',
    keyword: 'required',
    params: { missingProperty: 'taxonomy_term--log_category--weed_control' },
    message: "must have required property 'taxonomy_term--log_category--weed_control'"
  }
]
*/

lastInstance.unrecognizedEntities.find( d => d.entity.attributes.name.includes('flaming') ).allTests.find( d => d.overlay.includes('log--activity--flaming') ).errors;
/*
{
  overlay: 'log--activity--flaming',
  errors: [
    {
      instancePath: '',
      schemaPath: '#/required',
      keyword: 'required',
      params: [Object],
      message: "must have required property 'id'"
    },
    {
      instancePath: '/attributes/timestamp',
      schemaPath: '#/properties/attributes/properties/timestamp/type',
      keyword: 'type',
      params: [Object],
      message: 'must be string'
    }
  ]
}
*/

let rejectedCandidates = lastInstance.unrecognizedEntities.filter( e => e.entity.type == "log--activity" );

let flameRejectedCandidate = rejectedCandidates.find( d => /^flaming/.test(d.entity.attributes.name) );

flameRejectedCandidate.entity.id = randomUUID();

flameRejectedCandidate.entity.attributes.timestamp = new Date( 1000 * parseFloat( flameRejectedCandidate.entity.attributes.timestamp ) ).toISOString();

preparedExample.instances = [];
await preparedExample.structureInstances();

lastInstance = preparedExample.instances[preparedExample.instances.length - 1];

lastInstance.errors;
/*
[
  {
    instancePath: '',
    schemaPath: '#/required',
    keyword: 'required',
    params: { missingProperty: 'taxonomy_term--log_category--weed_control' },
    message: "must have required property 'taxonomy_term--log_category--weed_control'"
  }
]
*/

lastInstance.ambiguousEntities.find( d => d.possibleOverlays.includes('taxonomy_term--log_category--weed_control') );
/*
{
  passedTests: [
    { overlay: 'taxonomy_term--log_category', errors: [] },
    {
      overlay: 'taxonomy_term--log_category--weed_control',
      errors: []
    }
  ],
  possibleOverlays: [
    'taxonomy_term--log_category',
    'taxonomy_term--log_category--weed_control'
  ],
  allTests: [
    { overlay: 'log--activity--flaming', errors: [Array] },
    { overlay: 'convention_taxonomy', errors: [Array] },
    { overlay: 'taxonomy_term--log_category', errors: [] },
    {
      overlay: 'taxonomy_term--log_category--weed_control',
      errors: []
    },
    { overlay: 'quantity--standard--area_percentage', errors: [Array] },
    { overlay: 'taxonomy_term--unit--%', errors: [Array] },
    { overlay: 'asset--plant--planting', errors: [Array] }
  ],
  amountPassed: 2,
  entity: {
    type: 'taxonomy_term--log_category',
    id: '344042cb-a667-414d-91bc-699cc878dfe1',
    attributes: { name: 'weed_control' }
  }
}
*/

let assembledData = lastInstance.assembled_entity;
assembledData['taxonomy_term--log_category--weed_control'] = lastInstance.ambiguousEntities.find( d => d.possibleOverlays.includes('taxonomy_term--log_category--weed_control') ).entity;

let intendedConvention = JSON.parse( fs.readFileSync(directory_name) );
intendedConvention.gitlabProjectId = 'undefined';

let convention = new builder.ConventionSchema( intendedConvention );

// add our example, assembledData, into the object.
convention.addExample({ object: assembledData, is_valid_example: true });

// test all examples with the testExamples method.
let test = convention.testExamples();
test.success;
// true

let file_directory_name = path.join(__dirname, '..', '..', '..', 'working_examples', 'processed_examples', 'log_activity_flaming_minFilled.json');
// Store your object
fs.writeFileSync( file_directory_name, JSON.stringify(assembledData) );  