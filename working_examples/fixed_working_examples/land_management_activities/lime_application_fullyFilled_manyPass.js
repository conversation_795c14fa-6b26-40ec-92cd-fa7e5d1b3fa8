let {lime_app_full_details_manyPass} = require("./surveyStackExamples");
const builder = require("convention_builder");
const { randomUUID } = require('crypto');

//__dirname = path.resolve();
let conventionEntityID = randomUUID();

let moreEntities = [
    {
        type: "taxonomy_term--convention",
        id: conventionEntityID,
        attributes: {
            name: "<PERSON>e",
            external_uri: "https://gitlab.com/api/v4/projects/44311021/jobs/artifacts/main/raw/output/collection/conventions/log--input--lime/schema.json?job=copy_schemas"
        }
    },
];

//Lime is missing a taxonomy term
// This is an error that will appear if this term isn't added once initialized 
/*
  {
    instancePath: '',
    schemaPath: '#/required',
    keyword: 'required',
    params: { missingProperty: 'taxonomy_term--material_type--lime' },
    message: "must have required property 'taxonomy_term--material_type--lime'"
  }
*/

// Here, we add the missing taxonomy term
let missingTaxID = randomUUID();

let missingTaxEntity = [
  {
      type: "taxonomy_term--material_type",
      id: missingTaxID,
      attributes: {
          name: "lime",
      }
  },
];

let example_array = [ ... lime_app_full_details_manyPass, ... missingTaxEntity, ... moreEntities ];

let logInputs = example_array.filter( d => /^log--input/.test(d.type) );
logInputs.forEach( log => {
    log
        .relationships
        .convention = {
            data: [
                {
                    type: "taxonomy_term--convention",
                    id: conventionEntityID
                }
            ]
        };
} );

// This is using the convention locally instead of grabbing it from the internet 
let directory_name = path.join(__dirname, '..', '..', '..', 'output', 'collection', 'conventions', 'log--input--lime', 'object.json');
let base_schema_dir_name = path.join(__dirname, '..', '..', '..', 'input', 'collection');

let preparedExample = new builder.ArrayDataOrganizer(
    {
        exampleArray: example_array,
        predefinedConventions: [ JSON.parse( fs.readFileSync(directory_name) ) ],
        baseSchemataFolder: base_schema_dir_name
    });

await preparedExample.initialize();

let lastInstance = preparedExample.instances[preparedExample.instances.length - 1];

lastInstance.errors;
/*
 errors: [
    {
      instancePath: '',
      schemaPath: '#/required',
      keyword: 'required',
      params: [Object],
      message: "must have required property 'log--input--lime'"
    },
],
*/


lastInstance.unrecognizedEntities.filter( d => d.entity.type.includes('log--input') ).filter( d => d.entity.attributes.name.includes('lime') )[0].allTests.find(d => d.overlay.includes('log--input--lime')).errors;
/*
[
  {
    instancePath: '',
    schemaPath: '#/required',
    keyword: 'required',
    params: { missingProperty: 'id' },
    message: "must have required property 'id'"
  },
  {
    instancePath: '/attributes/timestamp',
    schemaPath: '#/properties/attributes/properties/timestamp/format',
    keyword: 'format',
    params: { format: 'date-time' },
    message: 'must match format "date-time"'
  }
]
*/

let limeInputsRejectedCandidates = lastInstance.unrecognizedEntities.filter( d => d.entity.type.includes('log--input') ).filter( d => d.entity.attributes.name.includes('lime') );

// There's going to be a few of these because of the multiple events, so we'll have to do this in a loop?
limeInputsRejectedCandidates.forEach( log => {
    log.entity.id = randomUUID();
    log.entity.attributes.timestamp = new Date( 1000 * parseFloat( log.entity.attributes.timestamp ) ).toISOString();
} );

preparedExample.instances = [];
await preparedExample.structureInstances();

lastInstance = preparedExample.instances[preparedExample.instances.length - 1];

lastInstance.is_valid;
//true

let intendedConvention = JSON.parse( fs.readFileSync(directory_name) );
intendedConvention.gitlabProjectId = 'undefined';

let assembledData = lastInstance.assembled_entity;

let convention = new builder.ConventionSchema( intendedConvention );

// add our example, assembledData, into the object.
convention.addExample({ object: assembledData, is_valid_example: true });

// test all examples with the testExamples method.
let test = convention.testExamples();
test.success;

// Store your object
let file_directory_name = path.join(__dirname, '..', '..', '..', 'working_examples', 'processed_examples', 'log_input_lime_fullyFilled_manyPass.json');
fs.writeFileSync( file_directory_name, JSON.stringify(assembledData) );  