let {flaming_example_NoField_YesPlanting} = require(`./surveyStackExamples`);
const builder = require("convention_builder");
const { randomUUID } = require('crypto');

// Use this for the Node REPL if necessary based on your setup
//__dirname = path.resolve();

// Add the convention relationship
let conventionEntityID = randomUUID();

let moreEntities = [
    {
        type: "taxonomy_term--convention",
        id: conventionEntityID,
        attributes: {
            name: "Flaming",
            external_uri: "https://gitlab.com/api/v4/projects/44311021/jobs/artifacts/main/raw/output/collection/conventions/log--activity--flaming/schema.json?job=copy_schemas"
        }
    },
];

let example_array = [ ... flaming_example_NoField_YesPlanting, ... moreEntities ];

let logActivities = example_array.filter( d => /^log--activity/.test(d.type) );

logActivities.forEach( log => {
    log
        .relationships
        .convention = {
            data: [
                {
                    type: "taxonomy_term--convention",
                    id: conventionEntityID
                }
            ]
        };
} );

let directory_name = path.join(__dirname, '..', '..', '..', 'output', 'collection', 'conventions', 'log--activity--flaming', 'object.json');
let base_schema_dir_name = path.join(__dirname, '..', '..', '..', 'input', 'collection');

let preparedExample = new builder.ArrayDataOrganizer(
    {
        exampleArray: example_array,
        predefinedConventions: [ JSON.parse( fs.readFileSync(directory_name) ) ],
        baseSchemataFolder: base_schema_dir_name
    });

await preparedExample.initialize();

let lastInstance = preparedExample.instances[preparedExample.instances.length - 1];

lastInstance.errors;
/*
[
  {
    instancePath: '',
    schemaPath: '#/required',
    keyword: 'required',
    params: { missingProperty: 'log--activity--flaming' },
    message: "must have required property 'log--activity--flaming'"
  },
  {
    instancePath: '',
    schemaPath: '#/required',
    keyword: 'required',
    params: { missingProperty: 'taxonomy_term--log_category--weed_control' },
    message: "must have required property 'taxonomy_term--log_category--weed_control'"
  }
]
*/

lastInstance.unrecognizedEntities.filter( d => d.entity.type.includes('log--activity') ).filter( d => d.entity.attributes.name.includes('flaming'))[0].allTests.find( d => d.overlay.includes('log--activity--flaming') );
/*
{
  overlay: 'log--activity--flaming',
  errors: [
    {
      instancePath: '',
      schemaPath: '#/required',
      keyword: 'required',
      params: [Object],
      message: "must have required property 'id'"
    },
    {
      instancePath: '/attributes/timestamp',
      schemaPath: '#/properties/attributes/properties/timestamp/type',
      keyword: 'type',
      params: [Object],
      message: 'must be string'
    }
  ]
}
*/

let rejectedCandidates = lastInstance.unrecognizedEntities.filter( e => e.entity.type == "log--activity" );

let flameRejectedCandidate = rejectedCandidates.find( d => /^flaming/.test(d.entity.attributes.name) );

flameRejectedCandidate.entity.id = randomUUID();

flameRejectedCandidate.entity.attributes.timestamp = new Date( 1000 * parseFloat( flameRejectedCandidate.entity.attributes.timestamp ) ).toISOString();

preparedExample.instances = [];
await preparedExample.structureInstances();

lastInstance = preparedExample.instances[preparedExample.instances.length - 1];

lastInstance.errors;

// It is missing this taxonomy term,
/*
[
  {
    instancePath: '',
    schemaPath: '#/required',
    keyword: 'required',
    params: { missingProperty: 'taxonomy_term--log_category--weed_control' },
    message: "must have required property 'taxonomy_term--log_category--weed_control'"
  }
]
*/

// ambiguous entities is empty, so it must be in unrecognized entities 
lastInstance.ambiguousEntities;
//[]

// It has a problem with the additional property of status, let's just delete it 
lastInstance.unrecognizedEntities.filter( d => d.entity.type.includes('taxonomy_term--log_category') ).find( d => d.entity.attributes.name.includes('weed_control')).allTests.find( d => d.overlay.includes('taxonomy_term--log_category--weed_control') ).errors;

/*
[
  {
    instancePath: '/attributes',
    schemaPath: '#/properties/attributes/additionalProperties',
    keyword: 'additionalProperties',
    params: { additionalProperty: 'status' },
    message: 'must NOT have additional properties'
  }
]
*/

example_array.filter( d => d.type == "taxonomy_term--log_category" ).forEach( obj => { delete obj.attributes.status} );

preparedExample.instances = [];
await preparedExample.structureInstances();

// The entity still needs some help
lastInstance = preparedExample.instances[preparedExample.instances.length - 1];

lastInstance.errors;
/*
[
  {
    instancePath: '',
    schemaPath: '#/required',
    keyword: 'required',
    params: { missingProperty: 'taxonomy_term--log_category--weed_control' },
    message: "must have required property 'taxonomy_term--log_category--weed_control'"
  }
]
*/

// Now the entity is ambiguous because we have fixed it. (It is recognized and conflicts with another)
// We will force it to be taxonomy_term--log_category--weed_control, as that is more precise
let assembledData = lastInstance.assembled_entity;
assembledData['taxonomy_term--log_category--weed_control'] = lastInstance.ambiguousEntities.filter( d => d.possibleOverlays.includes('taxonomy_term--log_category--weed_control') )[0].entity;

let intendedConvention = JSON.parse( fs.readFileSync(directory_name) );
intendedConvention.gitlabProjectId = 'undefined';

let convention = new builder.ConventionSchema( intendedConvention );

// add our example, assembledData, into the object.
convention.addExample({ object: assembledData, is_valid_example: true });

// test all examples with the testExamples method.
let test = convention.testExamples();
test.success;
// true

let file_directory_name = path.join(__dirname, '..', '..', '..', 'working_examples', 'processed_examples', 'log_activity_flaming_noField.json');
// Store your object
fs.writeFileSync( file_directory_name, JSON.stringify(assembledData) );  