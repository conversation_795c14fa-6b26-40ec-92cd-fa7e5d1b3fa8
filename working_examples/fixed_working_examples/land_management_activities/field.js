let {create_field1} = require("./surveyStackExamples");
const builder = require("convention_builder");
const { randomUUID } = require('crypto');

// Use this for the Node REPL if necessary based on your setup
//__dirname = path.resolve();
let conventionEntityID = randomUUID();

let moreEntities = [
    {
        type: "taxonomy_term--convention",
        id: conventionEntityID,
        attributes: {
            name: "Field",
            external_uri: "https://gitlab.com/api/v4/projects/44311021/jobs/artifacts/main/raw/output/collection/conventions/asset--land--field/schema.json?job=copy_schemas"
        }
    },
];

let example_array = [ ... create_field1, ... moreEntities ];

example_array[0].attributes;

// This has the convention 'asset--land--field', which we will remove as it is not necessary for the example 
/*
{
  convention: [ 'asset--land--field' ],
  name: 'new_field_example',
  status: 'active',
  intrinsic_geometry: {
    value: 'GEOMETRYCOLLECTION(POLYGON ((-58.10067061294254 2.1804178031026282, -59.286637392831054 -1.5860417183772455, -52.93822698283956 -0.9583348609234292, -52.41500634465345 1.2041770971739254, -56.42636457074697 2.7380036087330524, -58.10067061294254 2.1804178031026282)))'
  },
  land_type: 'field',
  is_location: true,
  is_fixed: true,
  flag: []
}
*/

// remove unneeded attribute from the example
delete example_array[0].attributes.convention;


let landAssets = example_array.filter( d => /^asset--land/.test(d.type) );

// Make the land asset have a relationship (to add the convention relationship)
landAssets.forEach( asset => {
    asset.relationships = {};
} );

landAssets.forEach( asset => {
    asset
        .relationships
        .convention = {
            data: [
                {
                    type: "taxonomy_term--convention",
                    id: conventionEntityID
                }
            ]
        };
} );

let directory_name = path.join(__dirname, '..', '..', '..', 'output', 'collection', 'conventions', 'asset--land--field', 'object.json');
let base_schema_dir_name = path.join(__dirname, '..', '..', '..', 'input', 'collection');

let intendedConvention = JSON.parse( fs.readFileSync(directory_name) );
intendedConvention.gitlabProjectId = 'undefined';

//__dirname = path.resolve();
let preppedExample = new builder.ArrayDataOrganizer(
    {
        exampleArray: example_array,
        predefinedConventions: [ JSON.parse( fs.readFileSync(directory_name) ) ], 
        baseSchemataFolder: base_schema_dir_name
    });


// this will have 'is_valid:'
await preppedExample.initialize();

let convention = new builder.ConventionSchema( intendedConvention );

let assembledData = preppedExample.instances[0].assembled_entity;

// add our example, assembledData, into the object.
convention.addExample({ object: assembledData, is_valid_example: true });

// test all examples with the testExamples method.
let test = convention.testExamples();
test.success;

// Store your object
let file_directory_name = path.join(__dirname, '..', '..', '..', 'working_examples', 'processed_examples', 'asset--land--field_Field.json');
fs.writeFileSync( file_directory_name, JSON.stringify(assembledData) );  