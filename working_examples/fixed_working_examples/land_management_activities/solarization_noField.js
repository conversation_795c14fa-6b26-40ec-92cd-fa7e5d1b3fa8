let {solarization_example_noFieldyesPlanting} = require(`./surveyStackExamples`);
const builder = require("convention_builder");
const { randomUUID } = require('crypto');

// Use this for the Node REPL if necessary based on your setup
//__dirname = path.resolve();

// Add the convention relationship;
let conventionEntityID = randomUUID();

let moreEntities = [
    {
        type: "taxonomy_term--convention",
        id: conventionEntityID,
        attributes: {
            name: "Solarization",
            external_uri: "https://gitlab.com/api/v4/projects/44311021/jobs/artifacts/main/raw/output/collection/conventions/log--activity--solarization/schema.json?job=copy_schemas"
        }
    },
];

let example_array = [ ... solarization_example_noFieldyesPlanting, ... moreEntities ];

let logActivities = example_array.filter( d => /^log--activity/.test(d.type) );

logActivities.forEach( log => {
    log
        .relationships
        .convention = {
            data: [
                {
                    type: "taxonomy_term--convention",
                    id: conventionEntityID
                }
            ]
        };
} );


let directory_name = path.join(__dirname, '..', '..', '..', 'output', 'collection', 'conventions', 'log--activity--solarization', 'object.json');
let base_schema_dir_name = path.join(__dirname, '..', '..', '..', 'input', 'collection');

let preparedExample = new builder.ArrayDataOrganizer(
    {
        exampleArray: example_array,
        predefinedConventions: [ JSON.parse( fs.readFileSync(directory_name) ) ],
        baseSchemataFolder: base_schema_dir_name
    });

await preparedExample.initialize();

let lastInstance = preparedExample.instances[preparedExample.instances.length - 1];

lastInstance.errors;
/*
[
  {
    instancePath: '',
    schemaPath: '#/required',
    keyword: 'required',
    params: { missingProperty: 'log--activity--solarization' },
    message: "must have required property 'log--activity--solarization'"
  },
  {
    instancePath: '',
    schemaPath: '#/required',
    keyword: 'required',
    params: { missingProperty: 'taxonomy_term--log_category--weed_control' },
    message: "must have required property 'taxonomy_term--log_category--weed_control'"
  }
]
*/

// Taking a look at the first error, the problems with the log--activity 
lastInstance.unrecognizedEntities.filter( d => d.entity.type.includes('log--activity') ).filter( d => d.entity.attributes.name.includes('solarization') )[0].allTests.find(d => d.overlay.includes('log--activity--solarization')).errors;

/*
[
  {
    instancePath: '',
    schemaPath: '#/required',
    keyword: 'required',
    params: { missingProperty: 'id' },
    message: "must have required property 'id'"
  },
  {
    instancePath: '/attributes/timestamp',
    schemaPath: '#/properties/attributes/properties/timestamp/type',
    keyword: 'type',
    params: { type: 'string' },
    message: 'must be string'
  }
]
*/

let solarizationRejectedCandidates = lastInstance.unrecognizedEntities.filter( d => d.entity.type.includes('log--activity') ).filter( d => d.entity.attributes.name.includes('solarization') );

// This is in a loop as there are multiple entities (from being a many pass example)
// I believe it is also the case that solarization is like irrigation, whereas 
// there are multiple entities (for taking the tarp on / taking the tarp off)
solarizationRejectedCandidates.forEach( log => {
    log.entity.id = randomUUID();
    log.entity.attributes.timestamp = new Date( 1000 * parseFloat( log.entity.attributes.timestamp ) ).toISOString();
} );

await preparedExample.structureInstances();

lastInstance = preparedExample.instances[preparedExample.instances.length - 1];
lastInstance.errors;
/*
errors: [
    {
      instancePath: '',
      schemaPath: '#/required',
      keyword: 'required',
      params: [Object],
      message: "must have required property 'taxonomy_term--log_category--weed_control'"
    },

*/

lastInstance.unrecognizedEntities.find( e => e.entity.type == "taxonomy_term--log_category" ).allTests.find(d => d.overlay.includes('taxonomy_term--log_category--weed_control')).errors;
/*
[
  {
    instancePath: '/attributes/status',
    schemaPath: '#/properties/attributes/properties/status/type',
    keyword: 'type',
    params: { type: 'boolean' },
    message: 'must be boolean'
  },
  {
    instancePath: '/attributes/status',
    schemaPath: '#/properties/attributes/properties/status/oneOf/0/const',
    keyword: 'const',
    params: { allowedValue: 0 },
    message: 'must be equal to constant'
  },
  {
    instancePath: '/attributes/status',
    schemaPath: '#/properties/attributes/properties/status/oneOf/1/const',
    keyword: 'const',
    params: { allowedValue: 1 },
    message: 'must be equal to constant'
  },
  {
    instancePath: '/attributes/status',
    schemaPath: '#/properties/attributes/properties/status/oneOf',
    keyword: 'oneOf',
    params: { passingSchemas: null },
    message: 'must match exactly one schema in oneOf'
  }
]
*/

// We are getting rid of status from this entity to solve the error

example_array.filter( d => d.type == "taxonomy_term--log_category" ).forEach( obj => { delete obj.attributes.status} );

preparedExample.instances = [];
await preparedExample.structureInstances();

lastInstance = preparedExample.instances[preparedExample.instances.length - 1];

// The taxonomy term is still not recognized, it is probably ambiguous now
lastInstance.errors;
/*
[
  {
    instancePath: '',
    schemaPath: '#/required',
    keyword: 'required',
    params: { missingProperty: 'taxonomy_term--log_category--weed_control' },
    message: "must have required property 'taxonomy_term--log_category--weed_control'"
  }
]
*/

// Ambiguous entity should be set to the more specific one, "taxnomy_term--log_category--weed_control"
lastInstance.ambiguousEntities.find( e => e.possibleOverlays.includes('taxonomy_term--log_category--weed_control'));
/*
{
  passedTests: [
    { overlay: 'taxonomy_term--log_category', errors: [] },
    {
      overlay: 'taxonomy_term--log_category--weed_control',
      errors: []
    }
  ],
  possibleOverlays: [
    'taxonomy_term--log_category',
    'taxonomy_term--log_category--weed_control'
  ],
  allTests: [
    { overlay: 'log--activity--solarization', errors: [Array] },
    { overlay: 'convention_taxonomy', errors: [Array] },
    { overlay: 'taxonomy_term--log_category', errors: [] },
    {
      overlay: 'taxonomy_term--log_category--weed_control',
      errors: []
    },
    { overlay: 'quantity--standard--area_percentage', errors: [Array] },
    { overlay: 'taxonomy_term--unit--%', errors: [Array] },
    { overlay: 'plant_asset', errors: [Array] }
  ],
  amountPassed: 2,
  entity: {
    type: 'taxonomy_term--log_category',
    id: '10a5030d-3c8b-4a8b-80ca-439f8858789c',
    attributes: { name: 'weed_control' }
  }
*/

// taxonomy_term--log_category is an ambiguous entity that we will set to taxonomy_term--log_category--weed_control below
// is_valid prep
let intendedConvention = JSON.parse( fs.readFileSync(directory_name) );
intendedConvention.gitlabProjectId = 'undefined';

let convention = new builder.ConventionSchema( intendedConvention );

let assembledData = lastInstance.assembled_entity;

// removing the ambiguous entity by forcing it to be recognized as such
assembledData['taxonomy_term--log_category--weed_control'] = lastInstance.ambiguousEntities.find( e => e.possibleOverlays.includes('taxonomy_term--log_category--weed_control')).entity;

// add our example, assembledData, into the object.
convention.addExample({ object: assembledData, is_valid_example: true });

// test all examples with the testExamples method.
let test = convention.testExamples();
test.success;
// true

// Store your object
let file_directory_name = path.join(__dirname, '..', '..', '..', 'working_examples', 'processed_examples', 'log_activity_solarization_noField.json');
fs.writeFileSync( file_directory_name, JSON.stringify(assembledData) );  