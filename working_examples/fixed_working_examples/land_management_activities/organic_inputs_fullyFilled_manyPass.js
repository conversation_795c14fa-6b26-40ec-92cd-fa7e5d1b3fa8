let {organic_matter_inputs_fullDetails_multipleEntries} = require(`./surveyStackExamples`); 
const builder = require("convention_builder");
const { randomUUID } = require('crypto');

// Use this for the Node REPL if necessary based on your setup
//__dirname = path.resolve();

// Add the convention relationship
let conventionEntityID = randomUUID();

let moreEntities = [
    {
        type: "taxonomy_term--convention",
        id: conventionEntityID,
        attributes: {
            name: "Organic Matter",
            external_uri: "https://gitlab.com/api/v4/projects/44311021/jobs/artifacts/main/raw/output/collection/conventions/log--input--organic_matter/schema.json?job=copy_schemas"
        }
    },
];

let example_array = [ ... organic_matter_inputs_fullDetails_multipleEntries, ... moreEntities ];

let logInputs = example_array.filter( d => /^log--input/.test(d.type) );
logInputs.forEach( log => {
    log
        .relationships
        .convention = {
            data: [
                {
                    type: "taxonomy_term--convention",
                    id: conventionEntityID
                }
            ]
        };
} );

let directory_name = path.join(__dirname, '..', '..', '..', 'output', 'collection', 'conventions', 'log--input--organic_matter', 'object.json');
let base_schema_dir_name = path.join(__dirname, '..', '..', '..', 'input', 'collection');

let preparedExample = new builder.ArrayDataOrganizer(
    {
        exampleArray: example_array,
        predefinedConventions: [ JSON.parse( fs.readFileSync(directory_name) ) ],
        baseSchemataFolder: base_schema_dir_name
    });

await preparedExample.initialize();

let lastInstance = preparedExample.instances[preparedExample.instances.length - 1];

lastInstance.errors;
/*
errors: [
    {
      instancePath: '',
      schemaPath: '#/required',
      keyword: 'required',
      params: [Object],
      message: "must have required property 'log--input--organic_matter'"
    }
  ],
*/

// NOTE: for organic_inputs, the entity.attributes.name is not consistent (comma separated list of products)
// This should be closely watched to ensure it is a log--input--organic_matter 

lastInstance.unrecognizedEntities.find( d => d.entity.type.includes('log--input') ).allTests.find(d => d.overlay.includes('log--input--organic_matter')).errors;
/*
[
  {
    instancePath: '',
    schemaPath: '#/required',
    keyword: 'required',
    params: { missingProperty: 'id' },
    message: "must have required property 'id'"
  },
  {
    instancePath: '/attributes/timestamp',
    schemaPath: '#/properties/attributes/properties/timestamp/format',
    keyword: 'format',
    params: { format: 'date-time' },
    message: 'must match format "date-time"'
  }
]
*/

let organicInputsRejectedCandidates = lastInstance.unrecognizedEntities.filter( d => d.entity.type.includes('log--input') );


// This is in a loop as there are multiple entities (from being a many pass example)
organicInputsRejectedCandidates.forEach( log => {
    log.entity.id = randomUUID();
    log.entity.attributes.timestamp = new Date( 1000 * parseFloat( log.entity.attributes.timestamp ) ).toISOString();
} );

preparedExample.instances = [];
await preparedExample.structureInstances();

lastInstance = preparedExample.instances[preparedExample.instances.length - 1];

lastInstance.is_valid;

// is_valid 
let intendedConvention = JSON.parse( fs.readFileSync(directory_name) );
intendedConvention.gitlabProjectId = 'undefined';

let convention = new builder.ConventionSchema( intendedConvention );

let assembledData = lastInstance.assembled_entity;

// add our example, assembledData, into the object.
convention.addExample({ object: assembledData, is_valid_example: true });

// test all examples with the testExamples method.
let test = convention.testExamples();
test.success;
// true

// Store your object
let file_directory_name = path.join(__dirname, '..', '..', '..', 'working_examples', 'processed_examples', 'log_input_organic_matter_fullyFilled_manyPass.json');
fs.writeFileSync( file_directory_name, JSON.stringify(assembledData) );  