let {pesticide_herbicide_fungicide_many_details_multiplePass} = require("./surveyStackExamples");
const builder = require("convention_builder");
const { randomUUID } = require('crypto');

//__dirname = path.resolve();
let conventionEntityID = randomUUID();

let moreEntities = [
    {
        type: "taxonomy_term--convention",
        id: conventionEntityID,
        attributes: {
            name: "Herbicide/pesticide",
            external_uri: "https://gitlab.com/api/v4/projects/44311021/jobs/artifacts/main/raw/output/collection/conventions/log--input--herbicide_or_pesticide/schema.json?job=copy_schemas"
        }
    },
];

let example_array = [ ... pesticide_herbicide_fungicide_many_details_multiplePass, ... moreEntities ];

// TODO ERRORS in API  compose we fix here
// 1 -- Missing quantity--material--active_ingredient_percent.
// 2 -- The name of the product is not in the valid list and is instead 'herbicide'.

// The label should be constant. The place to inform what's been applied should be another entity.
let allQuantityMaterials = example_array.filter( d => d.type == "quantity--material" );
// Fixing labels for tt quantities.
allQuantityMaterials.forEach( d => d.attributes.label = "application_rate" );


let inputLogs = example_array.filter( d => /^log--input/.test(d.type) );

inputLogs.forEach( log => {
    log
        .relationships
        .convention = {
            data: [
                {
                    type: "taxonomy_term--convention",
                    id: conventionEntityID
                }
            ]
        };
} );


// // // Fixing detected errors
let providedLogInput = example_array.find( d => d.type == "log--input" );
// API COMPOSE TODO: missing id
providedLogInput.id = randomUUID();
// API COMPOSE TODO: wrong timestamp format
providedLogInput.attributes.timestamp = '2024-07-08T17:04:51.519Z';

let directory_name = path.join(__dirname, '..', '..', '..', 'output', 'collection', 'conventions', 'log--input--herbicide_or_pesticide', 'object.json');
let base_schema_dir_name = path.join(__dirname, '..', '..', '..', 'input', 'collection');

let intendedConvention = JSON.parse( fs.readFileSync(directory_name) );
intendedConvention.gitlabProjectId = 'undefined';

let preppedExample = new builder.ArrayDataOrganizer(
    {
        exampleArray: example_array,
        predefinedConventions: [ JSON.parse( fs.readFileSync(directory_name) ) ],
        baseSchemataFolder: base_schema_dir_name
    });

await preppedExample.initialize();
preppedExample.instances[0].errors;
/*
[
  {
    instancePath: '',
    schemaPath: '#/required',
    keyword: 'required',
    params: {
      missingProperty: 'taxonomy_term--log_category--pest_disease_control'
    },
    message: "must have required property 'taxonomy_term--log_category--pest_disease_control'"
  }
]
*/

let issuesWithLogInput = preppedExample.instances[0].unrecognizedEntities.find( d => d.entity.type == "log--input" ).allTests.find( d => d.overlay == "log--input--herbicide_or_pesticide" );
issuesWithLogInput;
/*
[
  {
    instancePath: '',
    schemaPath: '#/required',
    keyword: 'required',
    params: {
      missingProperty: 'taxonomy_term--log_category--pest_disease_control'
    },
    message: "must have required property 'taxonomy_term--log_category--pest_disease_control'"
  }
]
{
  overlay: 'log--input--herbicide_or_pesticide',
  errors: [
    {
      instancePath: '',
      schemaPath: '#/required',
      keyword: 'required',
      params: [Object],
      message: "must have required property 'id'"
    },
    {
      instancePath: '/attributes/timestamp',
      schemaPath: '#/properties/attributes/properties/timestamp/type',
      keyword: 'type',
      params: [Object],
      message: 'must be string'
    }
  ]
}
*/

// // // Since there are ambiguous entities, we finish the assignation "by hand".
let assembledData = preppedExample.instances[0].assembled_entity;

//preppedExample.instances[0].ambiguousEntities[0].entity;
// NOTE Use a fixed identifier for replacements. Before, an array position was used here, which is inherently unstable (if more erors emerge, the order can change and the assignation stops working). Here, I am using unambiguous attributes to obtain the proper entity.
assembledData['taxonomy_term--log_category--pest_disease_control'] = preppedExample.instances[0].ambiguousEntities.find( d => d.possibleOverlays.includes('taxonomy_term--log_category--pest_disease_control') ).entity;


let convention = new builder.ConventionSchema( intendedConvention );

// add our example, assembledData, into the object.
// convention.validExamples = [];
convention.addExample({ object: assembledData, is_valid_example: true });

// test all examples with the testExamples method.
let test = convention.testExamples();
test.success;

let file_directory_name = path.join(__dirname, '..', '..', '..', 'working_examples', 'processed_examples', 'log_input_herbicide_or_pesticide_fullyFilled_manyPass.json');
fs.writeFileSync( file_directory_name, JSON.stringify(assembledData) );  