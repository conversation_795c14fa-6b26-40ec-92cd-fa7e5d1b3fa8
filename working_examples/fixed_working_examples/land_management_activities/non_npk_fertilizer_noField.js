let {non_npk_fertilizer_noField} = require("./surveyStackExamples");
const builder = require("convention_builder");
const { randomUUID } = require('crypto');

//__dirname = path.resolve();
let conventionEntityID = randomUUID();

let moreEntities = [
    {
        type: "taxonomy_term--convention",
        id: conventionEntityID,
        attributes: {
            name: "Fertilizer",
            external_uri: "https://gitlab.com/api/v4/projects/44311021/jobs/artifacts/main/raw/output/collection/conventions/log--input--fertilizer/schema.json?job=copy_schemas"
        }
    },
];

let example_array = [ ... non_npk_fertilizer_noField, ... moreEntities ];

let logInputs = example_array.filter( d => /^log--input/.test(d.type) );
logInputs.forEach( log => {
    log
        .relationships
        .convention = {
            data: [
                {
                    type: "taxonomy_term--convention",
                    id: conventionEntityID
                }
            ]
        };
} );


// This is using the convention locally instead of grabbing it from the internet 
let directory_name = path.join(__dirname, '..', '..', '..', 'output', 'collection', 'conventions', 'log--input--fertilizer', 'object.json');
let base_schema_dir_name = path.join(__dirname, '..', '..', '..', 'input', 'collection');

let preparedExample = new builder.ArrayDataOrganizer(
    {
        exampleArray: example_array,
        predefinedConventions: [ JSON.parse( fs.readFileSync(directory_name) ) ],
        baseSchemataFolder: base_schema_dir_name
    });

await preparedExample.initialize();

let lastInstance = preparedExample.instances[preparedExample.instances.length - 1];

lastInstance.errors;
/*
[
  {
    instancePath: '',
    schemaPath: '#/required',
    keyword: 'required',
    params: { missingProperty: 'log--input--fertilizer' },
    message: "must have required property 'log--input--fertilizer'"
  }
]
*/

lastInstance.unrecognizedEntities.filter( d => d.entity.type.includes('log--input') ).filter( d => d.entity.attributes.name.includes('fertilizer') )[0].allTests.find(d => d.overlay.includes('log--input--fertilizer')).errors;
/*
[
  {
    instancePath: '',
    schemaPath: '#/required',
    keyword: 'required',
    params: { missingProperty: 'id' },
    message: "must have required property 'id'"
  },
  {
    instancePath: '/attributes/timestamp',
    schemaPath: '#/properties/attributes/properties/timestamp/type',
    keyword: 'type',
    params: { type: 'string' },
    message: 'must be string'
  }
]
*/

let fertilizerRejectedCandidate = lastInstance.unrecognizedEntities.filter( d => d.entity.type.includes('log--input') ).find( d => d.entity.attributes.name.includes('fertilizer') );

fertilizerRejectedCandidate.entity.id = randomUUID();

fertilizerRejectedCandidate.entity.attributes.timestamp = new Date( 1000 * parseFloat( fertilizerRejectedCandidate.entity.attributes.timestamp ) ).toISOString();

preparedExample.instances = [];
await preparedExample.structureInstances();


lastInstance = preparedExample.instances[preparedExample.instances.length - 1];
lastInstance.is_valid;
//true

let intendedConvention = JSON.parse( fs.readFileSync(directory_name) );
intendedConvention.gitlabProjectId = 'undefined';

let convention = new builder.ConventionSchema( intendedConvention );

let assembledData = lastInstance.assembled_entity;

// add our example, assembledData, into the object.
convention.addExample({ object: assembledData, is_valid_example: true });

// test all examples with the testExamples method.
let test = convention.testExamples();
test.success;
// true

let file_directory_name = path.join(__dirname, '..', '..', '..', 'working_examples', 'processed_examples', 'log_input_non_NPK_fertilizer_noField.json');

fs.writeFileSync( file_directory_name, JSON.stringify(assembledData) );  