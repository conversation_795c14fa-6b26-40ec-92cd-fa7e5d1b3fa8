let {npk_fertilizer_noField} = require("./surveyStackExamples");
const builder = require("convention_builder");
const { randomUUID } = require('crypto');

//__dirname = path.resolve();
let conventionEntityID = randomUUID();

let moreEntities = [
    {
        type: "taxonomy_term--convention",
        id: conventionEntityID,
        attributes: {
            name: "Fertilizer",
            external_uri: "https://gitlab.com/api/v4/projects/44311021/jobs/artifacts/main/raw/output/collection/conventions/log--input--fertilizer/schema.json?job=copy_schemas"
        }
    },
];

let example_array = [ ... npk_fertilizer_noField, ... moreEntities ];

let logInputs = example_array.filter( d => /^log--input/.test(d.type) );
logInputs.forEach( log => {
    log
        .relationships
        .convention = {
            data: [
                {
                    type: "taxonomy_term--convention",
                    id: conventionEntityID
                }
            ]
        };
} );


// This is using the convention locally instead of grabbing it from the internet 
let directory_name = path.join(__dirname, '..', '..', '..', 'output', 'collection', 'conventions', 'log--input--fertilizer', 'object.json');
let base_schema_dir_name = path.join(__dirname, '..', '..', '..', 'input', 'collection');

let intendedConvention = JSON.parse( fs.readFileSync(directory_name) );
intendedConvention.gitlabProjectId = 'undefined';

let preppedExample = new builder.ArrayDataOrganizer(
    {
        exampleArray: example_array,
        predefinedConventions: [ JSON.parse( fs.readFileSync(directory_name) ) ],
        baseSchemataFolder: base_schema_dir_name
    });

await preppedExample.initialize();

preppedExample.instances[0].errors;
/*
[
  {
    instancePath: '',
    schemaPath: '#/required',
    keyword: 'required',
    params: { missingProperty: 'log--input--fertilizer' },
    message: "must have required property 'log--input--fertilizer'"
  }
]
*/

preppedExample.instances[0].unrecognizedEntities[1].allTests[0];
/*
{
  overlay: 'log--input--fertilizer',
  errors: [
    {
      instancePath: '',
      schemaPath: '#/required',
      keyword: 'required',
      params: [Object],
      message: "must have required property 'id'"
    },
    {
      instancePath: '/attributes/timestamp',
      schemaPath: '#/properties/attributes/properties/timestamp/type',
      keyword: 'type',
      params: [Object],
      message: 'must be string'
    }
  ]
}
*/

let npkFertilizerRejectedCandidate = preppedExample.instances[0].unrecognizedEntities.filter( e => e.entity.type == "log--input" )[0];

npkFertilizerRejectedCandidate.entity.id = randomUUID();

npkFertilizerRejectedCandidate.entity.attributes.timestamp = new Date( 1000 * parseFloat( npkFertilizerRejectedCandidate.entity.attributes.timestamp ) ).toISOString();

preppedExample.instances = [];
await preppedExample.structureInstances();

preppedExample.instances[0].is_valid;
//true

let file_directory_name = path.join(__dirname, '..', '..', '..', 'working_examples', 'processed_examples', 'log_input_NPK_fertilizer_noField.json');

let assembledData = preppedExample.instances[0].assembled_entity;
fs.writeFileSync( file_directory_name, JSON.stringify(assembledData) );  