let {additional_harvest_or_termination_details_fullyFilled_manyPass} = require("./surveyStackExamples");
const builder = require("convention_builder");
const { randomUUID } = require('crypto');

// Use this for the Node REPL if necessary based on your setup
//__dirname = path.resolve();

// Add the convention relationship
let conventionEntityID = randomUUID();

let moreEntities = [
    {
        type: "taxonomy_term--convention",
        id: conventionEntityID,
        attributes: {
            name: "Harvest",
            external_uri: "https://gitlab.com/api/v4/projects/44311021/jobs/artifacts/main/raw/output/collection/conventions/log--harvest--harvest/schema.json?job=copy_schemas"
        }
    },
];

let example_array = [ ... additional_harvest_or_termination_details_fullyFilled_manyPass, ... moreEntities ];

let logHarvests = example_array.filter( d => /^log--harvest/.test(d.type) );

logHarvests.forEach( log => {
    log
        .relationships
        .convention = {
            data: [
                {
                    type: "taxonomy_term--convention",
                    id: conventionEntityID
                }
            ]
        };
} );

let directory_name = path.join(__dirname, '..', '..', '..', 'output', 'collection', 'conventions', 'log--harvest--harvest', 'object.json');
let base_schema_dir_name = path.join(__dirname, '..', '..', '..', 'input', 'collection');

let preparedExample = new builder.ArrayDataOrganizer(
    {
        exampleArray: example_array,
        predefinedConventions: [ JSON.parse( fs.readFileSync(directory_name) ) ],
        baseSchemataFolder: base_schema_dir_name
    });

await preparedExample.initialize();

let lastInstance = preparedExample.instances[preparedExample.instances.length - 1];

lastInstance.errors;
/*
[
  {
    instancePath: '',
    schemaPath: '#/required',
    keyword: 'required',
    params: { missingProperty: 'log--harvest--harvest' },
    message: "must have required property 'log--harvest--harvest'"
  }
]
*/

// We need to fix the ID and timestamp for this unrecognized entity 
lastInstance.unrecognizedEntities.find( d => d.entity.attributes.name.includes('harvest') ).allTests.find( d => d.overlay.includes('log--harvest--harvest') ).errors;
/*
[
  {
    instancePath: '',
    schemaPath: '#/required',
    keyword: 'required',
    params: { missingProperty: 'id' },
    message: "must have required property 'id'"
  },
  {
    instancePath: '/attributes/timestamp',
    schemaPath: '#/properties/attributes/properties/timestamp/format',
    keyword: 'format',
    params: { format: 'date-time' },
    message: 'must match format "date-time"'
  }
]
*/

let harvestRejectedCandidates = lastInstance.unrecognizedEntities.filter( e => e.entity.type == "log--harvest" );

// This is in a loop as there are multiple entities (from being a many pass example)
harvestRejectedCandidates.forEach( log => {
    log.entity.id = randomUUID();
    log.entity.attributes.timestamp = new Date( 1000 * parseFloat( log.entity.attributes.timestamp ) ).toISOString();
} );

preparedExample.instances = [];
await preparedExample.structureInstances();

lastInstance = preparedExample.instances[preparedExample.instances.length - 1];

lastInstance.is_valid;
// is_valid 

// is_valid preparation
let intendedConvention = JSON.parse( fs.readFileSync(directory_name) );
intendedConvention.gitlabProjectId = 'undefined';

await preparedExample.initialize();

let convention = new builder.ConventionSchema( intendedConvention );

let assembledData = lastInstance.assembled_entity;

// add our example, assembledData, into the object.
convention.addExample({ object: assembledData, is_valid_example: true });

// test all examples with the testExamples method.
let test = convention.testExamples();
test.success;
// true

// Store your object
let file_directory_name = path.join(__dirname, '..', '..', '..', 'working_examples', 'processed_examples', 'log_harvest_harvest_fullyFilled_manyPass.json');
fs.writeFileSync( file_directory_name, JSON.stringify(assembledData) );  