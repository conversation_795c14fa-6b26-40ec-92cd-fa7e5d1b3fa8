let {solarization_details_minFilled} = require(`./surveyStackExamples`);
const builder = require("convention_builder");
const { randomUUID } = require('crypto');

// Use this for the Node REPL if necessary based on your setup
//__dirname = path.resolve();

// Add the convention relationship
let conventionEntityID = randomUUID();

let moreEntities = [
    {
        type: "taxonomy_term--convention",
        id: conventionEntityID,
        attributes: {
            name: "Solarization",
            external_uri: "https://gitlab.com/api/v4/projects/44311021/jobs/artifacts/main/raw/output/collection/conventions/log--activity--solarization/schema.json?job=copy_schemas"
        }
    },
];

let example_array = [ ... solarization_details_minFilled, ... moreEntities ];

let logActivities = example_array.filter( d => /^log--activity/.test(d.type) );

logActivities.forEach( log => {
    log
        .relationships
        .convention = {
            data: [
                {
                    type: "taxonomy_term--convention",
                    id: conventionEntityID
                }
            ]
        };
} );

let directory_name = path.join(__dirname, '..', '..', '..', 'output', 'collection', 'conventions', 'log--activity--solarization', 'object.json');
let base_schema_dir_name = path.join(__dirname, '..', '..', '..', 'input', 'collection');

let preparedExample = new builder.ArrayDataOrganizer(
    {
        exampleArray: example_array,
        predefinedConventions: [ JSON.parse( fs.readFileSync(directory_name) ) ],
        baseSchemataFolder: base_schema_dir_name
    });

await preparedExample.initialize();

let lastInstance = preparedExample.instances[preparedExample.instances.length - 1];

lastInstance.errors;
/*
[
  {
    instancePath: '',
    schemaPath: '#/required',
    keyword: 'required',
    params: { missingProperty: 'log--activity--solarization' },
    message: "must have required property 'log--activity--solarization'"
  },
  {
    instancePath: '',
    schemaPath: '#/required',
    keyword: 'required',
    params: { missingProperty: 'taxonomy_term--log_category--weed_control' },
    message: "must have required property 'taxonomy_term--log_category--weed_control'"
  },
  {
    instancePath: '',
    schemaPath: '#/required',
    keyword: 'required',
    params: { missingProperty: 'plant_asset' },
    message: "must have required property 'plant_asset'"
  }
]
*/

// NOTE: the plant_asset will no longer be required after changes have been made to the base convention 

let solarizationRejectedCandidates = lastInstance.unrecognizedEntities.filter( e => e.entity.type == "log--activity" );

// This is in a loop as there are multiple entities (from being a many pass example)
// I believe it is also the case that solarization is like irrigation, whereas 
// there are multiple entities (for taking the tarp on / taking the tarp off)
solarizationRejectedCandidates.forEach( log => {
    log.entity.id = randomUUID();
    log.entity.attributes.timestamp = new Date( 1000 * parseFloat( log.entity.attributes.timestamp ) ).toISOString();
} );

preparedExample.instances = [];
await preparedExample.structureInstances();

lastInstance = preparedExample.instances[preparedExample.instances.length - 1];

lastInstance.ambiguousEntities;
/*
[
  {
    passedTests: [ [Object], [Object] ],
    possibleOverlays: [
      'taxonomy_term--log_category',
      'taxonomy_term--log_category--weed_control'
    ],
    allTests: [
      [Object], [Object],
      [Object], [Object],
      [Object], [Object],
      [Object]
    ],
    amountPassed: 2,
    entity: {
      type: 'taxonomy_term--log_category',
      id: 'e1ebaa2e-52e1-4147-8b68-24bee951b0fe',
      attributes: [Object]
    }
  }
]
*/

let assembledData = lastInstance.assembled_entity;
assembledData['taxonomy_term--log_category--weed_control'] = lastInstance.ambiguousEntities.find( d => d.possibleOverlays.includes('taxonomy_term--log_category--weed_control') ).entity;


let intendedConvention = JSON.parse( fs.readFileSync(directory_name) );
intendedConvention.gitlabProjectId = 'undefined';

let convention = new builder.ConventionSchema( intendedConvention );

// add our example, assembledData, into the object.
convention.addExample({ object: assembledData, is_valid_example: true });

// test all examples with the testExamples method.
let test = convention.testExamples();
test.success;
// false !

// Plant asset and such 
// TODO: Come back here to finish this once it's working properly 