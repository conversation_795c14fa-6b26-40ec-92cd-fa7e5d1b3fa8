const builder = require('convention_builder');
const { randomUUID } = require('crypto');

/* 
This files contains: 
    - A good example for herbicide
    - Bad example with: 
        - wrong taxonomy_term--material_type--herbicide_or_pesticide NAME
        - no taxonomy_term--material_type--herbicide_or_pesticide
        - wrong taxonomy_term--unit--rate NAME 
        - no taxonomy relationship units for quantity--material 
        - no taxonomy relationship material_type for quantity--material--rate
        - no quantity--material--rate (but instead two quantity--material--active_ingredient_percent)
        - no taxonomy term taxonomy_term--material_type--herbicide_or_pesticide (but instead taxonomy--term--material_type--fertilizer)
        - no quantity--material--active_ingredient_percentage
*/


// Required Terms 
// log--input--herbicide_or_pesticide
    // id 
    // attributes 
        // timestamp
        // status
    // relationships 
        // convention
        // asset 
            // asset--plant
        // category
            // taxonomy_term--log_category or taxonomy_term--log_category--pest_disease_control

// convention_taxonomy
    // id
    // attributes
        //name = Herbicide/pesticide

// taxonomy_term--material_type--herbicide_or_pesticide
    // id
    // attributes 
        // name = [list of possible values]

// quantity--material--active_ingredient_percent
    // id 
    // attributes

// asset--plant--planting
    // id
    // attributes
        // name
        // status = active

// quantity--material--rate
    // id
    // attributes
    // relationships
        // material_type
            // id = taxonomy_term--material_type--herbicide_or_pesticide

// taxonomy_term--unit--rate
    // id 
    // attributes
        // name = [list of possible values]

// taxonomy_term--log_category--pest_disease_control
    // id
    // attributes
        // name = pest_disease_control



// Building examples for testing 

// ** don't forget this ** 
//__dirname = path.resolve();

let directory_name = path.join(__dirname, '..', '..', '..', 'output', 'collection', 'conventions', 'log--input--herbicide_or_pesticide', 'object.json');
let base_schema_dir_name = path.join(__dirname, '..', '..', '..', 'input', 'collection');

let intendedConvention = JSON.parse( fs.readFileSync(directory_name) );
intendedConvention.gitlabProjectId = 'undefined';

let convention = new builder.ConventionSchema( intendedConvention );

let assetID = randomUUID();
let conventionID = randomUUID();

let asset__plant__planting__uuid = randomUUID();

let taxonomy_term__log_category__pest_disease_control__uuid = randomUUID();
let taxonomy_term__material_type__herbicide_or_pesticide__uuid = randomUUID();
let taxonomy_term__unit__percentage_uuid = randomUUID();
let taxonomy_term__unit__rate_uuid = randomUUID();

let quantity__standard__area_percentage_uuid = randomUUID();
let quantity__material__rate_uuid = randomUUID();
let quantity__material__active_ingredient_percent_uuid = randomUUID();



// good example (for reference and formatting purposes)
let createHerbExam = [
    {
        type: 'log--input',
        id: assetID,
        attributes: {
            name: 'example herbicide log',
            status: 'done',
            timestamp: ( new Date() ).toISOString()
        },
        relationships: { 
            convention: {
                data: [{
                    type: 'taxonomy_term--convention',
                    id: conventionID,
                }]
            },
            asset: {
                data: [{
                    type: 'asset--plant',
                    id: asset__plant__planting__uuid,
                }]
            }, 
            category: {
                data: [{
                    type: 'taxonomy_term--log_category',
                    id: taxonomy_term__log_category__pest_disease_control__uuid
                }]
            }, 
            quantity: {
                data: [
                    {
                        type: 'quantity--standard',
                        id: quantity__standard__area_percentage_uuid
                    }, 
                    {
                        type: 'quantity--material',
                        id: quantity__material__rate_uuid
                    },
                    {
                        type: 'quantity--material',
                        id: quantity__material__active_ingredient_percent_uuid
                    }
                ]
            }
        }
    },
    {
        type: 'taxonomy_term--convention',
        id: conventionID,
        attributes: {
            name: 'Herbicide/pesticide',
            external_uri:'https://gitlab.com/api/v4/projects/44311021/jobs/artifacts/main/raw/output/collection/conventions/log--input--herbicide_or_pesticide/schema.json?job=copy_schemas'
        }
    },
    {
        type: 'taxonomy_term--log_category',
        id: taxonomy_term__log_category__pest_disease_control__uuid,
        attributes: {
            name: 'pest_disease_control'
        }
    },
    {
        type: 'quantity--standard',
        id: quantity__standard__area_percentage_uuid,
        attributes: {
            label: 'area'
        }, 
        relationships: {
            units: {
                data: [
                    {
                        type: 'taxonomy_term--unit',
                        id: taxonomy_term__unit__percentage_uuid
                    }
                ]
            }
        }
    },
    {
        type: 'quantity--material--active_ingredient_percent',
        id: quantity__material__active_ingredient_percent_uuid, 
        attributes: {
            label: 'active_ingredient_percent'
        },
        relationships: {
            units: { 
                data: [
                    {
                        type: 'taxonomy_term--unit',
                        id: taxonomy_term__unit__percentage_uuid
                    }
                ]
            }
        }
    },
    {
        type: 'taxonomy_term--unit',
        id: taxonomy_term__unit__percentage_uuid,
        attributes: {
            name: '%'
        }
    },
    {
        type: 'quantity--material',
        id: quantity__material__rate_uuid,
        attributes: {
            label: 'application_rate',
            measure: 'rate'
        }, 
        relationships: {
            units: {
                data: [
                    {
                        type: 'taxonomy_term--unit',
                        id: taxonomy_term__unit__rate_uuid
                    }
                ]
            }, 
            material_type: {
                data: [
                    {
                        type: 'taxonomy_term--material_type',
                        id: taxonomy_term__material_type__herbicide_or_pesticide__uuid
                    }
                ]
            }
        }
    },
    {
        type: 'taxonomy_term--unit',
        id: taxonomy_term__unit__rate_uuid, 
        attributes: {
            name: 'cubic_ft'
        }
    },
    {
        type: 'taxonomy_term--material_type',
        id: taxonomy_term__material_type__herbicide_or_pesticide__uuid,
        attributes: {
            name: 'roundup'
        }
    }
];

// assembling 
let new_example = new builder.ArrayDataOrganizer({
    exampleArray: createHerbExam,
    predefinedConventions: [ JSON.parse( fs.readFileSync(directory_name) ) ], 
    baseSchemataFolder: base_schema_dir_name
});

await new_example.initialize();
lastInstance = new_example.instances[new_example.instances.length - 1];

let assembledData = lastInstance.assembled_entity;
assembledData['taxonomy_term--log_category--pest_disease_control'] = lastInstance.ambiguousEntities.find( d => d.possibleOverlays.includes('taxonomy_term--log_category--pest_disease_control') ).entity;

convention.addExample({ object: assembledData, is_valid_example: true });




// Bad Examples //

// wrong name taxonomy_term--material_type--herbicide_or_pesticide
let lastInstance2 = new_example.instances[new_example.instances.length - 1];
let assembledData2 = lastInstance2.assembled_entity;

assembledData2['taxonomy_term--material_type--herbicide_or_pesticide'] = lastInstance2.identifiedEntities.find( d => d.possibleOverlays.includes('taxonomy_term--material_type--herbicide_or_pesticide') ).entity;
assembledData2['taxonomy_term--material_type--herbicide_or_pesticide'].attributes.name = 'banana';

convention.addExample({ object: assembledData2, is_valid_example: false });



// no taxonomy_term--material_type--herbicide_or_pesticide 
let lastInstance3 = new_example.instances[new_example.instances.length - 1];
let assembledData3 = lastInstance3.assembled_entity;

assembledData3['taxonomy_term--material_type--herbicide_or_pesticide'] = lastInstance2.identifiedEntities.find( d => d.possibleOverlays.includes('taxonomy_term--material_type--herbicide_or_pesticide') ).entity;
assembledData3['taxonomy_term--material_type--herbicide_or_pesticide'] = '';

convention.addExample({ object: assembledData3, is_valid_example: false });



// wrong name taxonomy_term--unit--rate
let lastInstance4 = new_example.instances[new_example.instances.length - 1];
let assembledData4 = lastInstance4.assembled_entity;

assembledData4['taxonomy_term--unit--rate'] = lastInstance4.identifiedEntities.find( d => d.possibleOverlays.includes('taxonomy_term--unit--rate') ).entity;
assembledData4['taxonomy_term--unit--rate'].attributes.name = 'elephants/yard';

convention.addExample({ object: assembledData4, is_valid_example: false });



// quantity--material has no taxonomy relationship units
let lastInstance5 = new_example.instances[new_example.instances.length - 1];
let assembledData5 = lastInstance5.assembled_entity;

assembledData5['quantity--material--rate'] = lastInstance5.identifiedEntities.find( d => d.possibleOverlays.includes('quantity--material--rate') ).entity;
assembledData5['quantity--material--rate'].relationships.units.data = {};

convention.addExample({ object: assembledData5, is_valid_example: false });



// quantity--material--rate has no taxonomy relationship material_type
let lastInstance6 = new_example.instances[new_example.instances.length - 1];
let assembledData6 = lastInstance6.assembled_entity;

assembledData6['quantity--material--rate'] = lastInstance6.identifiedEntities.find( d => d.possibleOverlays.includes('quantity--material--rate') ).entity;
assembledData6['quantity--material--rate'].relationships.material_type.data = {};

convention.addExample({ object: assembledData6, is_valid_example: false });



let quantity__material__active_ingredient_percent_2_uuid = randomUUID();
let taxonomy_term__unit__percentage_2_uuid = randomUUID();

// no quantity--material--rate, two quantity--material--active_ingredient_percent

let createHerbExam_doubleActiveIngredientPercent = [
    {
        type: 'log--input',
        id: assetID,
        attributes: {
            name: 'example herbicide log',
            status: 'done',
            timestamp: ( new Date() ).toISOString()
        },
        relationships: { 
            convention: {
                data: [{
                    type: 'taxonomy_term--convention',
                    id: conventionID,
                }]
            },
            asset: {
                data: [{
                    type: 'asset--plant',
                    id: asset__plant__planting__uuid,
                }]
            }, 
            category: {
                data: [{
                    type: 'taxonomy_term--log_category',
                    id: taxonomy_term__log_category__pest_disease_control__uuid
                }]
            }, 
            quantity: {
                data: [
                    {
                        type: 'quantity--standard',
                        id: quantity__standard__area_percentage_uuid
                    }, 
                    {
                        type: 'quantity--material',
                        id: quantity__material__active_ingredient_percent_2_uuid
                    },
                    {
                        type: 'quantity--material',
                        id: quantity__material__active_ingredient_percent_uuid
                    }
                ]
            }
        }
    },
    {
        type: 'taxonomy_term--convention',
        id: conventionID,
        attributes: {
            name: 'Herbicide/pesticide',
            external_uri:'https://gitlab.com/api/v4/projects/44311021/jobs/artifacts/main/raw/output/collection/conventions/log--input--herbicide_or_pesticide/schema.json?job=copy_schemas'
        }
    },
    {
        type: 'taxonomy_term--log_category',
        id: taxonomy_term__log_category__pest_disease_control__uuid,
        attributes: {
            name: 'pest_disease_control'
        }
    },
    {
        type: 'quantity--standard',
        id: quantity__standard__area_percentage_uuid,
        attributes: {
            label: 'area'
        }, 
        relationships: {
            units: {
                data: [
                    {
                        type: 'taxonomy_term--unit',
                        id: taxonomy_term__unit__percentage_uuid
                    }
                ]
            }
        }
    },
    {
        type: 'quantity--material--active_ingredient_percent',
        id: quantity__material__active_ingredient_percent_uuid, 
        attributes: {
            label: 'active_ingredient_percent'
        },
        relationships: {
            units: { 
                data: [
                    {
                        type: 'taxonomy_term--unit',
                        id: taxonomy_term__unit__percentage_uuid
                    }
                ]
            }
        }
    },
    {
        type: 'taxonomy_term--unit',
        id: taxonomy_term__unit__percentage_uuid,
        attributes: {
            name: '%'
        }
    },
    {
        type: 'quantity--material--active_ingredient_percent',
        id: quantity__material__active_ingredient_percent_2_uuid, 
        attributes: {
            label: 'active_ingredient_percent'
        },
        relationships: {
            units: { 
                data: [
                    {
                        type: 'taxonomy_term--unit',
                        id: taxonomy_term__unit__percentage_2_uuid
                    }
                ]
            }
        }
    },
    {
        type: 'taxonomy_term--unit',
        id: taxonomy_term__unit__rate_uuid, 
        attributes: {
            name: 'cubic_ft'
        }
    },
    {
        type: 'taxonomy_term--material_type',
        id: taxonomy_term__material_type__herbicide_or_pesticide__uuid,
        attributes: {
            name: 'roundup'
        }
    }
];


let new_example_2 = new builder.ArrayDataOrganizer({
    exampleArray: createHerbExam_doubleActiveIngredientPercent,
    predefinedConventions: [ JSON.parse( fs.readFileSync(directory_name) ) ], 
    baseSchemataFolder: base_schema_dir_name
});

await new_example_2.initialize();
lastInstance2 = new_example_2.instances[new_example_2.instances.length - 1];

let assembledData2 = lastInstance2.assembled_entity;
assembledData2['taxonomy_term--log_category--pest_disease_control'] = lastInstance2.ambiguousEntities.find( d => d.possibleOverlays.includes('taxonomy_term--log_category--pest_disease_control') ).entity;

convention.addExample({ object: assembledData2, is_valid_example: false });



// Wrong taxonomy term (not taxonomy_term--material_type--herbicide_or_pesticide, but taxonomy--term--material_type--fertilizer)

/* 
"taxonomy_term--material_type--fertilizer":{
        id: taxonomy_term__material_type__fertilizer_uuid,
        attributes: {
            name: "ammonia_sulfate"
        }
    },
*/

let createHerbExam_wrongTaxTermMatType = [
    {
        type: 'log--input',
        id: assetID,
        attributes: {
            name: 'example herbicide log',
            status: 'done',
            timestamp: ( new Date() ).toISOString()
        },
        relationships: { 
            convention: {
                data: [{
                    type: 'taxonomy_term--convention',
                    id: conventionID,
                }]
            },
            asset: {
                data: [{
                    type: 'asset--plant',
                    id: asset__plant__planting__uuid,
                }]
            }, 
            category: {
                data: [{
                    type: 'taxonomy_term--log_category',
                    id: taxonomy_term__log_category__pest_disease_control__uuid
                }]
            }, 
            quantity: {
                data: [
                    {
                        type: 'quantity--standard',
                        id: quantity__standard__area_percentage_uuid
                    }, 
                    {
                        type: 'quantity--material',
                        id: quantity__material__active_ingredient_percent_2_uuid
                    },
                    {
                        type: 'quantity--material',
                        id: quantity__material__active_ingredient_percent_uuid
                    }
                ]
            }
        }
    },
    {
        type: 'taxonomy_term--convention',
        id: conventionID,
        attributes: {
            name: 'Herbicide/pesticide',
            external_uri:'https://gitlab.com/api/v4/projects/44311021/jobs/artifacts/main/raw/output/collection/conventions/log--input--herbicide_or_pesticide/schema.json?job=copy_schemas'
        }
    },
    {
        type: 'taxonomy_term--log_category',
        id: taxonomy_term__log_category__pest_disease_control__uuid,
        attributes: {
            name: 'pest_disease_control'
        }
    },
    {
        type: 'quantity--standard',
        id: quantity__standard__area_percentage_uuid,
        attributes: {
            label: 'area'
        }, 
        relationships: {
            units: {
                data: [
                    {
                        type: 'taxonomy_term--unit',
                        id: taxonomy_term__unit__percentage_uuid
                    }
                ]
            }
        }
    },
    {
        type: 'quantity--material--active_ingredient_percent',
        id: quantity__material__active_ingredient_percent_uuid, 
        attributes: {
            label: 'active_ingredient_percent'
        },
        relationships: {
            units: { 
                data: [
                    {
                        type: 'taxonomy_term--unit',
                        id: taxonomy_term__unit__percentage_uuid
                    }
                ]
            }
        }
    },
    {
        type: 'taxonomy_term--unit',
        id: taxonomy_term__unit__percentage_uuid,
        attributes: {
            name: '%'
        }
    },
    {
        type: 'quantity--material--active_ingredient_percent',
        id: quantity__material__active_ingredient_percent_2_uuid, 
        attributes: {
            label: 'active_ingredient_percent'
        },
        relationships: {
            units: { 
                data: [
                    {
                        type: 'taxonomy_term--unit',
                        id: taxonomy_term__unit__percentage_2_uuid
                    }
                ]
            }
        }
    },
    {
        type: 'taxonomy_term--unit',
        id: taxonomy_term__unit__rate_uuid, 
        attributes: {
            name: 'cubic_ft'
        }
    },
    {
        type: 'taxonomy_term--material_type',
        id: taxonomy_term__material_type__fertilizer_uuid,
        attributes: {
            name: 'ammonia_sulfate'
        }
    }
];


let new_example_3 = new builder.ArrayDataOrganizer({
    exampleArray: createHerbExam_wrongTaxTermMatType,
    predefinedConventions: [ JSON.parse( fs.readFileSync(directory_name) ) ], 
    baseSchemataFolder: base_schema_dir_name
});

await new_example_3.initialize();
lastInstance3 = new_example_3.instances[new_example_3.instances.length - 1];

let assembledData3 = lastInstance3.assembled_entity;
assembledData3['taxonomy_term--log_category--pest_disease_control'] = assembledData3.ambiguousEntities.find( d => d.possibleOverlays.includes('taxonomy_term--log_category--pest_disease_control') ).entity;

convention.addExample({ object: assembledData3, is_valid_example: false });


// quantity--material--active_ingredient_percentage missing
let createHerbExam_noQuantityMaterialIngredientPercent = [
    {
        type: 'log--input',
        id: assetID,
        attributes: {
            name: 'example herbicide log',
            status: 'done',
            timestamp: ( new Date() ).toISOString()
        },
        relationships: { 
            convention: {
                data: [{
                    type: 'taxonomy_term--convention',
                    id: conventionID,
                }]
            },
            asset: {
                data: [{
                    type: 'asset--plant',
                    id: asset__plant__planting__uuid,
                }]
            }, 
            category: {
                data: [{
                    type: 'taxonomy_term--log_category',
                    id: taxonomy_term__log_category__pest_disease_control__uuid
                }]
            }, 
            quantity: {
                data: [
                    {
                        type: 'quantity--standard',
                        id: quantity__standard__area_percentage_uuid
                    }, 
                    {
                        type: 'quantity--material',
                        id: quantity__material__rate_uuid
                    },
                    {
                        type: 'quantity--material',
                        id: quantity__material__active_ingredient_percent_uuid
                    }
                ]
            }
        }
    },
    {
        type: 'taxonomy_term--convention',
        id: conventionID,
        attributes: {
            name: 'Herbicide/pesticide',
            external_uri:'https://gitlab.com/api/v4/projects/44311021/jobs/artifacts/main/raw/output/collection/conventions/log--input--herbicide_or_pesticide/schema.json?job=copy_schemas'
        }
    },
    {
        type: 'taxonomy_term--log_category',
        id: taxonomy_term__log_category__pest_disease_control__uuid,
        attributes: {
            name: 'pest_disease_control'
        }
    },
    {
        type: 'quantity--standard',
        id: quantity__standard__area_percentage_uuid,
        attributes: {
            label: 'area'
        }, 
        relationships: {
            units: {
                data: [
                    {
                        type: 'taxonomy_term--unit',
                        id: taxonomy_term__unit__percentage_uuid
                    }
                ]
            }
        }
    },
    {
        type: 'quantity--material--active_ingredient_percent',
        id: quantity__material__active_ingredient_percent_uuid, 
        attributes: {
            label: 'active_ingredient_percent'
        },
        relationships: {
            units: { 
                data: [
                    {
                        type: 'taxonomy_term--unit',
                        id: taxonomy_term__unit__percentage_uuid
                    }
                ]
            }
        }
    },
    {
        type: 'taxonomy_term--unit',
        id: taxonomy_term__unit__percentage_uuid,
        attributes: {
            name: '%'
        }
    },
    {
        type: 'quantity--material',
        id: quantity__material__rate_uuid,
        attributes: {
            label: 'application_rate',
            measure: 'rate'
        }, 
        relationships: {
            units: {
                data: [
                    {
                        type: 'taxonomy_term--unit',
                        id: taxonomy_term__unit__rate_uuid
                    }
                ]
            }, 
            material_type: {
                data: [
                    {
                        type: 'taxonomy_term--material_type',
                        id: taxonomy_term__material_type__herbicide_or_pesticide__uuid
                    }
                ]
            }
        }
    },
    {
        type: 'taxonomy_term--unit',
        id: taxonomy_term__unit__rate_uuid, 
        attributes: {
            name: 'cubic_ft'
        }
    },
    {
        type: 'taxonomy_term--material_type',
        id: taxonomy_term__material_type__herbicide_or_pesticide__uuid,
        attributes: {
            name: 'roundup'
        }
    }
];

// assembling 
let new_example_4 = new builder.ArrayDataOrganizer({
    exampleArray: createHerbExam_noQuantityMaterialIngredientPercent,
    predefinedConventions: [ JSON.parse( fs.readFileSync(directory_name) ) ], 
    baseSchemataFolder: base_schema_dir_name
});

await new_example_4.initialize();
lastInstance_4 = new_example_4.instances[new_example_4.instances.length - 1];

let assembledData = lastInstance_4.assembled_entity;
assembledData['taxonomy_term--log_category--pest_disease_control'] = lastInstance_4.ambiguousEntities.find( d => d.possibleOverlays.includes('taxonomy_term--log_category--pest_disease_control') ).entity;

convention.addExample({ object: assembledData, is_valid_example: true });