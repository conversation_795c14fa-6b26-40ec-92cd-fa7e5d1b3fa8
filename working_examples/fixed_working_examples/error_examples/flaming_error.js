const builder = require('convention_builder');
const { randomUUID } = require('crypto');

/* 
This files contains: 
    - A good example for flaming
    - Bad example with: 
        - does not have a log category 
        - has wrong log category (name is for log--category--amendment)
*/


// taxonomy_term__log_category__weed_control
// quantity__standard__area_percentage

// Building examples for testing 

// ** don't forget this ** 
//__dirname = path.resolve();

let directory_name = path.join(__dirname, '..', '..', '..', 'output', 'collection', 'conventions', 'log--activity--flaming', 'object.json');
let base_schema_dir_name = path.join(__dirname, '..', '..', '..', 'input', 'collection');

let intendedConvention = JSON.parse( fs.readFileSync(directory_name) );
intendedConvention.gitlabProjectId = 'undefined';

let convention = new builder.ConventionSchema( intendedConvention );

let assetID = randomUUID();
let conventionID = randomUUID();
let asset__plant__planting_uuid = randomUUID();
let taxonomy_term__log_category__weed_control_uuid = randomUUID();
let quantity__standard__area_percentage_uuid = randomUUID();
let taxonomy_term__unit__percentage_uuid = randomUUID();

// good example (for reference and formatting purposes)
let createFlameExam = [
    {
        type: 'log--activity',
        id: assetID,
        attributes: {
            name: 'new_flaming_example',
            status: 'done',
            timestamp: ( new Date() ).toISOString()
        },
        relationships: { 
            convention: {
                data: [{
                    type: 'taxonomy_term--convention',
                    id: conventionID,
                }]
            },
            asset: {
                data: [{
                    type: 'asset--plant',
                    id: asset__plant__planting_uuid,
                }]
            }, 
            category: {
                data: [{
                    type: 'taxonomy_term--log_category',
                    id: taxonomy_term__log_category__weed_control_uuid,
                }]
            }, 
            quantity: {
                data: [{
                    type: 'quantity--standard',
                    id: quantity__standard__area_percentage_uuid,
                }]
            }
        }
    },
    {
        type: 'taxonomy_term--convention',
        id: conventionID,
        attributes: {
            name: 'Flaming',
            external_uri: 'https://gitlab.com/api/v4/projects/44311021/jobs/artifacts/main/raw/output/collection/conventions/log--activity--flaming/schema.json?job=copy_schemas'
        }
    }, 
    {
        type: 'taxonomy_term--log_category',
        id: taxonomy_term__log_category__weed_control_uuid,
        attributes: {
            name: "weed_control"
        },
    },
    {
        type: 'quantity--standard--area_percentage',
        id: quantity__standard__area_percentage_uuid,
        attributes: {
            label: "area"
        },
        relationships: {
            units: {
                data: [{
                    type: 'taxonomy_term__unit__percentage_uuid',
                    id: taxonomy_term__unit__percentage_uuid
                }]
            }
        }
    }, 
    {
        type: 'taxnomy_term--unit--%',
        id: taxonomy_term__unit__percentage_uuid,
        attributes: {
            name: '%'
        },
    }
];


// assembling 
let new_example = new builder.ArrayDataOrganizer({
    exampleArray: createFlameExam,
    predefinedConventions: [ JSON.parse( fs.readFileSync(directory_name) ) ], 
    baseSchemataFolder: base_schema_dir_name
});

await new_example.initialize();
lastInstance = new_example.instances[new_example.instances.length - 1];

let assembledData = lastInstance.assembled_entity;

assembledData['taxonomy_term--log_category--weed_control'] = lastInstance.ambiguousEntities.find( d => d.possibleOverlays.includes('taxonomy_term--log_category--weed_control') ).entity;

convention.addExample({ object: assembledData, is_valid_example: true });

// Bad Example 1
// does not have a log category 
let createFlameExample_noLogCategory = [
    {
        type: 'log--activity',
        id: assetID,
        attributes: {
            name: 'new_flaming_example',
            status: 'done',
            timestamp: ( new Date() ).toISOString()
        },
        relationships: { 
            convention: {
                data: [{
                    type: 'taxonomy_term--convention',
                    id: conventionID,
                }]
            },
            asset: {
                data: [{
                    type: 'asset--plant',
                    id: asset__plant__planting_uuid,
                }]
            }, 
            quantity: {
                data: [{
                    type: 'quantity--standard',
                    id: quantity__standard__area_percentage_uuid,
                }]
            }
        }
    },
    {
        type: 'taxonomy_term--convention',
        id: conventionID,
        attributes: {
            name: 'Flaming',
            external_uri: 'https://gitlab.com/api/v4/projects/44311021/jobs/artifacts/main/raw/output/collection/conventions/log--activity--flaming/schema.json?job=copy_schemas'
        }
    }, 
    {
        type: 'quantity--standard--area_percentage',
        id: quantity__standard__area_percentage_uuid,
        attributes: {
            label: "area"
        },
        relationships: {
            units: {
                data: [{
                    type: 'taxonomy_term__unit__percentage_uuid',
                    id: taxonomy_term__unit__percentage_uuid
                }]
            }
        }
    }, 
    {
        type: 'taxnomy_term--unit--%',
        id: taxonomy_term__unit__percentage_uuid,
        attributes: {
            name: '%'
        },
    }
];

// assembling 2
let new_example2 = new builder.ArrayDataOrganizer({
    exampleArray: createFlameExample_noLogCategory,
    predefinedConventions: [ JSON.parse( fs.readFileSync(directory_name) ) ], 
    baseSchemataFolder: base_schema_dir_name
});

await new_example2.initialize();
lastInstance2 = new_example2.instances[new_example2.instances.length - 1];

let assembledData2 = lastInstance2.assembled_entity;

convention.addExample({ object: assembledData2, is_valid_example: false });

// Bad Example 2 
// has wrong log category (name is for log--category--amendment)
let createFlameExam_wrongLogCategory = [
    {
        type: 'log--activity',
        id: assetID,
        attributes: {
            name: 'new_flaming_example',
            status: 'done',
            timestamp: ( new Date() ).toISOString()
        },
        relationships: { 
            convention: {
                data: [{
                    type: 'taxonomy_term--convention',
                    id: conventionID,
                }]
            },
            asset: {
                data: [{
                    type: 'asset--plant',
                    id: asset__plant__planting_uuid,
                }]
            }, 
            category: {
                data: [{
                    type: 'taxonomy_term--log_category',
                    id: taxonomy_term__log_category__weed_control_uuid,
                }]
            }, 
            quantity: {
                data: [{
                    type: 'quantity--standard',
                    id: quantity__standard__area_percentage_uuid,
                }]
            }
        }
    },
    {
        type: 'taxonomy_term--convention',
        id: conventionID,
        attributes: {
            name: 'Flaming',
            external_uri: 'https://gitlab.com/api/v4/projects/44311021/jobs/artifacts/main/raw/output/collection/conventions/log--activity--flaming/schema.json?job=copy_schemas'
        }
    }, 
    {
        type: 'taxonomy_term--log_category',
        id: taxonomy_term__log_category__weed_control_uuid,
        attributes: {
             name: "amendment"
        },
    },
    {
        type: 'quantity--standard--area_percentage',
        id: quantity__standard__area_percentage_uuid,
        attributes: {
            label: "area"
        },
        relationships: {
            units: {
                data: [{
                    type: 'taxonomy_term__unit__percentage_uuid',
                    id: taxonomy_term__unit__percentage_uuid
                }]
            }
        }
    }, 
    {
        type: 'taxnomy_term--unit--%',
        id: taxonomy_term__unit__percentage_uuid,
        attributes: {
            name: '%'
        },
    }
];

// assembling 3
let new_example3 = new builder.ArrayDataOrganizer({
    exampleArray: createFlameExam_wrongLogCategory,
    predefinedConventions: [ JSON.parse( fs.readFileSync(directory_name) ) ], 
    baseSchemataFolder: base_schema_dir_name
});

await new_example3.initialize();
lastInstance3 = new_example3.instances[new_example3.instances.length - 1];

let assembledData3 = lastInstance3.assembled_entity;

convention.addExample({ object: assembledData3, is_valid_example: false });

// test all examples with the testExamples method.
let test = convention.testExamples();
test.success;

// Store your object
let file_directory_name = path.join(__dirname, '..', '..', '..', 'working_examples', 'processed_examples', 'log_activity_flaming_.json');
fs.writeFileSync( file_directory_name, JSON.stringify(assembledData) );  