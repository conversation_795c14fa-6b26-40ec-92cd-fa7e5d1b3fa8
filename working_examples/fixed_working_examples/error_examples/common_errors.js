

// Global Errors // 
// These are errors which are common in all logs // 

// It could be the case that these errors are "bumped out" and iterated on ... 
// The approach would be to iterate on all subfolders in the folder and check the attribute 
// Similar approaches appear in other areas of this code 

// ** It has been determined for now, that this is not crucial for functionality and will be addressed in future iterations. ** 

// Not required for mowing: 
//      - taxonomy_term--log_category 

//// log--activity
// id 
// attributes 
//      timestamp 
//      status 
// relationships
//      convention 
//      category
//          
// 
// 
// attached entity convention_taxonomy
//      id
//      attributes
//          name = constant value
// attached entity taxonomy_term--log_category (this isn't required for some)
//      id
//      attributes 
//      name = constant value


// Different ones (take these out)
// These all exist as separate files,,, 

// log harvest harvest 
// required taxonomy_term--unit--yield

// herb pest 
// taxonomy_term--material_type--herbicide_or_pesticide
// quantity--material--active_ingredient_percent
// quantity--material--rate
// taxonomy_term--unit--rate
// taxonomy_term--log_category--pest_disease_control

// lime 
// taxonomy_term--material_type--lime

// fertilizer 
// taxonomy_term--material_type--fertilizer
