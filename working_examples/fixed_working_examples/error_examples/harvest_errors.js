const builder = require('convention_builder');
const { randomUUID } = require('crypto');

/* 
This files contains: 
    - A good example for harvest
    - Bad Examples: 
        - TBD No taxonomy_term--unit--yield
        - TBD taxonomy_term--unit--yeld wrong name
*/

// Required Terms 
// log--harvest--harvest
    // id 
    // attributes 
        // timestamp
        // status
    // relationships 
        // convention
        // asset 
            // asset--plant
        // category
            // taxonomy_term--log_category

// convention_taxonomy
    // id
    // attributes
        //name = Harvest

// asset--plant--planting
    // id
    // attributes
        // name
        // status = active

// taxonomy_term--log_category
    // id
    // name

// taxonomy_term--unit--yield
    // id
    // attributes
        // name = [List of possible values] // see harvestLogSurvey.js



// Building examples for testing 

// ** don't forget this ** 
//__dirname = path.resolve();

let directory_name = path.join(__dirname, '..', '..', '..', 'output', 'collection', 'conventions', 'log--harvest--harvest', 'object.json');
let base_schema_dir_name = path.join(__dirname, '..', '..', '..', 'input', 'collection');

let intendedConvention = JSON.parse( fs.readFileSync(directory_name) );
intendedConvention.gitlabProjectId = 'undefined';

let convention = new builder.ConventionSchema( intendedConvention );

let assetID = randomUUID();
let conventionID = randomUUID();

let asset__plant__planting__uuid = randomUUID();

let quantity__standard__yield_uuid = randomUUID();
let quantity__standard__residue_removed_uuid = randomUUID();
let quantity__standard__area_percentage_uuid = randomUUID();

let taxonomy_term__log_category__harvest_uuid = randomUUID();
let taxonomy_term__unit__percentage_uuid = randomUUID();
let taxonomy_term__unit__yield_uuid = randomUUID();


// good example (for reference and formatting purposes)
let createHarvestExam = [
    {
        type: 'log--harvest',
        id: assetID,
        attributes: {
            name: 'example harvest log',
            status: 'done',
            timestamp: ( new Date() ).toISOString()
        },
        relationships: { 
            convention: {
                data: [{
                    type: 'taxonomy_term--convention',
                    id: conventionID,
                }]
            },
            asset: {
                data: [{
                    type: 'asset--plant',
                    id: asset__plant__planting__uuid,
                }]
            }, 
            category: {
                data: [{
                    type: 'taxonomy_term--log_category',
                    id: taxonomy_term__log_category__harvest_uuid
                }]
            }, 
            quantity: {
                data: [
                    {
                        type: 'quantity--standard',
                        id: quantity__standard__yield_uuid
                    }, 
                    {
                        type: 'quantity--standard',
                        id: quantity__standard__residue_removed_uuid
                    },
                    {
                        type: 'quantity--standard',
                        id: quantity__standard__area_percentage_uuid
                    }
                ]
            }
        }
    },
    {
        type: 'taxonomy_term--convention',
        id: conventionID,
        attributes: {
            name: 'Harvest',
            external_uri:'https://gitlab.com/api/v4/projects/44311021/jobs/artifacts/main/raw/output/collection/conventions/log--harvest--harvest/schema.json?job=copy_schemas'
        }
    },
    {
        type: 'taxonomy_term--log_category',
        id: taxonomy_term__log_category__harvest_uuid,
        attributes: {
            name: 'harvest'
        }
    },
    {
        type: 'quantity--standard',
        id: quantity__standard__area_percentage_uuid, 
        attributes: {
            label: 'area'
        },
        relationships: {
            units: {
                data: [
                    {
                        type: 'taxonomy_term--unit',
                        id: taxonomy_term__unit__percentage_uuid
                    }, 
                ]
            }
        }
    },
    {
        type: 'taxonomy_term',
        id: taxonomy_term__unit__percentage_uuid,
        attributes: {
            name: '%'
        },
    },
    {
        type: 'quantity--standard',
        id: quantity__standard__residue_removed_uuid,
        attributes: {
            label: 'residue_removed'
        },
        relationships: {
            units: {
                data: [
                    {
                        type: 'taxonomy_term--unit',
                        id: taxonomy_term__unit__percentage_uuid
                    }, 
                ]
            }
        }
    },
    {
        type: 'quantity--standard',
        id: quantity__standard__yield_uuid,
        attributes: {
            label: 'yield'
        },
        relationships: {
            units: {
                data: [
                    {
                        type: 'taxonomy_term--unit',
                        id: taxonomy_term__unit__yield_uuid
                    }, 
                ]
            }
        }
    },
    {
        type: 'taxonomy_term--unit',
        id: taxonomy_term__unit__yield_uuid,
        attributes: {
            name: 'bu' // must be taxonomy_term__unit__yield.setEnum valuesArray in harvestLogSurvey.js 
        },
    }
];

// assembling 
let new_example = new builder.ArrayDataOrganizer({
    exampleArray: createHarvestExam,
    predefinedConventions: [ JSON.parse( fs.readFileSync(directory_name) ) ], 
    baseSchemataFolder: base_schema_dir_name
});

await new_example.initialize();
lastInstance = new_example.instances[new_example.instances.length - 1];

let assembledData = lastInstance.assembled_entity;
convention.addExample({ object: assembledData, is_valid_example: true });

// Bad Examples 

// No Taxonomy Term Unit Yield 
let createHarvestExam_noTaxTermYield = [
    {
        type: 'log--harvest',
        id: assetID,
        attributes: {
            name: 'example harvest log',
            status: 'done',
            timestamp: ( new Date() ).toISOString()
        },
        relationships: { 
            convention: {
                data: [{
                    type: 'taxonomy_term--convention',
                    id: conventionID,
                }]
            },
            asset: {
                data: [{
                    type: 'asset--plant',
                    id: asset__plant__planting__uuid,
                }]
            }, 
            category: {
                data: [{
                    type: 'taxonomy_term--log_category',
                    id: taxonomy_term__log_category__harvest_uuid
                }]
            }, 
            quantity: {
                data: [
                    /*
                    {
                        type: 'quantity--standard',
                        id: quantity__standard__yield_uuid
                    }, 
                    */
                    {
                        type: 'quantity--standard',
                        id: quantity__standard__residue_removed_uuid
                    },
                    {
                        type: 'quantity--standard',
                        id: quantity__standard__area_percentage_uuid
                    }
                ]
            }
        }
    },
    {
        type: 'taxonomy_term--convention',
        id: conventionID,
        attributes: {
            name: 'Harvest',
            external_uri:'https://gitlab.com/api/v4/projects/44311021/jobs/artifacts/main/raw/output/collection/conventions/log--harvest--harvest/schema.json?job=copy_schemas'
        }
    },
    {
        type: 'taxonomy_term--log_category',
        id: taxonomy_term__log_category__harvest_uuid,
        attributes: {
            name: 'harvest'
        }
    },
    {
        type: 'quantity--standard',
        id: quantity__standard__area_percentage_uuid, 
        attributes: {
            label: 'area'
        },
        relationships: {
            units: {
                data: [
                    {
                        type: 'taxonomy_term--unit',
                        id: taxonomy_term__unit__percentage_uuid
                    }, 
                ]
            }
        }
    },
    {
        type: 'taxonomy_term',
        id: taxonomy_term__unit__percentage_uuid,
        attributes: {
            name: '%'
        },
    },
    {
        type: 'quantity--standard',
        id: quantity__standard__residue_removed_uuid,
        attributes: {
            label: 'residue_removed'
        },
        relationships: {
            units: {
                data: [
                    {
                        type: 'taxonomy_term--unit',
                        id: taxonomy_term__unit__percentage_uuid
                    }, 
                ]
            }
        }
    },
    {
        type: 'quantity--standard',
        id: quantity__standard__yield_uuid,
        attributes: {
            label: 'yield'
        },
        relationships: {
            units: {
                data: [
                    {
                        type: 'taxonomy_term--unit',
                        id: taxonomy_term__unit__yield_uuid
                    }, 
                ]
            }
        }
    },
    /*
    {
        type: 'taxonomy_term--unit',
        id: taxonomy_term__unit__yield_uuid,
        attributes: {
            name: 'bu' // must be taxonomy_term__unit__yield.setEnum valuesArray in harvestLogSurvey.js 
        },
    }
    */
];

// assembling 
let new_example2 = new builder.ArrayDataOrganizer({
    exampleArray: createHarvestExam_noTaxTermYield,
    predefinedConventions: [ JSON.parse( fs.readFileSync(directory_name) ) ], 
    baseSchemataFolder: base_schema_dir_name
});

await new_example2.initialize();
lastInstance2 = new_example2.instances[new_example2.instances.length - 1];

let assembledData2 = lastInstance2.assembled_entity;
convention.addExample({ object: assembledData2, is_valid_example: false });

// Bad Example 2
// Wrong name taxonomy term unit yield 

let createHarvestExam_wrongNameTaxTermYield = [
    {
        type: 'log--harvest',
        id: assetID,
        attributes: {
            name: 'example harvest log',
            status: 'done',
            timestamp: ( new Date() ).toISOString()
        },
        relationships: { 
            convention: {
                data: [{
                    type: 'taxonomy_term--convention',
                    id: conventionID,
                }]
            },
            asset: {
                data: [{
                    type: 'asset--plant',
                    id: asset__plant__planting__uuid,
                }]
            }, 
            category: {
                data: [{
                    type: 'taxonomy_term--log_category',
                    id: taxonomy_term__log_category__harvest_uuid
                }]
            }, 
            quantity: {
                data: [{
                        type: 'quantity--standard',
                        id: quantity__standard__yield_uuid
                    }, 
                    {
                        type: 'quantity--standard',
                        id: quantity__standard__residue_removed_uuid
                    },
                    {
                        type: 'quantity--standard',
                        id: quantity__standard__area_percentage_uuid
                    }
                ]
            }
        }
    },
    {
        type: 'taxonomy_term--convention',
        id: conventionID,
        attributes: {
            name: 'Harvest',
            external_uri:'https://gitlab.com/api/v4/projects/44311021/jobs/artifacts/main/raw/output/collection/conventions/log--harvest--harvest/schema.json?job=copy_schemas'
        }
    },
    {
        type: 'taxonomy_term--log_category',
        id: taxonomy_term__log_category__harvest_uuid,
        attributes: {
            name: 'harvest'
        }
    },
    {
        type: 'quantity--standard',
        id: quantity__standard__area_percentage_uuid, 
        attributes: {
            label: 'area'
        },
        relationships: {
            units: {
                data: [
                    {
                        type: 'taxonomy_term--unit',
                        id: taxonomy_term__unit__percentage_uuid
                    }, 
                ]
            }
        }
    },
    {
        type: 'taxonomy_term',
        id: taxonomy_term__unit__percentage_uuid,
        attributes: {
            name: '%'
        },
    },
    {
        type: 'quantity--standard',
        id: quantity__standard__residue_removed_uuid,
        attributes: {
            label: 'residue_removed'
        },
        relationships: {
            units: {
                data: [
                    {
                        type: 'taxonomy_term--unit',
                        id: taxonomy_term__unit__percentage_uuid
                    }, 
                ]
            }
        }
    },
    {
        type: 'quantity--standard',
        id: quantity__standard__yield_uuid,
        attributes: {
            label: 'yield'
        },
        relationships: {
            units: {
                data: [
                    {
                        type: 'taxonomy_term--unit',
                        id: taxonomy_term__unit__yield_uuid
                    }, 
                ]
            }
        }
    },
    {
        type: 'taxonomy_term--unit',
        id: taxonomy_term__unit__yield_uuid,
        attributes: {
            name: 'banana feet'
        },
    }
];

// assembling 
let new_example3 = new builder.ArrayDataOrganizer({
    exampleArray: createHarvestExam_wrongNameTaxTermYield,
    predefinedConventions: [ JSON.parse( fs.readFileSync(directory_name) ) ], 
    baseSchemataFolder: base_schema_dir_name
});

await new_example3.initialize();
lastInstance3 = new_example3.instances[new_example3.instances.length - 1];

let assembledData3 = lastInstance3.assembled_entity;
convention.addExample({ object: assembledData3, is_valid_example: false });