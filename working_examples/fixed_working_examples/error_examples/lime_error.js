const builder = require('convention_builder');
const { randomUUID } = require('crypto');

/* 
This files contains: 
    - A good example for lime
    - Bad example with: 
	    - taxonomy_term--material_type--lime missing 
        - taxonomy_term--material_type--lime wrong name
*/


// Required Terms 
// log--input--lime
    // id 
    // attributes 
        // timestamp
        // status
    // relationships 
        // convention
        // asset 
            // asset--plant
        // category
            // taxonomy_term--log_category or taxonomy_term--log_category--amendment

    // taxonomy_term--material_type--lime



// Building examples for testing 

// ** don't forget this ** 
//__dirname = path.resolve();

let directory_name = path.join(__dirname, '..', '..', '..', 'output', 'collection', 'conventions', 'log--input--lime', 'object.json');
let base_schema_dir_name = path.join(__dirname, '..', '..', '..', 'input', 'collection');

let intendedConvention = JSON.parse( fs.readFileSync(directory_name) );
intendedConvention.gitlabProjectId = 'undefined';

let convention = new builder.ConventionSchema( intendedConvention );

let assetID = randomUUID();
let conventionID = randomUUID();

let asset__plant__planting_uuid = randomUUID();

let taxonomy_term__log_category__amendment_uuid = randomUUID();
let taxonomy_term__unit__percentage_uuid = randomUUID();
let taxonomy_term__unit__rate_uuid = randomUUID();
let taxonomy_term__unit__lime_uuid = randomUUID();
let taxonomy_term__material_type__lime_uuid = randomUUID();
let taxonomy_term__material_type__lime_specific_uuid = randomUUID();

let quantity__standard__area_percentage_uuid = randomUUID();
let quantity__material__lime_uuid = randomUUID();
let quantity__material__rate_uuid = randomUUID();

// Good Example 
let createLimeExam = [
    {
        type: 'log--input',
        id: assetID,
        attributes: {
            name: 'example lime log',
            status: 'done',
            timestamp: ( new Date() ).toISOString()
        },
        relationships: { 
            convention: {
                data: [{
                    type: 'taxonomy_term--convention',
                    id: conventionID,
                }]
            },
            asset: {
                data: [{
                    type: 'asset--plant',
                    id: asset__plant__planting_uuid,
                }]
            }, 
            category: {
                data: [{
                    type: 'taxonomy_term--log_category',
                    id: taxonomy_term__log_category__amendment_uuid,
                }]
            }, 
            quantity: {
                data: [{
                    type: "quantity--standard",
                    id: quantity__standard__area_percentage_uuid
                },
                {
                    type: "quantity--standard",
                    id: quantity__material__lime_uuid
                }]
            }
        }
    },
    {
        type: 'taxonomy_term--convention',
        id: conventionID,
        attributes: {
            name: 'Lime',
            external_uri:'https://gitlab.com/api/v4/projects/44311021/jobs/artifacts/main/raw/output/collection/conventions/log--input--lime/schema.json?job=copy_schemas'
        }
    }, 
    {
        type: 'quantity--standard',
        id: quantity__standard__area_percentage_uuid,
        attributes: {
            label: "area"
        },
        relationships: {
            units: { data: [
                {
                    type:"taxonomy_term--unit",
                    id: taxonomy_term__unit__percentage_uuid
                }
            ] }
        }
    },
    {
        type: 'taxonomy_term--unit',
        id: taxonomy_term__unit__percentage_uuid,
        attributes: {
            name: "%"
        },
    }, 
    {
        type: 'quantity--material',
        id: quantity__material__rate_uuid,
        attributes: {
            label: "lime",
            value: "value"
        },
        relationships: {
            units: { data: [
                {
                    type:"taxonomy_term--unit",
                    id: taxonomy_term__unit__rate_uuid
                }
            ] },
            material_type: { data: [
                {
                    type:"taxonomy_term--material_type",
                    id: taxonomy_term__material_type__lime_uuid 
                },
                {
                    type:"taxonomy_term--material_type",
                    id: taxonomy_term__material_type__lime_specific_uuid
                }
            ]}
        }
    },
    {
        type: 'taxonomy_term--unit',
        id: taxonomy_term__unit__lime_uuid,
        attributes: {
            name: "lbs_acres"
        },
    },
    {
        type: 'taxonomy_term--unit',
        id: taxonomy_term__material_type__lime_uuid,
        attributes: {
            name: "lime"
        }
    },
    {
        type: 'taxonomy_term--material_type',
        id: taxonomy_term__material_type__lime_specific_uuid,
        attributes: {
            name: "calcitic_lime"
        }
    },
    {
        type: 'taxonomy_term--log_category',
        id: taxonomy_term__log_category__amendment_uuid,
        attributes: {
            name: "amendment"
        }
    }
];

// assembling 
let new_example = new builder.ArrayDataOrganizer({
    exampleArray: createLimeExam,
    predefinedConventions: [ JSON.parse( fs.readFileSync(directory_name) ) ], 
    baseSchemataFolder: base_schema_dir_name
});

await new_example.initialize();
lastInstance = new_example.instances[new_example.instances.length - 1];

let assembledData = lastInstance.assembled_entity;
convention.addExample({ object: assembledData, is_valid_example: true });


// Bad Examples 

// No taxonomy_term--material_type--lime
// Wrong taxonomy_term--material_type 

let createLimeExamTaxonomyTermMissing = [
    {
        type: 'log--input',
        id: assetID,
        attributes: {
            name: 'example lime log',
            status: 'done',
            timestamp: ( new Date() ).toISOString()
        },
        relationships: { 
            convention: {
                data: [{
                    type: 'taxonomy_term--convention',
                    id: conventionID,
                }]
            },
            asset: {
                data: [{
                    type: 'asset--plant',
                    id: asset__plant__planting_uuid,
                }]
            }, 
            category: {
                data: [{
                    type: 'taxonomy_term--log_category',
                    id: taxonomy_term__log_category__amendment_uuid,
                }]
            }, 
            quantity: {
                data: [{
                    type: "quantity--standard",
                    id: quantity__standard__area_percentage_uuid
                },
                {
                    type: "quantity--standard",
                    id: quantity__material__lime_uuid
                }]
            }
        }
    },
    {
        type: 'taxonomy_term--convention',
        id: conventionID,
        attributes: {
            name: 'Lime',
            external_uri:'https://gitlab.com/api/v4/projects/44311021/jobs/artifacts/main/raw/output/collection/conventions/log--input--lime/schema.json?job=copy_schemas'
        }
    }, 
    {
        type: 'quantity--standard',
        id: quantity__standard__area_percentage_uuid,
        attributes: {
            label: "area"
        },
        relationships: {
            units: { data: [
                {
                    type:"taxonomy_term--unit",
                    id: taxonomy_term__unit__percentage_uuid
                }
            ] }
        }
    },
    {
        type: 'taxonomy_term--unit',
        id: taxonomy_term__unit__percentage_uuid,
        attributes: {
            name: "%"
        },
    }, 
    {
        type: 'quantity--material',
        id: quantity__material__rate_uuid,
        attributes: {
            label: "lime",
            value: "value"
        },
        relationships: {
            units: { data: [
                {
                    type:"taxonomy_term--unit",
                    id: taxonomy_term__unit__rate_uuid
                }
            ] },
            material_type: { data: [
                {
                    type:"taxonomy_term--material_type",
                    id: taxonomy_term__material_type__lime_uuid 
                },
                {
                    type:"taxonomy_term--material_type",
                    id: taxonomy_term__material_type__lime_specific_uuid
                }
            ]}
        }
    },
    {
        type: 'taxonomy_term--unit',
        id: taxonomy_term__unit__lime_uuid,
        attributes: {
            name: "lbs_acres"
        },
    },
    {
        type: 'taxonomy_term--unit',
        id: taxonomy_term__material_type__lime_uuid,
        attributes: {
            name: "lime"
        }
    },
    {
        type: 'taxonomy_term--material_type',
        id: taxonomy_term__material_type__lime_specific_uuid,
        attributes: {
            name: "calcitic_lime"
        }
    },
    {
        type: 'taxonomy_term--log_category',
        id: taxonomy_term__log_category__amendment_uuid,
        attributes: {
            name: "amendment"
        }
    }
];

// assembling 
let new_example2 = new builder.ArrayDataOrganizer({
    exampleArray: createLimeExamTaxonomyTermMissing,
    predefinedConventions: [ JSON.parse( fs.readFileSync(directory_name) ) ], 
    baseSchemataFolder: base_schema_dir_name
});

await new_example2.initialize();
lastInstance2 = new_example2.instances[new_example2.instances.length - 1];

let assembledData2 = lastInstance2.assembled_entity;
convention.addExample({ object: assembledData2, is_valid_example: false });


// Bad Example
// taxonomy_term__material_type lime name wrong
// for reference, this is a constant set to 'lime'
// for more specific options, consider taxonomy_term__material_type__lime_specific 

let createLimeExam_wrongLimeName = [
    {
        type: 'log--input',
        id: assetID,
        attributes: {
            name: 'example lime log',
            status: 'done',
            timestamp: ( new Date() ).toISOString()
        },
        relationships: { 
            convention: {
                data: [{
                    type: 'taxonomy_term--convention',
                    id: conventionID,
                }]
            },
            asset: {
                data: [{
                    type: 'asset--plant',
                    id: asset__plant__planting_uuid,
                }]
            }, 
            category: {
                data: [{
                    type: 'taxonomy_term--log_category',
                    id: taxonomy_term__log_category__amendment_uuid,
                }]
            }, 
            quantity: {
                data: [{
                    type: "quantity--standard",
                    id: quantity__standard__area_percentage_uuid
                },
                {
                    type: "quantity--standard",
                    id: quantity__material__lime_uuid
                }]
            }
        }
    },
    {
        type: 'taxonomy_term--convention',
        id: conventionID,
        attributes: {
            name: 'Lime',
            external_uri:'https://gitlab.com/api/v4/projects/44311021/jobs/artifacts/main/raw/output/collection/conventions/log--input--lime/schema.json?job=copy_schemas'
        }
    }, 
    {
        type: 'quantity--standard',
        id: quantity__standard__area_percentage_uuid,
        attributes: {
            label: "area"
        },
        relationships: {
            units: { data: [
                {
                    type:"taxonomy_term--unit",
                    id: taxonomy_term__unit__percentage_uuid
                }
            ] }
        }
    },
    {
        type: 'taxonomy_term--unit',
        id: taxonomy_term__unit__percentage_uuid,
        attributes: {
            name: "%"
        },
    }, 
    {
        type: 'quantity--material',
        id: quantity__material__rate_uuid,
        attributes: {
            label: "lime",
            value: "value"
        },
        relationships: {
            units: { data: [
                {
                    type:"taxonomy_term--unit",
                    id: taxonomy_term__unit__rate_uuid
                }
            ] },
            material_type: { data: [
                {
                    type:"taxonomy_term--material_type",
                    id: taxonomy_term__material_type__lime_uuid 
                },
                {
                    type:"taxonomy_term--material_type",
                    id: taxonomy_term__material_type__lime_specific_uuid
                }
            ]}
        }
    },
    {
        type: 'taxonomy_term--unit',
        id: taxonomy_term__unit__lime_uuid,
        attributes: {
            name: "lbs_acres"
        },
    },
    {
        type: 'taxonomy_term--unit',
        id: taxonomy_term__material_type__lime_uuid,
        attributes: {
            name: "lemon" // this should be lime here 
        }
    },
    {
        type: 'taxonomy_term--material_type',
        id: taxonomy_term__material_type__lime_specific_uuid,
        attributes: {
            name: "calcitic_lime"
        }
    },
    {
        type: 'taxonomy_term--log_category',
        id: taxonomy_term__log_category__amendment_uuid,
        attributes: {
            name: "amendment"
        }
    }
];

// assembling 
let new_example3 = new builder.ArrayDataOrganizer({
    exampleArray: createLimeExam_wrongLimeName,
    predefinedConventions: [ JSON.parse( fs.readFileSync(directory_name) ) ], 
    baseSchemataFolder: base_schema_dir_name
});

await new_example3.initialize();
lastInstance3 = new_example3.instances[new_example3.instances.length - 1];

let assembledData3 = lastInstance3.assembled_entity;
convention.addExample({ object: assembledData3, is_valid_example: false });