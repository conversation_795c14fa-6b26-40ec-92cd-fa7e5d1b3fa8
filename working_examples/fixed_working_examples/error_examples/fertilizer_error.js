const builder = require('convention_builder');
const { randomUUID } = require('crypto');

/* 
This files contains: 
    - A good example for fertilizer
    - Bad example with: 
        - does not have a log category (taxonomy_term__log_category__amendment)
        - taxonomy_term--material_type--fertilizer wrong name
        - taxonomy_term--material_type--fertilizer missing
*/


// Required Terms 
// log--input--fertilizer
    // id 
    // attributes 
        // timestamp
        // status
    // relationships 
        // convention
        // asset 
            // asset--plant
        // category
            // taxonomy_term--log_category or taxonomy_term--log_category--amendment



// Building examples for testing 

// ** don't forget this ** 
//__dirname = path.resolve();

let directory_name = path.join(__dirname, '..', '..', '..', 'output', 'collection', 'conventions', 'log--input--fertilizer', 'object.json');
let base_schema_dir_name = path.join(__dirname, '..', '..', '..', 'input', 'collection');

let intendedConvention = JSON.parse( fs.readFileSync(directory_name) );
intendedConvention.gitlabProjectId = 'undefined';

let convention = new builder.ConventionSchema( intendedConvention );

let assetID = randomUUID();
let conventionID = randomUUID();

let asset__plant__planting_uuid = randomUUID();

let quantity__standard__area_percentage_uuid = randomUUID();
let quantity__standard__moisture_percentage_uuid = randomUUID();

let quantity__material__nitrogen_percentage_uuid = randomUUID();
let quantity__material__phosphorus_percentage_uuid = randomUUID();
let quantity__material__potassium_percentage_uuid = randomUUID();
let quantity__material__inhibition_id_uuid = randomUUID();

let quantity__material__rate_uuid = randomUUID();

let taxonomy_term__unit__percentage_uuid = randomUUID();
let taxonomy_term__unit__rate_uuid = randomUUID();
let taxonomy_term__log_category__amendment_uuid = randomUUID();
let taxonomy_term__material_type__fertilizer_uuid = randomUUID();


// good example (for reference and formatting purposes)
let createFertExam = [
    {
        type: 'log--input',
        id: assetID,
        attributes: {
            name: 'example fertilizer log',
            status: 'done',
            timestamp: ( new Date() ).toISOString()
        },
        relationships: { 
            convention: {
                data: [{
                    type: 'taxonomy_term--convention',
                    id: conventionID,
                }]
            },
            asset: {
                data: [{
                    type: 'asset--plant',
                    id: asset__plant__planting_uuid,
                }]
            }, 
            category: {
                data: [{
                    type: 'taxonomy_term--log_category',
                    id: taxonomy_term__log_category__amendment_uuid,
                }]
            }, 
            quantity: {
                data: [{
                    type: "quantity--standard",
                    id: quantity__standard__area_percentage_uuid
                },
                {
                    type: "quantity--standard",
                    id: quantity__standard__moisture_percentage_uuid
                },
                {
                    type: "quantity--material",
                    id: quantity__material__nitrogen_percentage_uuid
                },
                {
                    type: "quantity--material",
                    id: quantity__material__phosphorus_percentage_uuid
                },
                {
                    type: "quantity--material",
                    id: quantity__material__potassium_percentage_uuid
                },
                {
                    type: "quantity--material",
                    id: quantity__material__inhibition_id_uuid
                },
                {
                    type: "quantity--material",
                    id: quantity__material__rate_uuid
                }]
            }
        }
    },
    {
        type: 'taxonomy_term--convention',
        id: conventionID,
        attributes: {
            name: 'Fertilizer',
            external_uri:'https://gitlab.com/api/v4/projects/44311021/jobs/artifacts/main/raw/output/collection/conventions/log--input--fertilizer/schema.json?job=copy_schemas'
        }
    }, 
    {
        type: 'taxonomy_term--log_category',
        id: taxonomy_term__log_category__amendment_uuid,
        attributes: {
            name: "amendment"
        },
    },
    {
        type: 'quantity--material',
        id: quantity__material__inhibition_id_uuid,
        attributes: {
            label: "none" //other possible value 'nitrification_inhibitor'
        },
    }, 
    {
        type: 'quantity--material',
        id: quantity__material__rate_uuid,
        relationships: {
            units: { data: [
                {
                    type:"taxonomy_term--unit",
                    id: taxonomy_term__unit__rate_uuid
                }
            ] },
            material_type: { data: [
                {
                    type:"taxonomy_term--material_type",
                    id: taxonomy_term__material_type__fertilizer_uuid 
                }
            ]}
        }
    },
    {
        type: 'taxonomy_term--material_type',
        id: taxonomy_term__material_type__fertilizer_uuid,
        attributes: {
            name: "ammonia_sulfate"
        },
    }, 
    {
        type: 'taxonomy_term--unit',
        id: taxonomy_term__unit__rate_uuid,
        attributes: {
            name: "g_acre"
        },
    }, 
    {
        type: 'quantity--standard',
        id: quantity__standard__area_percentage_uuid,
        attributes: {
            label: "area"
        },
        relationships: {
            units: { data: [
                {
                    type:"taxonomy_term--unit",
                    id: taxonomy_term__unit__percentage_uuid
                }
            ] }
        }
    }, 
    {
        type: 'taxonomy_term--unit',
        id: taxonomy_term__unit__percentage_uuid,
        attributes: {
            name: "%"
        },
    }, 
    {
        type: 'quantity--standard',
        id: quantity__standard__moisture_percentage_uuid,
        attributes: {
            label: "moisture",
            measure: "water_content"
        },
        relationships: {
            units: { data: [
                {
                    type:"taxonomy_term--unit",
                    id: taxonomy_term__unit__percentage_uuid
                }
            ] }
        }
    }, 
    {
        type: 'quantity--material',
        id: quantity__material__nitrogen_percentage_uuid,
        attributes: {
            label: "n",
            measure: "ratio"
        },
        relationships: {
            units: { data: [
                {
                    type:"taxonomy_term--unit",
                    id: taxonomy_term__unit__percentage_uuid
                }
            ] }
        }
    }, 
    {
        type: 'quantity--material',
        id: quantity__material__phosphorus_percentage_uuid,
        attributes: {
            label: "p",
            measure: "ratio"
        },
        relationships: {
            units: { data: [
                {
                    type:"taxonomy_term--unit",
                    id: taxonomy_term__unit__percentage_uuid
                }
            ] }
        }
    }, 
    {
        type: 'quantity--material',
        id: quantity__material__potassium_percentage_uuid,
        attributes: {
            label: "k",
            measure: "ratio"
        },
        relationships: {
            units: { data: [
                {
                    type:"taxonomy_term--unit",
                    id: taxonomy_term__unit__percentage_uuid
                }
            ] }
        }
    }, 
];

// assembling 
let new_example = new builder.ArrayDataOrganizer({
    exampleArray: createFertExam,
    predefinedConventions: [ JSON.parse( fs.readFileSync(directory_name) ) ], 
    baseSchemataFolder: base_schema_dir_name
});

await new_example.initialize();
lastInstance = new_example.instances[new_example.instances.length - 1];

let assembledData = lastInstance.assembled_entity;

convention.addExample({ object: assembledData, is_valid_example: true });

// Bad Examples 

// No log category 
let createFertExam_noLogCategory = [
    {
        type: 'log--input',
        id: assetID,
        attributes: {
            name: 'example fertilizer log',
            status: 'done',
            timestamp: ( new Date() ).toISOString()
        },
        relationships: { 
            convention: {
                data: [{
                    type: 'taxonomy_term--convention',
                    id: conventionID,
                }]
            },
            asset: {
                data: [{
                    type: 'asset--plant',
                    id: asset__plant__planting_uuid,
                }]
            }, 
            quantity: {
                data: [{
                    type: "quantity--standard",
                    id: quantity__standard__area_percentage_uuid
                },
                {
                    type: "quantity--standard",
                    id: quantity__standard__moisture_percentage_uuid
                },
                {
                    type: "quantity--material",
                    id: quantity__material__nitrogen_percentage_uuid
                },
                {
                    type: "quantity--material",
                    id: quantity__material__phosphorus_percentage_uuid
                },
                {
                    type: "quantity--material",
                    id: quantity__material__potassium_percentage_uuid
                },
                {
                    type: "quantity--material",
                    id: quantity__material__inhibition_id_uuid
                },
                {
                    type: "quantity--material",
                    id: quantity__material__rate_uuid
                }]
            }
        }
    },
    {
        type: 'taxonomy_term--convention',
        id: conventionID,
        attributes: {
            name: 'Fertilizer',
            external_uri:'https://gitlab.com/api/v4/projects/44311021/jobs/artifacts/main/raw/output/collection/conventions/log--input--fertilizer/schema.json?job=copy_schemas'
        }
    }, 
    {
        type: 'quantity--material',
        id: quantity__material__inhibition_id_uuid,
        attributes: {
            label: "none" //other possible value 'nitrification_inhibitor'
        },
    }, 
    {
        type: 'quantity--material',
        id: quantity__material__rate_uuid,
        relationships: {
            units: { data: [
                {
                    type:"taxonomy_term--unit",
                    id: taxonomy_term__unit__rate_uuid
                }
            ] },
            material_type: { data: [
                {
                    type:"taxonomy_term--material_type",
                    id: taxonomy_term__material_type__fertilizer_uuid 
                }
            ]}
        }
    },
    {
        type: 'taxonomy_term--material_type',
        id: taxonomy_term__material_type__fertilizer_uuid,
        attributes: {
            name: "ammonia_sulfate"
        },
    }, 
    {
        type: 'taxonomy_term--unit',
        id: taxonomy_term__unit__rate_uuid,
        attributes: {
            name: "g_acre"
        },
    }, 
    {
        type: 'quantity--standard',
        id: quantity__standard__area_percentage_uuid,
        attributes: {
            label: "area"
        },
        relationships: {
            units: { data: [
                {
                    type:"taxonomy_term--unit",
                    id: taxonomy_term__unit__percentage_uuid
                }
            ] }
        }
    }, 
    {
        type: 'taxonomy_term--unit',
        id: taxonomy_term__unit__percentage_uuid,
        attributes: {
            name: "%"
        },
    }, 
    {
        type: 'quantity--standard',
        id: quantity__standard__moisture_percentage_uuid,
        attributes: {
            label: "moisture",
            measure: "water_content"
        },
        relationships: {
            units: { data: [
                {
                    type:"taxonomy_term--unit",
                    id: taxonomy_term__unit__percentage_uuid
                }
            ] }
        }
    }, 
    {
        type: 'quantity--material',
        id: quantity__material__nitrogen_percentage_uuid,
        attributes: {
            label: "n",
            measure: "ratio"
        },
        relationships: {
            units: { data: [
                {
                    type:"taxonomy_term--unit",
                    id: taxonomy_term__unit__percentage_uuid
                }
            ] }
        }
    }, 
    {
        type: 'quantity--material',
        id: quantity__material__phosphorus_percentage_uuid,
        attributes: {
            label: "p",
            measure: "ratio"
        },
        relationships: {
            units: { data: [
                {
                    type:"taxonomy_term--unit",
                    id: taxonomy_term__unit__percentage_uuid
                }
            ] }
        }
    }, 
    {
        type: 'quantity--material',
        id: quantity__material__potassium_percentage_uuid,
        attributes: {
            label: "k",
            measure: "ratio"
        },
        relationships: {
            units: { data: [
                {
                    type:"taxonomy_term--unit",
                    id: taxonomy_term__unit__percentage_uuid
                }
            ] }
        }
    }, 
];

// assembling 
let new_example2 = new builder.ArrayDataOrganizer({
    exampleArray: createFertExam_noLogCategory,
    predefinedConventions: [ JSON.parse( fs.readFileSync(directory_name) ) ], 
    baseSchemataFolder: base_schema_dir_name
});

await new_example2.initialize();
lastInstance2 = new_example2.instances[new_example.instances.length - 1];

let assembledData2 = lastInstance2.assembled_entity;

convention.addExample({ object: assembledData2, is_valid_example: false });

// Bad Example 
// taxonomy_term--material_type--fertilizer wrong name
// // For more info see fertilizerLogSurvey taxonomy_term__material_type__fertilizer.setEnum valuesArray

let createFertExam_wrongFertName = [
    {
        type: 'log--input',
        id: assetID,
        attributes: {
            name: 'example fertilizer log',
            status: 'done',
            timestamp: ( new Date() ).toISOString()
        },
        relationships: { 
            convention: {
                data: [{
                    type: 'taxonomy_term--convention',
                    id: conventionID,
                }]
            },
            asset: {
                data: [{
                    type: 'asset--plant',
                    id: asset__plant__planting_uuid,
                }]
            }, 
            category: {
                data: [{
                    type: 'taxonomy_term--log_category',
                    id: taxonomy_term__log_category__amendment_uuid,
                }]
            }, 
            quantity: {
                data: [{
                    type: "quantity--standard",
                    id: quantity__standard__area_percentage_uuid
                },
                {
                    type: "quantity--standard",
                    id: quantity__standard__moisture_percentage_uuid
                },
                {
                    type: "quantity--material",
                    id: quantity__material__nitrogen_percentage_uuid
                },
                {
                    type: "quantity--material",
                    id: quantity__material__phosphorus_percentage_uuid
                },
                {
                    type: "quantity--material",
                    id: quantity__material__potassium_percentage_uuid
                },
                {
                    type: "quantity--material",
                    id: quantity__material__inhibition_id_uuid
                },
                {
                    type: "quantity--material",
                    id: quantity__material__rate_uuid
                }]
            }
        }
    },
    {
        type: 'taxonomy_term--convention',
        id: conventionID,
        attributes: {
            name: 'Fertilizer',
            external_uri:'https://gitlab.com/api/v4/projects/44311021/jobs/artifacts/main/raw/output/collection/conventions/log--input--fertilizer/schema.json?job=copy_schemas'
        }
    }, 
    {
        type: 'taxonomy_term--log_category',
        id: taxonomy_term__log_category__amendment_uuid,
        attributes: {
            name: "amendment"
        },
    },
    {
        type: 'quantity--material',
        id: quantity__material__inhibition_id_uuid,
        attributes: {
            label: "none" //other possible value 'nitrification_inhibitor'
        },
    }, 
    {
        type: 'quantity--material',
        id: quantity__material__rate_uuid,
        relationships: {
            units: { data: [
                {
                    type:"taxonomy_term--unit",
                    id: taxonomy_term__unit__rate_uuid
                }
            ] },
            material_type: { data: [
                {
                    type:"taxonomy_term--material_type",
                    id: taxonomy_term__material_type__fertilizer_uuid 
                }
            ]}
        }
    },
    {
        type: 'taxonomy_term--material_type',
        id: taxonomy_term__material_type__fertilizer_uuid,
        attributes: {
            name: "Banana Soup" // this should be either custom or from a list 
        },
    }, 
    {
        type: 'taxonomy_term--unit',
        id: taxonomy_term__unit__rate_uuid,
        attributes: {
            name: "g_acre"
        },
    }, 
    {
        type: 'quantity--standard',
        id: quantity__standard__area_percentage_uuid,
        attributes: {
            label: "area"
        },
        relationships: {
            units: { data: [
                {
                    type:"taxonomy_term--unit",
                    id: taxonomy_term__unit__percentage_uuid
                }
            ] }
        }
    }, 
    {
        type: 'taxonomy_term--unit',
        id: taxonomy_term__unit__percentage_uuid,
        attributes: {
            name: "%"
        },
    }, 
    {
        type: 'quantity--standard',
        id: quantity__standard__moisture_percentage_uuid,
        attributes: {
            label: "moisture",
            measure: "water_content"
        },
        relationships: {
            units: { data: [
                {
                    type:"taxonomy_term--unit",
                    id: taxonomy_term__unit__percentage_uuid
                }
            ] }
        }
    }, 
    {
        type: 'quantity--material',
        id: quantity__material__nitrogen_percentage_uuid,
        attributes: {
            label: "n",
            measure: "ratio"
        },
        relationships: {
            units: { data: [
                {
                    type:"taxonomy_term--unit",
                    id: taxonomy_term__unit__percentage_uuid
                }
            ] }
        }
    }, 
    {
        type: 'quantity--material',
        id: quantity__material__phosphorus_percentage_uuid,
        attributes: {
            label: "p",
            measure: "ratio"
        },
        relationships: {
            units: { data: [
                {
                    type:"taxonomy_term--unit",
                    id: taxonomy_term__unit__percentage_uuid
                }
            ] }
        }
    }, 
    {
        type: 'quantity--material',
        id: quantity__material__potassium_percentage_uuid,
        attributes: {
            label: "k",
            measure: "ratio"
        },
        relationships: {
            units: { data: [
                {
                    type:"taxonomy_term--unit",
                    id: taxonomy_term__unit__percentage_uuid
                }
            ] }
        }
    }, 
];

// assembling 
let new_example3 = new builder.ArrayDataOrganizer({
    exampleArray: createFertExam_wrongFertName,
    predefinedConventions: [ JSON.parse( fs.readFileSync(directory_name) ) ], 
    baseSchemataFolder: base_schema_dir_name
});

await new_example3.initialize();
lastInstance3 = new_example3.instances[new_example3.instances.length - 1];

let assembledData3 = lastInstance3.assembled_entity;

convention.addExample({ object: assembledData3, is_valid_example: false });


// Bad Example 
// // taxonomy_term--material_type--fertilizer missing

let createFertExam_missingMatTypeTTerm = [
    {
        type: 'log--input',
        id: assetID,
        attributes: {
            name: 'example fertilizer log',
            status: 'done',
            timestamp: ( new Date() ).toISOString()
        },
        relationships: { 
            convention: {
                data: [{
                    type: 'taxonomy_term--convention',
                    id: conventionID,
                }]
            },
            asset: {
                data: [{
                    type: 'asset--plant',
                    id: asset__plant__planting_uuid,
                }]
            }, 
            category: {
                data: [{
                    type: 'taxonomy_term--log_category',
                    id: taxonomy_term__log_category__amendment_uuid,
                }]
            }, 
            quantity: {
                data: [{
                    type: "quantity--standard",
                    id: quantity__standard__area_percentage_uuid
                },
                {
                    type: "quantity--standard",
                    id: quantity__standard__moisture_percentage_uuid
                },
                {
                    type: "quantity--material",
                    id: quantity__material__nitrogen_percentage_uuid
                },
                {
                    type: "quantity--material",
                    id: quantity__material__phosphorus_percentage_uuid
                },
                {
                    type: "quantity--material",
                    id: quantity__material__potassium_percentage_uuid
                },
                {
                    type: "quantity--material",
                    id: quantity__material__inhibition_id_uuid
                },
                {
                    type: "quantity--material",
                    id: quantity__material__rate_uuid
                }]
            }
        }
    },
    {
        type: 'taxonomy_term--convention',
        id: conventionID,
        attributes: {
            name: 'Fertilizer',
            external_uri:'https://gitlab.com/api/v4/projects/44311021/jobs/artifacts/main/raw/output/collection/conventions/log--input--fertilizer/schema.json?job=copy_schemas'
        }
    }, 
    {
        type: 'taxonomy_term--log_category',
        id: taxonomy_term__log_category__amendment_uuid,
        attributes: {
            name: "amendment"
        },
    },
    {
        type: 'quantity--material',
        id: quantity__material__inhibition_id_uuid,
        attributes: {
            label: "none" //other possible value 'nitrification_inhibitor'
        },
    }, 
    {
        type: 'quantity--material',
        id: quantity__material__rate_uuid,
        relationships: {
            units: { data: [
                {
                    type:"taxonomy_term--unit",
                    id: taxonomy_term__unit__rate_uuid
                }
            ] },
            material_type: { data: [
                {
                    type:"taxonomy_term--material_type",
                    id: taxonomy_term__material_type__fertilizer_uuid 
                }
            ]}
        }
    },
    /*{
        type: 'taxonomy_term--material_type',
        id: taxonomy_term__material_type__fertilizer_uuid,
        attributes: {
            name: "some_value"
        },
    }, 
    */
    {
        type: 'taxonomy_term--unit',
        id: taxonomy_term__unit__rate_uuid,
        attributes: {
            name: "g_acre"
        },
    }, 
    {
        type: 'quantity--standard',
        id: quantity__standard__area_percentage_uuid,
        attributes: {
            label: "area"
        },
        relationships: {
            units: { data: [
                {
                    type:"taxonomy_term--unit",
                    id: taxonomy_term__unit__percentage_uuid
                }
            ] }
        }
    }, 
    {
        type: 'taxonomy_term--unit',
        id: taxonomy_term__unit__percentage_uuid,
        attributes: {
            name: "%"
        },
    }, 
    {
        type: 'quantity--standard',
        id: quantity__standard__moisture_percentage_uuid,
        attributes: {
            label: "moisture",
            measure: "water_content"
        },
        relationships: {
            units: { data: [
                {
                    type:"taxonomy_term--unit",
                    id: taxonomy_term__unit__percentage_uuid
                }
            ] }
        }
    }, 
    {
        type: 'quantity--material',
        id: quantity__material__nitrogen_percentage_uuid,
        attributes: {
            label: "n",
            measure: "ratio"
        },
        relationships: {
            units: { data: [
                {
                    type:"taxonomy_term--unit",
                    id: taxonomy_term__unit__percentage_uuid
                }
            ] }
        }
    }, 
    {
        type: 'quantity--material',
        id: quantity__material__phosphorus_percentage_uuid,
        attributes: {
            label: "p",
            measure: "ratio"
        },
        relationships: {
            units: { data: [
                {
                    type:"taxonomy_term--unit",
                    id: taxonomy_term__unit__percentage_uuid
                }
            ] }
        }
    }, 
    {
        type: 'quantity--material',
        id: quantity__material__potassium_percentage_uuid,
        attributes: {
            label: "k",
            measure: "ratio"
        },
        relationships: {
            units: { data: [
                {
                    type:"taxonomy_term--unit",
                    id: taxonomy_term__unit__percentage_uuid
                }
            ] }
        }
    }, 
];

// assembling 
let new_example4 = new builder.ArrayDataOrganizer({
    exampleArray: createFertExam_missingMatTypeTTerm,
    predefinedConventions: [ JSON.parse( fs.readFileSync(directory_name) ) ], 
    baseSchemataFolder: base_schema_dir_name
});

await new_example4.initialize();
lastInstance4 = new_example4.instances[new_example4.instances.length - 1];

let assembledData4 = lastInstance4.assembled_entity;

convention.addExample({ object: assembledData4, is_valid_example: false });