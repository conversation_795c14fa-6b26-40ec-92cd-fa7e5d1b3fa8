const builder = require('convention_builder');
const { randomUUID } = require('crypto');

/* 
This files contains: 
    - A good example for irrigation
    - Bad example with: 
	    - no log category relationship 
*/


// Required Terms 
// log--input--irrigation
    // id 
    // attributes 
        // timestamp
        // status
    // relationships 
        // convention
        // asset 
            // asset--plant
        // category
            // taxonomy_term--log_category or taxonomy_term--log_category--irrigation

// convention_taxonomy
    // id
    // attributes
        //name = Irrigation

// asset--plant--planting
    // id
    // attributes
        // name
        // status = active




// Building examples for testing 

// ** don't forget this ** 
//__dirname = path.resolve();

let directory_name = path.join(__dirname, '..', '..', '..', 'output', 'collection', 'conventions', 'log--input--irrigation', 'object.json');
let base_schema_dir_name = path.join(__dirname, '..', '..', '..', 'input', 'collection');

let intendedConvention = JSON.parse( fs.readFileSync(directory_name) );
intendedConvention.gitlabProjectId = 'undefined';

let convention = new builder.ConventionSchema( intendedConvention );

let assetID = randomUUID();
let conventionID = randomUUID();

let asset__plant__planting__uuid = randomUUID();

let quantity__standard__area_percentage_uuid = randomUUID();
let quantity__material__total_water_uuid = randomUUID();
let quantity__standard__effectiveness_uuid = randomUUID();

let taxonomy_term__log_category__irrigation_uuid = randomUUID();
let taxonomy_term__unit__percentage_uuid = randomUUID();
let taxonomy_term__unit__total_water_uuid = randomUUID();
let taxonomy_term__unit__effectiveness_rating_uuid = randomUUID();


// good example (for reference and formatting purposes)
let createIrrigationExam = [
    {
        type: 'log--input',
        id: assetID,
        attributes: {
            name: 'example irrigation log',
            status: 'done',
            timestamp: ( new Date() ).toISOString()
        },
        relationships: { 
            convention: {
                data: [{
                    type: 'taxonomy_term--convention',
                    id: conventionID,
                }]
            },
            asset: {
                data: [{
                    type: 'asset--plant',
                    id: asset__plant__planting__uuid,
                }]
            }, 
            category: {
                data: [{
                    type: 'taxonomy_term--log_category',
                    id: taxonomy_term__log_category__irrigation_uuid
                }]
            }, 
            quantity: {
                data: [
                    {
                        type: 'quantity--standard',
                        id: quantity__standard__area_percentage_uuid
                    }, 
                    {
                        type: 'quantity--material',
                        id: quantity__material__total_water_uuid
                    },
                    {
                        type: 'quantity--standard',
                        id: quantity__standard__effectiveness_uuid
                    }
                ]
            }
        }
    },
    {
        type: 'taxonomy_term--convention',
        id: conventionID,
        attributes: {
            name: 'Irrigation',
            external_uri:'https://gitlab.com/api/v4/projects/44311021/jobs/artifacts/main/raw/output/collection/conventions/log--input--irrigation/schema.json?job=copy_schemas'
        }
    },
    {
        type: 'taxonomy_term--log_category',
        id: taxonomy_term__log_category__irrigation_uuid,
        attributes: {
            name: 'irrigation'
        }
    },
    {
        type: 'quantity--standard',
        id: quantity__standard__area_percentage_uuid, 
        attributes: {
            label: 'area'
        },
        relationships: {
            units: {
                data: [
                    {
                        type: 'taxonomy_term--unit',
                        id: taxonomy_term__unit__percentage_uuid
                    }, 
                ]
            }
        }
    },
    {
        type: 'taxonomy_term',
        id: taxonomy_term__unit__percentage_uuid,
        attributes: {
            name: '%'
        },
    },
    {
        type: 'quantity--material',
        id: quantity__material__total_water_uuid,
        attributes: {
            label: 'total_water'
        },
        relationships: {
            units: {
                data: [
                    {
                        type: 'taxonomy_term--unit',
                        id: taxonomy_term__unit__total_water_uuid
                    }, 
                ]
            }
        }
    },
    {
        type: 'taxonomy_term--unit',
        id: taxonomy_term__unit__total_water_uuid,
        attributes: {
            name: 'inches'
        },
    },
    {
        type: 'quantity--standard',
        id: quantity__standard__effectiveness_uuid,
        attributes: {
            label: 'effectiveness'
        },
        relationships: {
            units: {
                data: [
                    {
                        type: 'taxonomy_term--unit',
                        id: taxonomy_term__unit__effectiveness_rating_uuid
                    }, 
                ]
            }
        }
    },
    {
        type: 'taxonomy_term--unit',
        id: taxonomy_term__unit__effectiveness_rating_uuid,
        attributes: {
            name: 'Under watered (0)'
        },
    }
];

// assembling 
let new_example = new builder.ArrayDataOrganizer({
    exampleArray: createIrrigationExam,
    predefinedConventions: [ JSON.parse( fs.readFileSync(directory_name) ) ], 
    baseSchemataFolder: base_schema_dir_name
});

await new_example.initialize();
lastInstance = new_example.instances[new_example.instances.length - 1];

let assembledData = lastInstance.assembled_entity;
convention.addExample({ object: assembledData, is_valid_example: true });



// Bad Examples // 


// no log category relationship 
// is_valid = false even with a log category entity (because requirement is relationship)
let createIrrigationExam_noCatRealationship = [
    {
        type: 'log--input',
        id: assetID,
        attributes: {
            name: 'example irrigation log',
            status: 'done',
            timestamp: ( new Date() ).toISOString()
        },
        relationships: { 
            convention: {
                data: [{
                    type: 'taxonomy_term--convention',
                    id: conventionID,
                }]
            },
            asset: {
                data: [{
                    type: 'asset--plant',
                    id: asset__plant__planting__uuid,
                }]
            }, 
            /* category: {
                data: [{
                    type: 'taxonomy_term--log_category',
                    id: taxonomy_term__log_category__irrigation_uuid
                }]
            }, 
            */
            quantity: {
                data: [
                    {
                        type: 'quantity--standard',
                        id: quantity__standard__area_percentage_uuid
                    }, 
                    {
                        type: 'quantity--material',
                        id: quantity__material__total_water_uuid
                    },
                    {
                        type: 'quantity--standard',
                        id: quantity__standard__effectiveness_uuid
                    }
                ]
            }
        }
    },
    {
        type: 'taxonomy_term--convention',
        id: conventionID,
        attributes: {
            name: 'Irrigation',
            external_uri:'https://gitlab.com/api/v4/projects/44311021/jobs/artifacts/main/raw/output/collection/conventions/log--input--irrigation/schema.json?job=copy_schemas'
        }
    },
    {
        type: 'taxonomy_term--log_category',
        id: taxonomy_term__log_category__irrigation_uuid,
        attributes: {
            name: 'irrigation'
        }
    },
    {
        type: 'quantity--standard',
        id: quantity__standard__area_percentage_uuid, 
        attributes: {
            label: 'area'
        },
        relationships: {
            units: {
                data: [
                    {
                        type: 'taxonomy_term--unit',
                        id: taxonomy_term__unit__percentage_uuid
                    }, 
                ]
            }
        }
    },
    {
        type: 'taxonomy_term',
        id: taxonomy_term__unit__percentage_uuid,
        attributes: {
            name: '%'
        },
    },
    {
        type: 'quantity--material',
        id: quantity__material__total_water_uuid,
        attributes: {
            label: 'total_water'
        },
        relationships: {
            units: {
                data: [
                    {
                        type: 'taxonomy_term--unit',
                        id: taxonomy_term__unit__total_water_uuid
                    }, 
                ]
            }
        }
    },
    {
        type: 'taxonomy_term--unit',
        id: taxonomy_term__unit__total_water_uuid,
        attributes: {
            name: 'inches'
        },
    },
    {
        type: 'quantity--standard',
        id: quantity__standard__effectiveness_uuid,
        attributes: {
            label: 'effectiveness'
        },
        relationships: {
            units: {
                data: [
                    {
                        type: 'taxonomy_term--unit',
                        id: taxonomy_term__unit__effectiveness_rating_uuid
                    }, 
                ]
            }
        }
    },
    {
        type: 'taxonomy_term--unit',
        id: taxonomy_term__unit__effectiveness_rating_uuid,
        attributes: {
            name: 'Under watered (0)'
        },
    }
];

// assembling 
let new_example2 = new builder.ArrayDataOrganizer({
    exampleArray: createIrrigationExam_noCatRealationship,
    predefinedConventions: [ JSON.parse( fs.readFileSync(directory_name) ) ], 
    baseSchemataFolder: base_schema_dir_name
});

await new_example2.initialize();
lastInstance2 = new_example2.instances[new_example2.instances.length - 1];

let assembledData2 = lastInstance2.assembled_entity;
convention.addExample({ object: assembledData2, is_valid_example: false });