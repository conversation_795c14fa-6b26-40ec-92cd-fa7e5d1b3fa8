This is the readme for the working_examples folder so that I can keep track of everything that's happening in here.

I am working on generating examples. These are the types of examples that can be generated using the Yearly Farm Management Survey:

1. Create Field
2. Harvest or Termination
3. Plantings
4. Soil Disturbance
5. Herbicides and Pesticides
6. Mowing / Tedding / Raking passes
7. Flaming
8. Solarization or Tarping
9. Organic Matter Inputs
10. Fertilizer
11. Lime Application
12. Irrigation
13. Grazing
  

Guidelines for Creating these Examples:
- Dates should always be set in the past.
- Generally, try to make things look real. Things that are silly aren't great examples.
- Fill out every possible section.
- Ignore Grazing for now.
- Always create the following examples:
    - Minimal data required (only fill out reqiured)
    - Maximum data possible (fill out everything, add multiples to anything that allows multiples)
- For all activities (everything but planting, harvest, and termination)
    - Create one example with Planting and no Field
    - Create one example with Field and no Planting
    - Create one example with both Field and Planting


Current Working Examples:
(Updated 06/12/2024)

- fixed_working_examples/land_management_activities/tedding_example
    - This example is working and has been exported.

There are blockers for some examples:
    - Waiting on convention convention update
    - Waiting on apiCompose updates to the farm survey



Current Status of Other Things:
- I am having problems getting the dataset from the "create_field" option on the survey. Is this possible?

Current Matter Harvest/Termination:
- I have worked through the no field/planting and no planting/field examples
- How is harvest vs termination determined? Note for this I didn't click anything for termination (left this blank) is this a required field? should it be? does this mean the difference between harvest and termination?
- Blockers on this:
    - oneOf const 1/0 and boolean required
    - required missingPropety asset--plant--planting

- Check in to see if these shouldn't have no field/planting or no planting/field

Current Matter Plantings:
- Check if I can add a planting with no planting/field ? Probablyyyyy not? maybe actually
- I can't get the diversified plantings example to create an array that isn't malformed? Maybe I'm not filling in the proper information
- Moving on to cover crops

- Q: do each of the different types of plantings work differently? should I craft examples for each of them?

Current Matter:
- I've explored some plantings there seems to be a bit of work there and explaination, I'm exploring some other examples I've never tackled before.
- Starting with Lime Application

Current Matter Lime Application:
- taxonomy_term--material_type--lime is required and not generated in either ambiguousEntities or unrecognizedEntities... next steps?

Current Matter - Irrigation:
- Made a good fixed example! (Maybe except for the ambiguous entities actually)
- Irrigation requires plantings (asset--plant)

Current Matter - Organic Matter Inputs:
- I think i've made a good example (full example with multiple entries)
- "must have required property 'asset--plant--planting'", in the case when there was no planting chosen



For Monday Conversation:
1. I need to know what to do about ambiguousEntities **
2. Problem 1, Lime Application taxonomy_term--material_type--lime is required and not generated, not in ambiguousEntities or unrecognizedEntities
3. Problem 2, Double check diversified plantings but I can't get the output to be valid from apiCompose
4. Problem 3, Harvest/Termination oneOf const 1/0 and boolean required for is_terminated
    - Solar for status
    - Flaming for status
    - taxonomy_term--plant_type--species (row crops, for status)
        - taxonomy_term--plant_type--species (status for perrenial planting)
        - all taxonomy_term--plant_type--species for plantings
5. Problem 4, Harvest/Termination required missingPropety asset--plant--planting
6. Problem 5, Create Field, How to fill out the survey properly to get this array?
7. Problem 6, Irrigation requires asset--plant for example without planting
8. Problem 6b, Organic Matter Inputs requires asset--plant--planting for example without planting
10. Problem 7, both taxonomy_term--log_category and taxonomy_term--log_category--weed_control pass with no errors and is thus ambiguous
    - needs to have 'taxonomy_term--log_category--weed_control
    - this is for flaming
11. Problem 9, "must have required property 'asset--plant--planting'" for flaming examples not generated with no field and planting





1. Q: Should I be doing harvest/termination examples with planting/no field
2. Q: Should I be doing each of the different types of planting examples? 
---------------------------------------------------------------------------------------------------------------------------------------------------
ApiCompose Required Updates 
(Double-Check that some of these haven't happened yet)

Aka the things in the examples that had to be fixed (and maybe wont/cant be fixed in the ApiCompose?)

1. Herbicide 
    a. Incorrect Timestamp format (all logs)
    b. Missing ID (all logs)
    c. Adding the convention relationship (all everything)
    d. Missing quantity--material--active_ingredient_percent 
    e. The name of the product is not in the valid list and is instead 'herbicide'/'insecticide'/'fungicide'
    f. quantity--material--rate should have the label application_rate
    g. 'taxonomy_term--log_category', 'taxonomy_term--log_category--pest_disease_control' are ambiguous entities 

2. Solarization 
    a. Incorrect Timestamp format (all logs)
    b. Missing ID (all logs)
    c. Adding the convention relationship (all everything)
    d. Delete status from "taxonomy_term--log_category" (all taxonomy terms with status)
    e. 'taxonomy_term--log_category', 'taxonomy_term--log_category--weed_control' are ambiguous entities

3. Planting
    a. Add convention relationship (all everything)

4. Organic Inputs 
    a. Incorrect Timestamp format (all logs)
    b. Missing ID (all logs)
    c. Adding the convention relationship (all everything)

5. Fertilizer 
    a. Incorrect Timestamp format (all logs)
    b. Missing ID (all logs)
    c. Adding the convention relationship (all everything)

6. Mowing
    a. Incorrect Timestamp format (all logs)
    b. Missing ID (all logs)
    c. Adding the convention relationship (all everything)

7. Soil Disturbance (Tillage)
    a. Incorrect Timestamp format (all logs)
    b. Missing ID (all logs)
    c. Adding the convention relationship (all everything)

8. Lime
    a. Incorrect Timestamp format (all logs)
    b. Missing ID (all logs)
    c. Adding the convention relationship (all everything)
    d. Missing taxonomy term 'taxonomy_term--material_type--lime'

9. Irrigation 
    a. Incorrect Timestamp format (all logs)
    b. Missing ID (all logs)
    c. Adding the convention relationship (all everything)

10. Harvest/Termination 
    a. Incorrect Timestamp format (all logs)
    b. Missing ID (all logs)
    c. Adding the convention relationship (all everything)

11. Flaming
    a. Incorrect Timestamp format (all logs)
    b. Missing ID (all logs)
    c. Adding the convention relationship (all everything)
    e. 'taxonomy_term--log_category', 'taxonomy_term--log_category--weed_control' are ambiguous entities

12. Field
    a. Adding the convention relationship (all everything)



---------------------------------------------------------------------------------------------------------------------------------------------------
How To's and Important Notes 

The above of this file will be retained until it is determined that it is not needed in the updating/transitioning process. 
Below here, will be important information regarding the creation of examples, what certain things mean and questions/answers. 

Table of Contents: 
1. How to Create a Convention Relationship 
    a. External URI - Fetch the convention from the internet 
    b. Locally grab the conventions through a path 

2. Understanding Instances 

3. Filter vs Find


** How to create a convention relationship **

The convention relationship is the piece which tells the entity which overlay it fits into, through the use of the convention relationship. This will have the type of type: "taxonomy_term--convention", a standard attributes.name for each of the different conventions (which can be seen from the schema documentation page or navigated through your local files), as well as an external_uri. The external_uri is necessary for pulling the conventions through the web. There are different ways to build this convention relationship, primarily depending on if you're currently in active development or not. The options are to fetch the convention from the internet through a URI, which may be out of sync with a newer, more updated, local copy of the conventions. The second option is to use the local copy instead of fetching the conventions, which is primarily for active development and works better with changes. 

Option 1: Fetching the convention from the internet through a URI. 

The first option to creating a convention relationship is to fetch the convention from the internet. This convention will not be up to date based on your local conventions, but will be the published online convention. This is generally not what you want when working on the development process as your conventions will be more updated. 

Create the taxonomy_term convention, which we will add as a relationship once built. This taxonomy_term below is the one for Irrigation. Please note, the number "44311021" from the external_uri the .env "CI_PROJECT_ID" and "SOURCE_REPO_PROJECT_ID". The entity.attributes.name should match that of the convention, which you can see in your schema.json file under "title", found in output/collection/conventions. The "external_uri" should match where your schema is hosted on the internet. 

let moreEntities = [
    {
        type: "taxonomy_term--convention",
        id: conventionEntityID,
        attributes: {
            name: "Irrigation",
            external_uri: "https://gitlab.com/api/v4/projects/44311021/jobs/artifacts/main/raw/output/collection/conventions/log--input--irrigation/schema.json?job=copy_schemas"
        }
    },
];

This can then be inserted in as a convention relationship into the assets or logs you are working with. This can easily be done in this manner, swapping out the significant parts as necessary. 

For each input log (log--input), add a .relationships.convention containing the appropriate data: 

example_array = the combination of your example and the additional entities
// let example_array = [ ... irrigation_example, ... moreEntities ];

let conventionEntityID = randomUUID();

let logInputs = example_array.filter( d => /^log--input/.test(d.type) );

logInputs.forEach( log => {
  log
      .relationships
      .convention = {
          data: [
              {
                  type: "taxonomy_term--convention",
                  id: conventionEntityID
              }
          ]
      };
} );

Once you have all of your pieces, you can create the example using the ArrayDataOrganizer from the convention builder. Be aware that the initialize() is async, so you may need to await it depending on your setup. 

const builder = require("convention_builder");

let preparedExample = new builder.ArrayDataOrganizer({exampleArray: example_array});

await preparedExample.initialize();

** Option 2: Getting the convention locally from a file location **

The second option to creating a convention relationship is to use the local conventions based on your current conventions and setup. This is generally recommended for development work as everything will be in-sync and as expected. 

You will first need your directory locations of wherever your .json live. This will look like the structure "output/collection/conventions/log--input--irrigation/object.json" but with the expected convention replacing log--input--irrigation. 

let directory_name = path.join(__dirname, '..', '..', '..', 'output', 'collection', 'conventions', 'log--input--irrigation', 'object.json');

let base_schema_dir_name = path.join(__dirname, '..', '..', '..', 'input', 'collection');

let intendedConvention = JSON.parse( fs.readFileSync(directory_name) );
intendedConvention.gitlabProjectId = 'undefined';

let preparedExample = new builder.ArrayDataOrganizer(
    {
        exampleArray: example_array,
        predefinedConventions: [ JSON.parse( fs.readFileSync(directory_name) ) ],
        baseSchemataFolder: base_schema_dir_name
    });

await preparedExample.initialize();




** Understanding Instances ** 

There may be some working changes to instances after further discussion, but in the meantime the current way that they are working will be preserved here for reference. 

First of all, please be aware that there are several ways to get your instances from your example. Once you've crafted an example (either manually or automatically through SurveyStack or other means), you can check how "well" your example is doing, which will tell you any problems with the example or reasons why it is not fit into the expected model based on the provided data. 

These instances methods are also async, which will require in certain setups an "await" prior to each of these calls. 

example_array = your crafted example 
directory_name = directory with your conventions (typically with the style .../output/collection/conventions/your--convention--here/object.json)
base_schema_dir_name = the base directory with your conventions (typically with the style .../input/collection)

This is the setup to get the example which is now prepared for initalization. 
let preparedExample = new builder.ArrayDataOrganizer(
    {
        exampleArray: example_array,
        predefinedConventions: [ JSON.parse( fs.readFileSync(directory_name) ) ],
        baseSchemataFolder: base_schema_dir_name
    });


await preparedExample.initialize();
await preparedExample.structureInstances();

Upon using initalize and structureInstances, the newly ran instances will be ADDED additionally into the instances array. Thus, it is recommended to always grab the latest instance to look at as the prior ones are possibly outdated. It also makes sense for some kinds of examples to have multiple instances. For example, solarization has at least 2 instances for even the smallest of examples. This is because there is once instance for putting the tarp on then another instance for taking the tarp off. Other events may also have at least 2 instances for even the smallest of examples because of the function(s) being performed. 

In order to limit the amount of instances, before initalizing and restructuring, you can absolutely reset them.

preparedExample.instances = [];

As a standard for the examples, you will see 
let lastInstance = preparedExample.instances[preparedExample.instances.length - 1];

for this will be the last instance in the instance array even if more errors are later produced. Thus, this keeps the integrity of the examples. 



** Filter versus Find ** 

This is not specific to the conventions and their understanding, but to Javascript in general. Because this effects choices made for the examples that were explicitly made, it is included in this README. 

var animals = [
    {name: "George", type: "Cow", location: "Cow Fields"},
    {name: "Harry", type: "Duck", location: "Outside Pond"},
    {name: "Frank", type: "Goose", location: "Backyard"}
]

Find: 
animals.find( livestock => livestock.name === "George")
// Returns an object: {name: "George", type: "Cow", location: "Cow Fields"}

- If search fails, find returns undefined 
- When looking, .find() will look and stop after the first match

Filter
animals.filter( livestock => livestock.name === "George")
// Returns an array: [ {name: "George", type: "Cow", location: "Cow Fields"} ]

- If search fails, filter returns an empty array [] 
- When looking, .filter() will continue searching through the entire array, even after the first match is found. 


Thus, in order for examples to be resilient to changes, filter will be prefered. 