let {pesticide_herbicide_fungicide_example_noPlanting_yesField} = require("./fixed_working_examples/land_management_activities/surveyStackExamples");
const builder = require("convention_builder");
const { randomUUID } = require('crypto');

let conventionEntityID = randomUUID();

let moreEntities = [
    {
        type: "taxonomy_term--convention",
        id: conventionEntityID,
        attributes: {
            name: "Herbicide/pesticide",
            external_uri: "https://gitlab.com/api/v4/projects/44311021/jobs/artifacts/main/raw/output/collection/conventions/log--input--herbicide_or_pesticide/schema.json?job=copy_schemas"
        }
    },
];

let example_array = [ ... pesticide_herbicide_fungicide_example_noPlanting_yesField, ... moreEntities ];

let inputLogs = example_array.filter( d => /^log--input/.test(d.type) );

inputLogs.forEach( log => {
    log
        .relationships
        .convention = {
            data: [
                {
                    type: "taxonomy_term--convention",
                    id: conventionEntityID
                }
            ]
        };
} );

example_array.filter( d => d.type == "log--input" )[0].relationships.convention;

// Let's feed our data into the importer
let example = new builder.ArrayDataOrganizer({exampleArray: example_array});

// initialize our objects so they retrieve the conventions from the net and perform their operations.
await example.initialize();

// These are the errors
/* 
 errors: [
    {
      instancePath: '',
      schemaPath: '#/required',
      keyword: 'required',
      params: [Object],
      message: "must have required property 'log--input--herbicide_or_pesticide'"
    },
    {
      instancePath: '',
      schemaPath: '#/required',
      keyword: 'required',
      params: [Object],
      message: "must have required property 'taxonomy_term--material_type--herbicide_or_pesticide'"
    },
    {
      instancePath: '',
      schemaPath: '#/required',
      keyword: 'required',
      params: [Object],
      message: "must have required property 'quantity--material--active_ingredient_percent'"
    },
    {
      instancePath: '',
      schemaPath: '#/required',
      keyword: 'required',
      params: [Object],
      message: "must have required property 'asset--plant--planting'"
    },
    {
      instancePath: '',
      schemaPath: '#/required',
      keyword: 'required',
      params: [Object],
      message: "must have required property 'quantity--material--rate'"
    },
    {
      instancePath: '',
      schemaPath: '#/required',
      keyword: 'required',
      params: [Object],
      message: "must have required property 'taxonomy_term--unit--rate'"
    },
    {
      instancePath: '',
      schemaPath: '#/required',
      keyword: 'required',
      params: [Object],
      message: "must have required property 'taxonomy_term--log_category--pest_disease_control'"
    }
  ],
  is_valid: false,
  assembled_entity: {
    id: '855642ef-0647-4603-9d64-ce4044a6d4db',
    type: 'object',
    'quantity--standard--area_percentage': {
      type: 'quantity--standard',
      id: '4e747ea7-d4f1-4886-9980-964c7ea3395c',
      attributes: [Object],
      relationships: [Object]
    },
    convention_taxonomy: {
      type: 'taxonomy_term--convention',
      id: '6991e732-1a64-4a4c-bf2b-1ad01faf1fa0',
      attributes: [Object]
    }
  }
}
> example.instances[0].errors
[
  {
    instancePath: '',
    schemaPath: '#/required',
    keyword: 'required',
    params: { missingProperty: 'log--input--herbicide_or_pesticide' },
    message: "must have required property 'log--input--herbicide_or_pesticide'"
  },
  {
    instancePath: '',
    schemaPath: '#/required',
    keyword: 'required',
    params: {
      missingProperty: 'taxonomy_term--material_type--herbicide_or_pesticide'
    },
    message: "must have required property 'taxonomy_term--material_type--herbicide_or_pesticide'"
  },
  {
    instancePath: '',
    schemaPath: '#/required',
    keyword: 'required',
    params: {
      missingProperty: 'quantity--material--active_ingredient_percent'
    },
    message: "must have required property 'quantity--material--active_ingredient_percent'"
  },
  {
    instancePath: '',
    schemaPath: '#/required',
    keyword: 'required',
    params: { missingProperty: 'asset--plant--planting' },
    message: "must have required property 'asset--plant--planting'"
  },
  {
    instancePath: '',
    schemaPath: '#/required',
    keyword: 'required',
    params: { missingProperty: 'quantity--material--rate' },
    message: "must have required property 'quantity--material--rate'"
  },
  {
    instancePath: '',
    schemaPath: '#/required',
    keyword: 'required',
    params: { missingProperty: 'taxonomy_term--unit--rate' },
    message: "must have required property 'taxonomy_term--unit--rate'"
  },
  {
    instancePath: '',
    schemaPath: '#/required',
    keyword: 'required',
    params: {
      missingProperty: 'taxonomy_term--log_category--pest_disease_control'
    },
    message: "must have required property 'taxonomy_term--log_category--pest_disease_control'"
  }
]
*/ 

example.instances[0].unrecognizedEntities[0].allTests[0].errors

// Deeper look at the errors in particular 
/* 
[
  {
    instancePath: '',
    schemaPath: '#/required',
    keyword: 'required',
    params: { missingProperty: 'id' },
    message: "must have required property 'id'"
  },
  {
    instancePath: '/attributes/timestamp',
    schemaPath: '#/properties/attributes/properties/timestamp/type',
    keyword: 'type',
    params: { type: 'string' },
    message: 'must be string'
  }
]
*/

let insecticideLogRejectedCandidates = example.instances[0].unrecognizedEntities.filter( e => e.entity.type == "log--input" );

let insecticideLogCandidate = insecticideLogRejectedCandidates[0];

// Set ID and timestamp to address errors above
insecticideLogCandidate.entity.id = randomUUID();

insecticideLogCandidate.entity.attributes.timestamp = new Date( 1000 * parseFloat( insecticideLogCandidate.entity.attributes.timestamp ) ).toISOString();

await example.structureInstances();

// let's take a look at the errors now
/*
 errors: [
    {
      instancePath: '',
      schemaPath: '#/required',
      keyword: 'required',
      params: [Object],
      message: "must have required property 'taxonomy_term--material_type--herbicide_or_pesticide'"
    },
    {
      instancePath: '',
      schemaPath: '#/required',
      keyword: 'required',
      params: [Object],
      message: "must have required property 'quantity--material--active_ingredient_percent'"
    },
    {
      instancePath: '',
      schemaPath: '#/required',
      keyword: 'required',
      params: [Object],
      message: "must have required property 'quantity--material--rate'"
    },
    {
      instancePath: '',
      schemaPath: '#/required',
      keyword: 'required',
      params: [Object],
      message: "must have required property 'taxonomy_term--unit--rate'"
    },
    {
      instancePath: '',
      schemaPath: '#/required',
      keyword: 'required',
      params: [Object],
      message: "must have required property 'taxonomy_term--log_category--pest_disease_control'"
    },
    {
      instancePath: '/log--input--herbicide_or_pesticide/relationships',
      schemaPath: '#/properties/log--input--herbicide_or_pesticide/properties/relationships/additionalProperties',
      keyword: 'additionalProperties',
      params: [Object],
      message: 'must NOT have additional properties'
    }
  ],
*/

let taxonomyMaterialTypeLogRejectedCandidates = example.instances[0].unrecognizedEntities.filter( e => e.entity.type == "taxonomy_term--material_type" );

taxonomyMaterialTypeLogRejectedCandidates[0].allTests[1].errors

/* 
[
  {
    instancePath: '/attributes/status',
    schemaPath: '#/properties/attributes/properties/status/type',
    keyword: 'type',
    params: { type: 'boolean' },
    message: 'must be boolean'
  },
  {
    instancePath: '/attributes/status',
    schemaPath: '#/properties/attributes/properties/status/oneOf/0/const',
    keyword: 'const',
    params: { allowedValue: 0 },
    message: 'must be equal to constant'
  },
  {
    instancePath: '/attributes/status',
    schemaPath: '#/properties/attributes/properties/status/oneOf/1/const',
    keyword: 'const',
    params: { allowedValue: 1 },
    message: 'must be equal to constant'
  },
  {
    instancePath: '/attributes/status',
    schemaPath: '#/properties/attributes/properties/status/oneOf',
    keyword: 'oneOf',
    params: { passingSchemas: null },
    message: 'must match exactly one schema in oneOf'
  },
  {
    instancePath: '/attributes/name',
    schemaPath: '#/properties/attributes/properties/name/enum',
    keyword: 'enum',
    params: { allowedValues: [Array] },
    message: 'must be equal to one of the allowed values'
  }
]
*/

// I'm still not sure about the status whether it should be 0/1 or a boolean?
// There's also /attributes/name should be enum equal to one of the allowed values (it's insecticide so? it should work)
