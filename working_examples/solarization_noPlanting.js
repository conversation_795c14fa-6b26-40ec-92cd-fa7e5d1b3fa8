let {solarization_example_yesFieldnoPlanting} = require("./fixed_working_examples/land_management_activities/surveyStackExamples");
const builder = require("convention_builder");
const { randomUUID } = require('crypto');

let conventionEntityID = randomUUID();

let moreEntities = [
    {
        type: "taxonomy_term--convention",
        id: conventionEntityID,
        attributes: {
            name: "Solarization",
            external_uri: "https://gitlab.com/api/v4/projects/44311021/jobs/artifacts/main/raw/output/collection/conventions/log--activity--solarization/schema.json?job=copy_schemas"
        }
    },
];

let example_array = [ ... solarization_example_yesFieldnoPlanting, ... moreEntities ];

let logActivities = example_array.filter( d => /^log--activity/.test(d.type) );

logActivities.forEach( log => {
    log
        .relationships
        .convention = {
            data: [
                {
                    type: "taxonomy_term--convention",
                    id: conventionEntityID
                }
            ]
        };
} );

let preparedExample = new builder.ArrayDataOrganizer({exampleArray: example_array});

await preparedExample.initialize();

// 3 errors
// must have required property 'log--activity--solarization'
// must have required property 'taxonomy_term--log_category--weed_control'
// must have required property 'plant_asset' 

// let's look at the overlay errors for 'log--activity--solarization' 
// anotherPreppedExample.instances[0].unrecognizedEntities.filter( e => e.entity.type == "log--activity")[0].allTests[0];

/* 
error 1: must have required property id
error 2: attributes/timestamp must be a string
error 3: attributes/ is_termination must be equal to constant (/oneOf/0/const)
error 4: attributes/ is_termination must be equal to constant (/oneOf/1/const)
error 4: attributes/ is_termination must match exactly one schema in oneOf 
*/

// ID and timestamp fixes 

let solarizationRejectedCandidate = preparedExample.instances[0].unrecognizedEntities.filter( e => e.entity.type == "log--activity" );

// regex different here doesn't "start with" solarization reads like name: 'start solarization or tarping' 'end solarization or tarping'
let actualCandidates = solarizationRejectedCandidate.filter( d => /solarization/.test(d.entity.attributes.name) );

actualCandidates.length == solarizationRejectedCandidate.length
// true

solarizationRejectedCandidate.forEach( log => {
    log.entity.id = randomUUID();
    log.entity.attributes.timestamp = new Date( 1000 * parseFloat( log.entity.attributes.timestamp ) ).toISOString();
} );

preparedExample.instances = [];
await preparedExample.structureInstances();

/* 
preparedExample.instances[1].unrecognizedEntities[0].allTests[0]
*/

preparedExample.instances[1].unrecognizedEntities[0].allTests[0]

/* 
[
  {
    instancePath: '',
    schemaPath: '#/required',
    keyword: 'required',
    params: { missingProperty: 'taxonomy_term--log_category--weed_control' },
    message: "must have required property 'taxonomy_term--log_category--weed_control'"
  },
  {
    instancePath: '',
    schemaPath: '#/required',
    keyword: 'required',
    params: { missingProperty: 'plant_asset' },
    message: "must have required property 'plant_asset'"
  },
  {
    instancePath: '/log--activity--solarization/relationships/asset/data',
    schemaPath: '#/properties/log--activity--solarization/properties/relationships/properties/asset/properties/data/contains',
    keyword: 'contains',
    params: { minContains: 1 },
    message: 'must contain at least 1 valid item(s)'
  }
]
*/