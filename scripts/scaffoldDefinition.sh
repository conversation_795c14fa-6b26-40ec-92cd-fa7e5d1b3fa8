#!/bin/bash

# This script will initialize everything that's required for a convention definition sub repo: Its folder structure and a Git module, which will also be added to the main repo.

CONVENTION_NAME=$1
source .env

echo $REPO_GROUP
echo $DEFINITIONS_SUBGROUP
echo $CONVENTION_NAME


# Create folder structure
mkdir -p definitions/$CONVENTION_NAME/examples/correct
mkdir -p definitions/$CONVENTION_NAME/examples/incorrect

# Initialize repo, add folder structure and readme and create a remote in the parametrized gitlab space.
cd definitions/$CONVENTION_NAME
git init
echo "# Scaffolded Convention Directory" >> README.md
git remote add origin https://gitlab.com/$ORGANIZATION/$REPO_GROUP/$DEFINITIONS_SUBGROUP/$CONVENTION_NAME.git
git config --global push.autoSetupRemote true
git add -A
git commit -m "scaffolding convention"
git push

# return to main module
cd ../..

# add the just created definition as a submodule
git submodule add https://gitlab.com/$ORGANIZATION/$REPO_GROUP/$DEFINITIONS_SUBGROUP/$CONVENTION_NAME.git
