const fs = require('fs');

function toTitleCase(str) {
    return str.replace(
        /\w\S*/g,
        function (txt) {
            return txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase();
        }
    );
}

// obtaining the folders that we want to list in the sidebar
let folders = fs.readdirSync( `${__dirname}/docusaurus/farm_schemas/docs/Conventions`, console.error );

function listConventions(files) {
    let namesWithRepetitions = files
        .map(f => f.replace(/_[0-9]*.[0-9]*.[0-9]*.mdx/, ""))
    ;
    let names = Array.from(new Set(namesWithRepetitions));
    return names;
}

function formatConventionVersionsSection(files, folder, convention) {
    let versions = files.filter(file => file.includes(convention))
        .map(file => file.replace('\.mdx', ''))
        .map(file => `Conventions/${folder}/${file}`)
    ;
    let conventionLabel = toTitleCase( convention.replaceAll("--", " ").replaceAll("_", " ") );
    let section = {
        type: 'category',
        label: conventionLabel,
        collapsed: true,
        items: versions
    };
    return section;
}

let conventionCategories = folders.flatMap(folder => {
    let files = fs.readdirSync(`${__dirname}/docusaurus/farm_schemas/docs/Conventions/${folder}`, console.error);
    let conventions = listConventions(files);
    let itemsArray = conventions
        .map(convention => formatConventionVersionsSection(files, folder, convention) )
    ;
    let output = {
        type: 'category',
        label: folder,
        collapsed: false,
        items: itemsArray
    };
    return output;
} );

fs.writeFileSync(`${__dirname}/docusaurus/farm_schemas/static/convention_folders_structure.json`, JSON.stringify({ conventionCategories: conventionCategories }));

