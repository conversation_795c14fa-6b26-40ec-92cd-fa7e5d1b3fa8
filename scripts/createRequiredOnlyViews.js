let {reduceSchemaToRequiredFields} = require("convention_builder");
let fs = require("fs");

let staticSchemas = `${__dirname}/../scripts/docusaurus/farm_schemas/static/schemas`;

let schemata = fs.readdirSync(staticSchemas);

schemata.forEach( folder => {
    let versions = fs.readdirSync(`${staticSchemas}/${folder }`);
    versions.forEach( version => {
        let doc = JSON.parse(fs.readFileSync(`${staticSchemas}/${folder}/${version}/schema.json`));
        let requiredOnlyVersion = reduceSchemaToRequiredFields(doc);
        fs.writeFileSync(`${staticSchemas}/${folder}/${version}/schema_required_only.json`, JSON.stringify(requiredOnlyVersion), console.error);
    });
} );
