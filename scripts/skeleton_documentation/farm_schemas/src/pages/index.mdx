---
title: Farm Conventions
---

import { buildArtifactLink, buildDocumentationLink, buildPackageLinks } from "../../.././../../src/repo_utilities.js";
import repo from "@site/static/repo_data.json";


# Farm Convention Schema

The intention of this project is to provide an easy to implement but very functional and strict set of JSON Schema conventions for complex data structures, based on the preexistent FarmOS conventions.
This site belongs to a repo that contains a set of tools allowing a project to publish such a set of schemata with a minimal effort. In it you will find documentation for each of our proposed conventions.


# Key Links


_The following artifacts are generated automatically when you finish creating your Collection by running gitlab's CI pipeline._


## **Input Collection**


<ul>
  <li>
    <a href={( function () {let repo_value = repo;
    <!-- let isStaging = config.baseUrl.includes("staging_wiki"); -->
    let isStaging = true;
    let link = buildArtifactLink({organization:"our-sci", repo:repo_value.repo, repoGroup:"conventions", file:"input/collection", branch: isStaging ? "staging" : "main"} );
    return link;
    } )()}> Collection Schemas </a> - <i>Schema set used as a source for the basic building blocs.</i>
  </li>
</ul>

## **Output Collection**


<ul>
  <li>
    <a href={( function () {
    let repo_value = repo;
    <!-- let isStaging = config.baseUrl.includes("staging_wiki"); -->
    let isStaging = true;
    let link = buildArtifactLink({organization:"our-sci", repo:repo_value.repo, repoGroup:"conventions", file:"output/collection", baseUrl: config.baseUrl, branch: isStaging ? "staging" : "main"} );
    return link;
    } )()}>Collection Schemas</a> - <i>Final published collection of conventions and overlays</i>
  </li>

  <li>
    <a href={( function () {
    let repo_value = repo;
    <!-- let isStaging = config.baseUrl.includes("staging_wiki"); -->
    let isStaging = true;
    let link = buildDocumentationLink({organization:"our-sci", repo:repo_value.repo, repoGroup:"conventions", section:"wiki", baseUrl: config.baseUrl, section: isStaging ? "staging_wiki" : "wiki"} );
    return link;
    } )()}>Collection Documentation</a> - <i>Human Readable documetation for the Input Collection</i>
  </li>
</ul>


## **Validation** - _Both Input and Output Collections can be validated in a single NPM package here_


<ul>
  <li>
    <a href={( function () {
    let repo_value = repo;
    let link = buildPackageLinks({packageName:repo.packageName} );
    return link.npm;
    } )()}>NPM Package</a>
  </li>

  <li>
    <a href={( function () {
    let repo_value = repo;
    let link = buildPackageLinks({packageName:repo.packageName} );
    return link.cdn;
    } )()}>NPM Package - Browser Version</a>
  </li>
</ul>
