const fs = require('fs');

// obtaining the folders that we want to list in the sidebar
let folders = fs.readdirSync( `${__dirname}/docs/Conventions`, console.error );
let conventionCategories = folders.flatMap( folder => {
    let files = fs.readdirSync( `${__dirname}/docs/Conventions/${folder}`, console.error);
    let itemsArray = files
        .map( file => { return file.split('.')[0]; } )
        .map( file => `Conventions/${folder}/${file}` )
    ;
    let output = {
        type: 'category',
        label: folder,
        items: itemsArray
    };
    return output;
} );



/**
 * Creating a sidebar enables you to:
 - create an ordered group of docs
 - render a sidebar for each doc of that group
 - provide next/previous navigation

 The sidebars can be generated from the filesystem, or explicitly defined here.

 Create as many sidebars as you want.
 */

// @ts-check

/** @type {import('@docusaurus/plugin-content-docs').SidebarsConfig} */
const sidebars = {
  // By default, Docusaurus generates a sidebar from the docs folder structure
  // tutorialSidebar: [{type: 'autogenerated', dirName: '.'}],

  // But you can create a sidebar manually
  
  tutorialSidebar: [
      {
          type: 'category',
          label: 'Introduction',
          items: ['Description and Specification', 'Definitions', 'Frequently Asked Questions'],
      },
      {
          type: 'category',
          label: 'Conventions',
          items: conventionCategories
      }
  ],
};

module.exports = sidebars;
