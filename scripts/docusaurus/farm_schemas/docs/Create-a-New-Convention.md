---
title: Create a New Convention
---

This section guides you through the process of creating your own convention, potentially building upon the existing Common Farm Conventions.

## Get Started
<!-- NOTE: The sidebar link points to 'Get Started', but to avoid filename collision with the 'Use' section's 'Get Started', this should ideally point to a uniquely named file like 'Get Started - Create.md' or similar. Adjusting the link here and potentially in sidebar.js might be needed. -->

An overview of the concepts and prerequisites for creating a new convention.

<!-- TODO: Add content for the 'Get Started' specific to creating conventions. -->

## Using the Farm Convention Publication Pipeline

This guide details the steps to set up a new project using the Yeoman generator, which scaffolds a complete environment for building, validating, documenting, and publishing your conventions using GitLab CI/CD.

### Introduction

Since the convention system coordinates a lot of different pieces of software, configuration could become complex.
In order to avoid this, we've created an efficient installation process based on Yeoman generators.
Now, getting your repo configured and working only requires answering some questions and taking some easy (and optional) follow up steps.

### Requirements

This publication pipeline is currently based on GitLab CI/CD. While adaptation to other CI systems is possible, you will need a GitLab project to leverage the full benefits. Creating a free GitLab account provides everything necessary.

### Which tool are we using?

[Yeoman](https://yeoman.io/) is a scaffolding helper tool. We use it to create an installer (a "generator" in Yeoman terms) that configures your repository based on your needs and data. The generator is an NPM package. We'll start from an empty project folder, install this package, and use it to transform the empty folder into a fully functional Convention Builder project.

### Step 1: Create a GitLab Project

You'll need an empty GitLab project to host your convention and run the CI/CD pipeline for documentation generation and publishing.

*   Follow GitLab's guide to [create an empty project](https://docs.gitlab.com/ee/user/project/).
*   Note your project's Git URL (e.g., `https://gitlab.com/your-user/your-group/your-convention-project.git`).
*   Clone the empty repository to your local machine: `git clone <your-project-url>`
*   Keep the URL handy; the generator will ask for it.

#### About the URL Structure

GitLab URLs can include project groups:

1.  **With a group:** `https://gitlab.com/your-user/your-group/your-project.git`
    *   `your-user`: Organization or username
    *   `your-group`: Project group (optional)
    *   `your-project`: Project name
2.  **Without a group:** `https://gitlab.com/your-user/your-project.git`
    *   `your-user`: Organization or username
    *   `your-project`: Project name

The generator will ask for these parts separately. If you don't have a group, leave that answer blank.

### Step 2: Install the Generator

Navigate into your cloned repository folder on your local machine. Install the Yeoman generator as a development dependency:

```bash
npm install --save-dev generator-farm_convention_builder
```

### Step 3: Run the Generator

Execute the generator using the `yo` command:

```bash
yo farm_convention_builder
```

The generator will prompt you for information about your project (name, description, GitLab URL components, etc.). Provide the requested details.

**Important:** Do not push your changes to GitLab yet. We need to configure CI/CD variables first.

The generator saves your configuration. You can re-run `yo farm_convention_builder` anytime to update your project structure or correct previous inputs. Your previous answers will be suggested as defaults.

<!-- TODO: The blog post referenced an image 'tutorial_generator_prompt.png'. This image needs to be located and added here if possible, or the reference removed. -->

### Step 4: Configure GitLab Project

#### Copy Environment Variables to GitLab CI/CD

The generator created a `.env` file in your project root with the variables you provided. These need to be added as CI/CD variables in your GitLab project settings for the pipeline to work.

*   In your GitLab project, navigate to **Settings** -> **CI/CD**.
*   Expand the **Variables** section.
*   Click **Add variable** for each variable listed in your local `.env` file. Enter the **Key** (variable name) and **Value**. Ensure the 'Protected' and 'Masked' flags are set appropriately (usually unchecked unless sensitive).
*   **Crucial:** If you plan to publish your convention definitions as an NPM package, you *must* add an additional variable:
    *   **Key:** `NPM_AUTH_TOKEN`
    *   **Value:** Your [NPM access token](https://docs.npmjs.com/about-access-tokens) with publish permissions.

#### Set GitLab Pages URL to be Predictable

To ensure the links in your auto-generated documentation work correctly, configure your GitLab Pages settings:

*   In your GitLab project, navigate to **Deploy** -> **Pages**.
*   Find the option **Use unique domain** and **uncheck** it.
*   Note down your project's GitLab Pages URL (e.g., `https://your-user.gitlab.io/your-group/your-project`). You'll use this to access your documentation wiki.

### Step 5: Push Initial Project Structure

Now that the GitLab CI/CD variables are configured, commit and push the files generated by Yeoman:

```bash
git add -A
git commit -m "Initialize project using farm_convention_builder generator"
git push origin main # Or your default branch name
```

#### Check the CI/CD Pipeline

Pushing the changes will automatically trigger a CI/CD pipeline in your GitLab project (**Build** -> **Pipelines**). This pipeline will:

*   Validate your initial convention files (if any).
*   Build the documentation website (wiki).
*   Deploy the website to GitLab Pages.

Once the pipeline succeeds, you should be able to access your documentation wiki at your GitLab Pages URL + `/wiki` (e.g., `https://your-user.gitlab.io/your-group/your-project/wiki`). It will initially contain documentation for the example convention provided by the generator.

### Step 6: Post-Installation Cleanup

The generator likely added a "Next Steps" section to your project's `README.md`. Review these steps (you may have already completed them during configuration). Once confirmed, edit your `README.md` to remove the generator's suggestions and add specific details about *your* convention project.

You are now ready to start defining your own conventions!
