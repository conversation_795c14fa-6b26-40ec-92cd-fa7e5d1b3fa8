## Description

The goal of this [Collection](#Definitions) is to **establish a baseline set of [Conventions](#Definitions) (schemas) to describe most agricultural management activities**.  This baseline set of Conventions _requires very little data, but defines a lot of types of data_.  We hope this supports **comparability** and **extensibility**, enabling others to build Collections of schemas which extend, but are still validated, against this Collection or parts of it.

The data and conceptual model is centered around `assets` (the things being tracked) and `logs` (events which affect those things).  It also follows a structure similar to JSON API, with each element containing `attributes` (data about the entity) and `relationships` (other entities connected to this one).  This model emerged from FarmOS's data model [(see more here)](https://farmos.org/model/).

By connecting `assets` and/or `logs` through `relationships` (via a linking `id`) we can flatten some or all of a farm management database into linked JSON objects.  This [snapshot](#Definitions) can effectively describe and validate even complex management activities.

✨🎉✨ We encourage groups to expand on this Collection by cloning the repository and creating your own Collection.  Please see the gitlab Readme file or FAQ for more details.

## Specification

Generally this Collection does not further the addition of data types inside `attributes` or `relationships`  through `additionalProperties`.  In addition, very few fields are required which allows data with minimal information to usually pass.  See each Convention for details on required fields.

Below are a list of design concepts / rules that affect large parts of the Conventions in this Collection.  The "Affects" column describes which types of entities are affected by the specification.

> Remember - most of the Convention information is contained in the Convention Schema's themselves.  See a specific Convention for more detail.

| Affects | Shorthand | Description | Reason |
| ------ | ------ | ------ | ------ |
| Logs | **Timestamp always required** | ALL `log`'s must have a valid `timestamp` | data isn't meaningfully usable without a time reference | 
| Logs | **Status always required** | ALL `log`'s must have a valid `status` | data isn't meaningfully usable without a status reference |
| Logs | **Log Category always Required** | ALL `log`'s should have AT LEAST one `log_category` specified | `log_category` is the primary filter for determining intent/purpose.  To communicate farm management information with any real meaning, intent must be specified |
| Logs | **All logs can have Area Applied** | ANY `log` MAY have an `area` quantity.  If not specified, assume `area` = 100% | `area` is frequently used across all logs and is a common modifier used by anyone consuming the data |
| Logs | **Area describes % of Land or Plant** | `area` is stored as a quantity, with label `area` and units `%` and describes the % of the asset which has had this operation applied.  For logs with one associated `land` assets only, this is a % of that land that this operation applied to.  For logs with one associated `plant` asset only, this is a % of that planting that this operation applied to.  For logs with multiple associated `plant` or `land` assets or mixed `plant` and `land` assets, `area` represents the % of the total `plant` and/or `land` assets referenced.  This mixed usage of `area` is confusing and should be avoided. | |
| Logs | **Associate Logs with Plantings** | ALL management logs in a field SHOULD be associated with a `plant` asset.  IF there is no `plant` asset (ie nothing was planting), EITHER 1) the activity should be associated with the next expected `plant` asset (eg, if it's winter field prep, associate it with next years planting), 2) OR in cases of extended fallow periods, a 'fallow' planting SHOULD be added and associated with logs during that period. | To communicate the answer to 'how someone grows something' you need to understand which activities in a field(s) are relate to that thing they are growing over time.  Attempting, whenever possible, to associate logs with a `plant` asset helps make that communication possible |
| Quantities | **Standard Quantity structure** | `quantity--standard` SHOULD be organized as follows: 1) 'label' is the unique name describing the quantity, 2) 'units' is the units of this quantity, 3) 'quantity' is the value.  If a material is applied, then use `quantity--material` | This clarifies the use of `label`, which could be used to describe different things and thus be confusing.  `label` is often the key filter term when comparing data, and thus critical in many Conventions. |
| Quantities| **Make Material Quantities when possible** | `quantity--material` SHOULD be used whenever a material is applied.  | This ensures we can always look to the `material` reference to identify any material applied, regardless of quantity. |
| Quantities| **Make Material Quantities even with no Value** | `quantity--material` SHOULD still be created even without a specified `value`.  | This ensures we can always look to the `material` reference to identify any material applied, regardless of quantity. |
| Quantities | **Don't re-use Material Quantity references** | A different `quantity--material` entity SHOULD be created to represent each different object in real life (eg if you have 5 irrigation logs that all use 5 gallons of water, you should create 5 separate quantities for each 5 gallon irrigation event because it's not the same physical water in each case). | This allows separate tracking of individual events and it makes searching for or summing quantities. |
| Quantities | **Separate Material or Product Quantities for better tracking** | If there are multiple referenced `material` or `product` taxonomy terms for a single quantity in a `quantity--material` and `quantity--product`, we cannot know how the `value` is split between them.  Rather, if the quantities were known between the two products, then two separate quantities SHOULD be added instead of one quantity with two product references. | In this sense, consider the material relationships like 'tags' (there can be many, and they can all be equally true), like 'insecticide' and 'johns bug spray'.  This doesn't mean it's 1/2 insecticide and 1/2 johns bug spray... it means it's both insecticide and bug spray. | 
| Quantities | **Add All Materials to Material Quantities** | The `material` or `product` taxonomy terms associated with the `quantity--material` or `quantity--product` SHOULD include all information about the material available, including the general type (eg. "pesticide", "lime"), the class (eg. "insecticide", "dolomitic lime"), or name (eg."Atrazine", "GroSmart D-Lime").  `label` is not required for `quantity--material` or `quantity--product` as the key information is provided in the `material` taxonomy terms. | This allows data with unclear relationships (hierarchy, classes, etc) a flexible place to be.  It is then up to those analyzing the data to filter by their desired key word.  This avoids the analyzer needing to know which item is a 'class' versus 'name' versus 'type', etc.  As a next step, we should link ontologies to the taxonomy terms, so that classes or hierarchies can be identified without being explicitly included in the `material` reference |
| Plantings | **Add All Crops and Varieties to Plant Crop Field** | The `crop` taonomy term in a `plant` asset SHOULD include all information about both the crop type (eg "sweet corn") and variety (eg "bobs red sweet"). | Allows data with unclear relationships (hierarchy, classes, etc) a flexible place to be.  It is then up to those analyzing the data to filter by their desired key word. |

