/* Styling for the overall section containing the feature blocks */
.features {
    padding: 2rem 0; /* Add vertical padding above and below the section */
    width: 100%;
  }
  
  /* Styling for the images within the feature blocks */
  .featureImage {
    display: block; /* Ensure image takes up block space */
    margin-left: auto;  /* Helps with centering if needed */
    margin-right: auto; /* Helps with centering if needed */
    max-width: 100%;   /* Prevent image from exceeding column width */
    height: auto;      /* Maintain aspect ratio */
    padding: 1rem;     /* Add some padding around the image */
    max-height: 300px; /* Optional: Constrain max height if images are very tall */
  }
  
  /* You might need styles for the hero section if you haven't defined them elsewhere */
  /* Example: */
  /*
  .heroBanner {
    padding: 4rem 0;
    text-align: center;
    position: relative;
    overflow: hidden;
    background-color: var(--ifm-hero-background-color); 
    color: var(--ifm-hero-text-color); 
  }
  
  .heroLogo {
    max-width: 150px;
    margin-bottom: 1rem;
  }
  */
  