---
title: Common Farm Conventions
---

import Link from '@docusaurus/Link';
import useBaseUrl from '@docusaurus/useBaseUrl';
import Heading from '@theme/Heading';
import styles from './index.module.css';

{/* --- Keep the existing Hero Section, update button link --- */}
<header className={styles.heroBanner}>
  <div className="container">
    <p className="hero__subtitle">A Shared Language for Farm Records Data</p>
    <div className={styles.buttons}>
      <Link
        className="button button--secondary button--lg"
        to="/docs/Why-Common-Farm-Conventions"> {/* Points to the new Why page */}
        Learn Why Conventions Matter
      </Link>
    </div>
  </div>
</header>

<main>

  {/* --- Introductory Paragraph --- */}
  <section className="padding-vert--lg">
    <div className="container">
      <p className="lead">
        Common Farm Conventions provide a shared language for farm records data, making it easier to exchange information, build interoperable tools, and improve data quality in agricultural production, research and management. Whether you need to integrate farm data into your application, manage data for a research project, or define standards for your organization, these conventions offer a flexible and powerful framework built on JSON Schema and the FarmOS data model.
      </p>
    </div>
  </section>

  {/* --- Refined Feature Blocks Section --- */}
  <section className={styles.features}>
    <div className="container">

      {/* Feature Block 1: Why */}
      <div className="row margin-vert--lg">
        <div className="col col--6">
          <div className="text--center padding-horiz--md">
            {/* TODO: Replace with a more relevant image if available */}
            <img src={useBaseUrl('/img/placeholder_why.png')} alt="Illustration showing fragmented vs connected data" className={styles.featureImage} />
          </div>
        </div>
        <div className="col col--6">
          <Heading as="h2">Unlock Your Farm Records Data</Heading>
          <p>
            Overcome data silos and incompatibility issues. Learn how shared data conventions, defined using JSON Schema, enable reliable data exchange, automated validation, and clearer documentation, simplifying integration and collaboration.
          </p>
          <Link
            className="button button--primary"
            to="/docs/Why-Common-Farm-Conventions">
            Why Use Conventions?
          </Link>
        </div>
      </div>

      {/* Feature Block 2: Use */}
      <div className="row margin-vert--lg">
        <div className="col col--6">
          <Heading as="h2">Integrate & Use Conventions</Heading>
          <p>
            For data managers, developers, and analysts: Learn how to apply existing conventions in your projects. Understand the core data model (based on farmOS), find best practices, and validate data against standard schemas.
          </p>
          <Link
            className="button button--primary"
            to="/docs/Use-Common-Farm-Conventions">
            Start Using Conventions
          </Link>
        </div>
        <div className="col col--6">
          <div className="text--center padding-horiz--md">
             {/* TODO: Replace with a more relevant image if available */}
             <img src={useBaseUrl('/img/placeholder_use.png')} alt="Illustration of data flowing into an application" className={styles.featureImage} />
          </div>
        </div>
      </div>

      {/* Feature Block 3: Create */}
       <div className="row margin-vert--lg">
        <div className="col col--6">
          <div className="text--center padding-horiz--md">
            {/* TODO: Replace with a more relevant image if available */}
            <img src={useBaseUrl('/img/placeholder_make.png')} alt="Illustration of building blocks forming a convention" className={styles.featureImage} />
          </div>
        </div>
        <div className="col col--6">
          <Heading as="h2">Build & Extend Conventions</Heading>
          <p>
            For developers and domain experts: Need a specific data format? Use our tooling pipeline to define, document, test, and publish your own conventions that build upon or extend the common foundation.
          </p>
          <Link
            className="button button--primary"
            to="/docs/Create-a-New-Convention"> {/* Updated link */}
            Create a New Convention
          </Link>
        </div>
      </div>

      {/* Feature Block 4: Contribute */}
      <div className="row margin-vert--lg">
        <div className="col col--6">
          <Heading as="h2">Join the Community</Heading>
          <p>
            Get involved! Ask questions in the forum, join the chat or community calls, report issues, or contribute code. Help shape the future of agricultural data standards.
          </p>
          <Link
            className="button button--primary"
            to="/docs/Contribute"> {/* Updated link to Contribute page */}
            Get Involved
          </Link>
        </div>
        <div className="col col--6">
          <div className="text--center padding-horiz--md">
             {/* TODO: Replace with a more relevant image if available */}
             <img src={useBaseUrl('/img/placeholder_contribute.png')} alt="Illustration of people collaborating" className={styles.featureImage} />
          </div>
        </div>
      </div>
    </div>
  </section>

  <section className="padding-vert--md">
      <div className="container">
          <div className="row">
              <div className="col col--6">
                  <div className="card">
                      <div className="card__header">
                          <h3>Using Conventions</h3>
                      </div>
                      <div className="card__body">
                          <p>Learn how to apply and work with existing Common Farm Conventions.</p>
                      </div>
                      <div className="card__footer">
                          <Link className="button button--primary button--block" to="/docs/Use-Common-Farm-Conventions">
                              Get Started Using
                          </Link>
                      </div>
                  </div>
              </div>
              <div className="col col--6">
                  <div className="card">
                      <div className="card__header">
                          <h3>Creating Conventions</h3>
                      </div>
                      <div className="card__body">
                          <p>Follow the pipeline to create and publish your own conventions.</p>
                      </div>
                      <div className="card__footer">
                          <Link className="button button--primary button--block" to="/docs/Create-a-New-Convention">
                              Get Started Creating
                          </Link>
                      </div>
                  </div>
              </div>
          </div>
      </div>
  </section>

</main>