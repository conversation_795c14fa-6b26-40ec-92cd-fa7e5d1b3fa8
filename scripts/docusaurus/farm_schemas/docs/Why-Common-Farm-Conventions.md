---
title: Why Common Farm Conventions?
slug: /
---

## The Challenge: Fragmented Agricultural Data

The agricultural sector faces a significant challenge: data is often collected and stored in diverse, incompatible formats across different farms, organizations, and software tools. This fragmentation makes it difficult to:

*   **Share data effectively:** Collaborating on projects, reporting to funders, or participating in research initiatives becomes cumbersome and error-prone.
*   **Integrate systems:** Connecting different farm management software, analysis tools, or data platforms requires custom, time-consuming development.
*   **Ensure data quality:** Without common standards, validating data consistency and accuracy is difficult.
*   **Gain broader insights:** Aggregating data for regional analysis, benchmarking, or developing predictive models is often impractical.

## The Solution: Shared Data Agreements (Conventions)

Common Farm Conventions provide a solution by establishing **shared agreements** on how to structure and describe agricultural data for specific purposes. Think of them as blueprints for data exchange.

Instead of rigid, one-size-fits-all standards, Conventions offer a flexible approach:

1.  **Built on a Common Foundation:** They leverage the robust and widely adopted [farmOS data model](https://farmos.org/model/) for core concepts (Assets, Logs, etc.), providing a familiar base.
2.  **Leveraging JSON Schema:** Conventions are defined using [JSON Schema](https://json-schema.org/), a powerful standard for describing the structure and constraints of JSON data. This brings numerous technical advantages.
3.  **Modularity and Extensibility:** A "base" Common Farm Convention provides core structures, but organizations or projects can create "child" conventions that inherit from and extend the base, tailoring it to specific needs without losing compatibility.

## Benefits for Everyone

Adopting Common Farm Conventions offers advantages across different roles:

### For Project Managers & Data Managers:

*   **Improved Data Quality:** Built-in validation rules ensure data consistency and completeness from the start.
*   **Simplified Collaboration:** Easily share and receive data with partners who adhere to the same conventions.
*   **Streamlined Reporting:** Standardized data formats make aggregation and reporting more efficient.
*   **Clear Documentation:** Conventions come with human-readable documentation generated directly from the schemas.

### For Developers & Technical Teams:

*   **Robust Validation:** Leverage JSON Schema tooling for automatic data validation in applications.
*   **Interoperability:** Easier integration between systems that understand the same conventions. Reduces the need for custom data mapping.
*   **Machine Readability:** JSON Schema provides a clear, machine-understandable definition of the data structure.
*   **Auto-Generated Documentation:** Reduce the burden of writing and maintaining data format documentation.
*   **Extensibility:** Create project-specific conventions that build upon the common base, promoting reuse and reducing redundant effort.
*   **Alignment with Modern Practices:** Uses well-established web standards (JSON, JSON Schema).

<!-- TODO: Add a brief comparison to other potential approaches if more detail is available from source documents, highlighting the flexibility and tooling benefits of the JSON Schema/layered convention approach. -->

## Conclusion

Common Farm Conventions offer a practical, flexible, and technically sound approach to tackling data interoperability challenges in agriculture. By providing shared blueprints built on established standards, they empower organizations to improve data quality, streamline collaboration, and unlock the potential of their data assets.
