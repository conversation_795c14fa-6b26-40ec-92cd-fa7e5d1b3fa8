---
title: Use Common Farm Conventions (CFC)
---

This section provides resources for understanding and utilizing the Common Farm Conventions in your projects or applications.

## Get Started

**Common Farm Conventions are data transfer standards for farm records like tillage, planting, and harvest.**  Domain experts can build on them to make their own, more specific but comparable, data transfer standards.  The CFC makes data more easily shared and services that use it more accessible.

### Who
_Who should use the CFC?  People who are..._

- Building ag software
- Connecting ag software together using APIs
- Organizing data ecosystems with many partners
- Building models using ag data to calculate, recommend, verify, certify, etc.
- Designing methodologies, standards, or best practices to share.
- Building or using AI tools to automate all of the above.

### Why
_Why use the CFC?_

**For end users**
- **Enter data once, use many times** - Data moving between services using the CFC takes less effort.
- **Easily connect with many services** - Data in one location can easily access many other services.

**For developers**
- **Connect services once, use many times** - A service connected to the CFC can then access all other connected services.
- **Easily build on best practices** - Ag data is complex - don't reinvent the wheel!

**For domain experts**
- **Agree once, use many times** - Agreement is hard to achieve.  CFC helps encode your agreement on methods/data/standards, and gives others the ability to build on that agreement.

### What
_What actually is a Common Farm Convention?_

Common Farm Convetions are a way to describe farm records using JSON Schema.  Conventions are built by domain experts or developers and then used to communicate and validate data between software.

**Using Conventions**
1. **Understand** exactly what the data should look like and need.
2. **Validate** using code whether a piece of data follows the convention.

Example

**Building Conventions**
1. **Agree** on what the data should look like
2. **Test** a convention against real data to ensure it's working
3. **Deploy** versioned conventions with documentation, validation code, and permalinks.

Example

Explore the underlying data model that forms the basis of the Common Farm Conventions. This model is heavily based on the [farmOS Data Model](https://farmos.org/model/), which defines core entities like Assets, Logs, Quantities, and their relationships. Understanding this structure is key to effectively using and extending conventions.

### How
_How can you get started?_

### Learn more!
- **JSON Schema** - [What is a schema anyway](https://json-schema.org/understanding-json-schema/about).  The [Background](https://json-schema.org/overview/what-is-jsonschema) of JSON Schema as an interchange format.
- 

