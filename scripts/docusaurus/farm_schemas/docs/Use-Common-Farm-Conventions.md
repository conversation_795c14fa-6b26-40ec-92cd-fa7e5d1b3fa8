---
title: Use Common Farm Conventions
---

This section provides resources for understanding and utilizing the Common Farm Conventions in your projects or applications. Whether you're integrating existing conventions or looking to ensure your data aligns with community standards, these guides will help you get started.

## Get Started

New to Common Farm Conventions? This guide provides the essential first steps to begin using them.

<!-- TODO: Add brief summary of Get Started content -->

## Convention Rules

Understand the fundamental rules and constraints that define a valid Common Farm Convention implementation.

<!-- TODO: Add brief summary of Convention Rules content -->

## Convention Best Practices

Learn recommended practices for effectively applying Common Farm Conventions to ensure data quality, interoperability, and ease of use.

<!-- TODO: Add brief summary of Best Practices content -->

## Data Model and Structure

Explore the underlying data model that forms the basis of the Common Farm Conventions. This model is heavily based on the [farmOS Data Model](https://farmos.org/model/), which defines core entities like Assets, Logs, Quantities, and their relationships. Understanding this structure is key to effectively using and extending conventions.

<!-- TODO: Add brief summary of Data Model content -->

## Contribute

Find out how you can contribute to the development and improvement of the Common Farm Conventions.

<!-- TODO: Add brief summary of Contribution guidelines -->
