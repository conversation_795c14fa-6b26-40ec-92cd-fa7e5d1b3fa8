export const exampleData = [
    {
        avatarSrc: "https://secure.gravatar.com/avatar/a02323e79edd7695acae01a43e2c7bf2a713ab9af5607b0b3f5e73ca3935fc8f?s=384&d=identicon",
        userName: "<PERSON>",
        userRoles: [ "Founder", "Advocate", "Lead Engineer" ],
        imgSrc: "/img/news_images/schema_1.jpg",
        header: "How schemata can help your AI be meaningful.",
        text: "Lorem ipsum dolor sit amet, consectetuer adipiscing elit.  Donec hendrerit tempor tellus.  Donec pretium posuere tellus.",
        link: "../"
    },
    {
        avatarSrc:"https://secure.gravatar.com/avatar/f180826a336b92199c17ba7f922ad2a42452a23f2dbdfff4eed1cd48f97b6bf2?s=1600&d=identicon",
        userName: "<PERSON>",
        userRoles: [ "<PERSON><PERSON><PERSON>", "Schema Designer" ],
        imgSrc: "/img/news_images/schema_3.jpg",
        header: "Conceptualizing an Ag. Model Training Framework.",
        text: "Nullam eu ante vel est convallis dignissim.  Fusce suscipit, wisi nec facilisis facilisis, est dui fermentum leo, quis tempor ligula erat quis odio.  Nunc porta vulputate tellus.",
        link: "../"
    },
    {
        avatarSrc:"https://gitlab.com/uploads/-/system/user/avatar/5581361/avatar.png?width=800",
        userName: "Octavio M. Duarte",
        userRoles: [ "Statistician", "Data Scientist" ],
        imgSrc: "/img/news_images/schema_2.jpg",
        header: "Short concepts of schema algebra.",
        text: "Nullam eu ante vel est convallis dignissim.  Fusce suscipit, wisi nec facilisis facilisis, est dui fermentum leo, quis tempor ligula erat quis odio.",
        link: "../blog/deploy-your-conventions-repo"
    }
];
