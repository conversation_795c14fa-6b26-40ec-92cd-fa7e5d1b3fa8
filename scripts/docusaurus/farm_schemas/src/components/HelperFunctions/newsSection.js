import { React, Component } from 'react';
import useBaseUrl from '@docusaurus/useBaseUrl';
import Layout from '@theme/Layout';
import yaml from "js-yaml";

import { exampleData } from "../featuredArticles";

exampleData.forEach( newsArticle => {
    if (newsArticle.author) {
        let authorData = authorsData.find( d => d.name == newsArticle.author );
        authorData.userName = authorData.name;
        authorData.userRoles = authorData.title;
        authorData.avatarSrc = authorData.image_url;
    };
} );

class NewsCard extends Component {
    render() {
        return (
            <div class="padding-vert--md padding-horiz--md">
              <div class="card-demo">
                <div class="card">
                  <div class="card__header">
                    <div class="avatar">
                      <img
                        class="avatar__photo"
                        src={ this.props.avatarSrc } />
                      <div class="avatar__intro">
                        <div class="avatar__name">{ this.props.userName }</div>
                        <small class="avatar__subtitle">
                          { this.props.userRoles.join(", ").concat(".") }
                        </small>
                      </div>
                    </div>
                  </div>
                  <div class="row">
                      <div class="col margin--md">
                        <div class="card__image"
                             height="10%"
                        >
                          <a href={this.props.link}>
                            <img
                              src={require( `@site/static${ this.props.imgSrc }` ).default}
                              alt="article illustration"
                              max-width="300px"
                              max-height="300px"
                              height="auto"
                            />
                          </a>
                        </div>
                      </div>
                    <div class="col margin--md">
                        <div class="card__body">
                          <h3>{this.props.header}</h3>
                          <p>{this.props.text}<a href={this.props.link}> read more...</a></p>
                        </div>
                      </div>
                  </div>
                  <div class="card__footer">
                  </div>
                </div>
                </div>
            </div>
        );
    };
}

export function NewsSection() {
    return (
          <div
            class="container"
          >
            <div
              class="col"
            >
              { exampleData.map(datum => ( <NewsCard {... datum}/> ))   }
            </div>
          </div>
    );
}

const partnerData = [
    {
        name: "Farmier, LLC.",
        website: "www.farmier.net",
        imgSrc: "/img/partner_logos/farmier.png",

    },
    {
        name: "USDA",
        website: "www.usda.net",
        imgSrc: "/img/partner_logos/usda.png",

    },
    {
        name: "PASA farming",
        website: "www.pasa.net",
        imgSrc: "/img/partner_logos/pasa.png",

    },
    {
        name: "Our Sci, LLC",
        website: "www.our-sci.net",
        imgSrc: "/img/partner_logos/our_sci.png",

    }
];

class UsedBy extends Component {
    render() {
        return(
            <div class="col">
                      <div class="col margin--md">
                        <div class="card__image"
                             height="10%"
                        >
                          <a href={this.props.website}>
                            <img
                              src={require( `@site/static${ this.props.imgSrc }` ).default}
                              max-width="300px"
                              max-height="300px"
                              height="auto"
                            />
                          </a>
                        </div>
                      </div>
            </div>
        );
    };
};

export function PartnerSection() {
    return(
        <div class="container">
          <div class="row">
            {partnerData.map( datum => ( <UsedBy {...datum}/> ) )}
          </div>
        </div>
    );
};

export function ButtonsSection() {
    return(
        <div class="container">
         <div class="row row--no-gutters">
           <div class="col col--6"
             style={{
                 display:'flex',
                 alignContent:'center',
                 justifyContent:'center'
             }}
           >
             <a class="button button--outline button--primary" href="./blog/deploy-your-conventions-repo">Get started</a>
           </div>
           <div class="col col--6"
                style={{
                 display:'flex',
                 alignContent:'center',
                 justifyContent:'center'
             }}
           >
             <a class="button button--outline button--primary" href="./blog/using-the-convention-builder">How to use</a>
           </div>
          </div>
        </div>
    );
};
