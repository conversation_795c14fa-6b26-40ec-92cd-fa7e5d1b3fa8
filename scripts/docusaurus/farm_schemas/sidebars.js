import conventionCategories from "./static/convention_folders_structure.json";

/**
 * Creating a sidebar enables you to:
 - create an ordered group of docs
 - render a sidebar for each doc of that group
 - provide next/previous navigation

 The sidebars can be generated from the filesystem, or explicitly defined here.

 Create as many sidebars as you want.
 */

// @ts-check

/** @type {import('@docusaurus/plugin-content-docs').SidebarsConfig} */
const sidebars = {
  // By default, Docusaurus generates a sidebar from the docs folder structure
  // tutorialSidebar: [{type: 'autogenerated', dirName: '.'}],

  // But you can create a sidebar manually

  tutorialSidebar: [
    {
        type: 'doc',
        id: 'Why-Common-Farm-Conventions',
        className: 'sidebar-category--bold',
        label: '🤔 Why Common Farm Conventions'
      },
      {
        type: 'category',
        label: '⭐ Use Common Farm Conventions',
        className: 'sidebar-category--bold',
        link: { // ensure that the subsequent # links for this doc have the sidebar still open.  Without this it doesn't know to open the sidebar
          type: 'doc',
          id: 'Use-Common-Farm-Conventions',
        },
        items: [
            { type: 'link', label: 'Get Started', href: '/Use-Common-Farm-Conventions' },
            { type: 'link', label: 'Convention Rules', href: '/Use-Common-Farm-Conventions' },
            { type: 'link', label: 'Convention Best Practices', href: '/Use-Common-Farm-Conventions' },
            { type: 'link', label: 'Data Model and Structure', href: '/Use-Common-Farm-Conventions' },
        ],
      },
      {
        type: 'category',
        label: '🚜 Create a New Convention',
        className: 'sidebar-category--bold',
        link: { // ensure that the subsequent # links for this doc have the sidebar still open.  Without this it doesn't know to open the sidebar
          type: 'doc',
          id: 'Create-a-New-Convention',
        },
        items: [
            { type: 'link', label: 'Get Started', href: '/Create-a-New-Convention' },
            { type: 'link', label: 'Using the Farm Convention Publication Pipeline', href: '/Create-a-New-Convention' },
        ],
      },
      {
        type: 'category',
        label: 'Developer Documentation',
        className: 'sidebar-category--bold',
        items: [
            {
                type: 'link',
                href: 'https://gitlab.com/our-sci/conventions/common_farm_conventions',
                label: 'Common Farm Convention Repository'
            }, 
            {
              type: 'link',
              href: 'https://gitlab.com/our-sci/software/conventions_builder',
              label: 'Conventions Builder Repository'
          },
          {
                type: 'link',
                href: 'https://our-sci.gitlab.io/software/conventions_builder/',
                label: 'Conventions Builder Documentation'
            },
        ],
      },
      {
        type: 'category',
        label: 'Example Projects',
        className: 'sidebar-category--bold',
        items: [
            'Pasa'
        ],
      },
      {
          type: 'category',
          label: '📚 Conventions',
          className: 'sidebar-category--bold sidebar-category--conventions',
          collapsed: false,
          items: conventionCategories.conventionCategories
      }
  ],
};

module.exports = sidebars;
