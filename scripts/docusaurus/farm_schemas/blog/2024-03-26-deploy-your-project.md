---
slug: deploy-your-conventions-repo
title: How to get your conventions system up and running
authors: oduarte
tags: [json_schema, conventions, yeoman, scaffolding, setup, configuration, code, node, javascript]
---


# Introduction

  Since the convention system coordinates a lot of different pieces of software, configuration could become complex. 
  In order to avoid this, we've created an efficient installation process based on Yeoman generators.
  Now, getting your repo configured and working only requires answering some questions and taking some easy (and optional) follow up steps.
  
## Requirements

  This project is currently based on GitLab CI/CD. This could be adapted to other CI systems rather easily, but in the meanwhile, you will need a GitLab project in order to test the full benefits of the system.
  Creating an account is currently free and will give you everything you need to run this project.
  
# Installation process

## Which tool are we using

  Yeoman is a scaffolding helper tool. It helped us create an installer that will configure everything on your repo based on your needs and data, and even allow you to update it in the future.
  The installer itself is a package. We will begin our project from scratch, in an empty folder, install this package and use it to transform our empty project into a Conventions Builder.
  
# Steps

## First Step: Create a GitLab project

  We will use GitLab's CI/CD system to run this project's many features and host our custom Wiki.
  It should be empty, as the installer will generate the whole structure. Below, a link by GitLab themselves explaining how to create the repo if you haven't done it before.
  
* [create an empty GitLab project](https://docs.gitlab.com/ee/user/project/). Let's assume the URL for new project is: https://gitlab.com/our-sci/conventions/test_set .
* Clone the repo on an empty local folder (`git clone https://gitlab.com/our-sci/conventions/test_set.git`).
* Have it's URL at hand. We will also provide it to our scaffolder.

### About the URL

  In our case, we've used a project group. You might not have used one. 
  If your URL has three sections, as ours does, it decomposes this way:
  
  1. URL with a project group: https://gitlab.com/our-sci/conventions/test_set.git
    * `our-sci` is the organization/user.
    * `conventions` is the project group.
    * `test_set` is the name of the actual project.
  2. An URL without a project group will have one section less: https://gitlab.com/our-sci/test_set.git . In this case, you will leave that answer blank when asked.
    * `our-sci` is the organization/user.
    * `test_set` is the name of the actual project.

## Second Step: Install the generator

  As we said, the generator (`Yeoman` installers are called "generators") is an NPM package. 
  
  * On your cloned repo, you can install it using `npm install --save-dev generator-farm_convention_builder`. Install it as a dev dependency, as the final user won't need it at all.
  
## Third Step: Run the generator

  You can call your generator using this command:
  
  * `yo farm_convention_builder`.
  

  It will ask you for several pieces of information about your project, which will be used to configure everything. You will be asked for things such as a name for the project, a short and a long description, etc. 
  You will need to answer several questions about your repo, that's why we suggested having the URL at hand before doing this, which will allow you to look at your already created URL.
  **Don't push your changes into the repo yet.** We will configure some variables first.
  
  The generator will store your configuration. You can run it again at whatever moment in your project's life cycle and it will update your structure. If you make a mistake, you can just call it again and enter the proper data and it will fix everything. Results you enter on your first use are stored as defaults and suggested next time you call it.
  
  ![Example prompt](./tutorial_generator_prompt.png)

## Fourth Step: GitLab Project configuration

### Copy your environment variables into GitLab

  The scaffolder created a `.env` file listing the environment variables you provided. The same variables need to be registered as [CI variables in the gitlab repo](https://docs.gitlab.com/ee/ci/variables/#for-a-project).
  
* Go to **Settings** -> **CI/CD** and, next to the **Variables** paragraph, click on the **Expand** button to the right. For each variable, you click on the **Add variable** button which is no top of the **CI/CD variables** table and add the key and value.

* **Important**: If you intend to publish an NPM package, an extra variable, `NPM_AUTH_TOKEN` needs to be added, which is the [key for your NPM account](https://docs.npmjs.com/about-access-tokens).

### Set your **gitlab pages** URL to be _predictable_

* In the left sidebar, go to **Deploy** -> **Pages**.
* There's an option called **Used unique domain**. It currently is a default. We want it unchecked. This will ensure all of our links work in a predictable structure.
* Have this **project URL** at hand. In our case, it is https://our-sci.gitlab.io/conventions/test_set .

## Push all the changes into your `main` branch.

  Now that the project has the required variables, we can push the changes into our remote.
  
* `git add -A`
* `git commit -m "Project initialized with scaffolder."`
* `git push`


### A CI should start in your **Build** -> **Pipelines** section

  It will do several things.
  Once it is finished, you will be able to visit your wiki containing a first document, the example definition we have provided, which comes from our first example convention, explained in [this tutorial](https://our-sci.gitlab.io/conventions/common_farm_conventions/wiki/blog/using-the-convention-builder).
  
  You can get into your wiki using the projects URL, plus the `wiki` path. 
  In our case, the URL is https://our-sci.gitlab.io/conventions/test_set and we can see the wiki by going into https://our-sci.gitlab.io/conventions/test_set/wiki


## Fifth Step: Finish the configuration (post install)

  We will add a section to your README file describing the last steps you need to follow or can follow. 
  You might have performed all of this steps already. 
  Edit your `README.md` to remove this suggestions section and add more details about your own project.
