source .env
# first, build all overlays, as some schemas require overlays defined in definitios for other schemas and we don't want to keep track of dependencies

for file in definitions/*
do if [ -f $file/definition.js ]; then
       echo $file; node $file/definition.js overlaysOnly
   fi
done;

# once all overlays exists, build everything
for file in definitions/*
do if [ -f $file/definition.js ]; then
       echo $file; node $file/definition.js
   fi
done;
