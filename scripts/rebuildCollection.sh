#! /usr/bin/env bash

## This script will get into the `definitions` folder and, for every submodule/folder inside, retrieve the definitions associated with it and prepare them to be compared againast the cache and built if needed, for documentation and validator distribution.

for file in definitions/*
do
    cd $file;
    convention_folder=$(echo $file | awk -F"/" '{print $2}')
    echo "Working on $convention_folder convention folder"
    git fetch --tags
    for tag in $(git tag)
    do
        echo $tag
        ## Avoid verbose warnings
        git config advice.detachedHead false
        git checkout $tag
        ## We need to build all overlays first, because there might be dependencies across conventions.
        node definition.js overlaysOnly
        echo $file
    done;
    git checkout main
    ## Restore default warnings behaviour.
    git config advice.detachedHead true
    echo $PWD
    cd ../..
done

for file in definitions/*
do
    cd $file;
    convention_folder=$(echo $file | awk -F"/" '{print $2}')
    echo "Working on $convention_folder convention folder"
    for tag in $(git tag)
    do
        echo $tag
        ## Avoid verbose warnings
        git config advice.detachedHead false
        git checkout $tag
        node definition.js
        ## We need to build all overlays first, because there might be dependencies across conventions.
        echo $file
    done;
    git checkout main
    ## Restore default warnings behaviour.
    git config advice.detachedHead true
    echo $PWD
    cd ../..
done
