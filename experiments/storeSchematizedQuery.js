let irrigationConventionExample = {
    id: irrigation_convention_uuid,
    type: "Object",
    "asset--plant--planting": {
        id: asset__plant__planting_uuid,
        attributes: asset__plant__planting_attributes
    },
    "log--input--irrigation": {
        id: log__input__irrigation_uuid,
        attributes: {
            name: "example irrigation log",
            status: "done",
            timestamp: ( new Date() ).toISOString(),
            // the schema is stored somewhere.
            // you mention it, you get it.
            data: {
                source:'noaa_climate',
                // type: 'json',
                // query: {
                //     lat:'',
                //     lon:'',
                //     temp:'',
                //     avgTemp:''
                // }
            }
        },
        relationships: {
            asset: { data: [
                {
                    type: "asset--plant",
                    id: asset__plant__planting_uuid
                }
            ] },
            category: { data: [
                {
                    type: "taxonomy_term--log_category",
                    id:taxonomy_term__log_category__irrigation_uuid
                }
            ] },
            quantity: { data: [
                {
                    type: "quantity--standard",
                    id: quantity__standard__area_percentage_uuid
                },
                {
                    type: "quantity--material",
                    id: quantity__material__total_water_uuid
                },
                {
                    type: "quantity--standard",
                    id: quantity__standard__effectiveness_uuid
                }
            ] }
        },
    },
    "taxonomy_term--log_category--irrigation": {
        id: taxonomy_term__log_category__irrigation_uuid,
        attributes: {
            name: "irrigation"
        },
    }, 
    "quantity--standard--area_percentage": {
        id: quantity__standard__area_percentage_uuid,
        attributes: {
            label: "area"
        },
        relationships: {
            units: { data: [
                {
                    type:"taxonomy_term--unit",
                    id: taxonomy_term__unit__percentage_uuid
                }
            ] }
        }
    },
    "taxonomy_term--unit--%":  {
        id: taxonomy_term__unit__percentage_uuid,
        attributes: {
            name: "%"
        },   
    },
    "quantity--material--total_water": {
        id: quantity__material__total_water_uuid,
        attributes: {
            label: "total_water"
        },
        relationships: {
            units: { data: [
                {
                    type:"taxonomy_term--unit",
                    id: taxonomy_term__unit__total_water_uuid
                }
            ] }
        }
    },
    "taxonomy_term--unit--total_water":  {
        id: taxonomy_term__unit__total_water_uuid,
        attributes: {
            name: "inches"
        },   
    },
    "quantity--standard--effectiveness": {
        id: quantity__standard__effectiveness_uuid,
        attributes: {
            label: "effectiveness"
        },
        relationships: {
            units: { data: [
                {
                    type:"taxonomy_term--unit",
                    id: taxonomy_term__unit__effectiveness_rating_uuid
                }
            ] }
        }
    },
    "taxonomy_term__unit__effectiveness_rating":  {
        id: taxonomy_term__unit__effectiveness_rating_uuid,
        attributes: {
            name: "Under watered (0)"
        },   
    }
};
