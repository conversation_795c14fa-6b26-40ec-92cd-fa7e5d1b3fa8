const { randomUUID } = require('crypto');
const {prepareDrupalSchemaForAJV, buildValidator} = require('../src/schema_utilities.js');
const standaloneCode = require("ajv/dist/standalone").default;
const fs = require('fs');

let cometFarmSchema = JSON.parse( fs.readFileSync("./comet_farm_schema.json") );

let validatorObj = buildValidator();
let validatorFunction = validatorObj.compile(cometFarmSchema);

// ERROR Observed
/* We obtain an error when trying to validate the schema.
* Uncaught:
* Error: reference "#/properties/Cropland/properties/CropScenario/properties/Crop/properties/OMADApplicationList/properties/OMADApplicationEvent" resolves to more than one schema
* at ambiguos (/home/<USER>/OurSci/json_schema/node_modules/ajv/dist/compile/resolve.js:151:16)
* at Ajv2020.addRef (/home/<USER>/OurSci/json_schema/node_modules/ajv/dist/compile/resolve.js:118:23)
*/


let failedSection = cometFarmSchema.properties.Cropland.properties.CropScenario.properties;
failedSection;

let modifiedSchema = structuredClone(cometFarmSchema); 

// fix the section identified as an array but provided with properties.
modifiedSchema.properties.Cropland.properties.CropScenario.properties.CropYear.type = 'object';


let validatorModified = validatorObj.compile(modifiedSchema);

// stil fails

// the attribute and its sub attribute have the same $id assigned, which makes them amgiuous
modifiedSchema.properties.Cropland.properties.CropScenario.properties.CropYear.properties.Crop.properties.OMADApplicationList.properties.OMADApplicationEvent.$id;
modifiedSchema.properties.Cropland.properties.CropScenario.properties.CropYear.properties.Crop.properties.OMADApplicationList.properties.OMADApplicationEvent.properties.OMADApplicationDate.$id;

delete modifiedSchema.properties.Cropland.properties.CropScenario.properties.CropYear.properties.Crop.properties.OMADApplicationList.properties.OMADApplicationEvent.properties.OMADApplicationDate.$id;


let validator3 = validatorObj.compile(modifiedSchema);
// ERROR Observed
// schema is invalid: data/properties/@cometEmailId/$id must match pattern "^[^#]*#?$"
// In this new file, I removed ids by hand and changed the array into object as done before.

let editedCometFarmSchema = JSON.parse(fs.readFileSync("./comet_farm_schema_modified.json"));

let validator4 = validatorObj.compile(editedCometFarmSchema);
