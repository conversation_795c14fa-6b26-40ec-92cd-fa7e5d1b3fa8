const builder = require("convention_builder");
let baseSchemataFolder = `${__dirname}/../input/collection`;
const { randomUUID } = require('crypto');


// let's use GeoJSON for one of the wronge examples
let geoJsonExample = {
    "type": "Feature",
    "geometry": {
        "type": "Point",
        "coordinates": [125.6, 10.1]
    },
    "properties": {
        "name": "Dinagat Islands"
    }
};

const tillageLogActivity = new builder.SchemaOverlay({
    typeAndBundle:"log--activity",
    name:"tillage",
    validExamples: [
        {
            attributes:
            {
                name:"tillage",
                status:"done",
                timestamp: ( new Date() ).toISOString(),
                geometry: { value: "POINT((1 2))" }
            },
            id: randomUUID() },
        {
            attributes:
            {
                name:"tillage",
                status:"done",
                timestamp: ( new Date() ).toISOString(),
                geometry: { value: "POLYGON((1 2, 2 4, 5 6))" }
            },
            id: randomUUID() }
    ],
    erroredExamples: [
        // This is valid WKT, but not the type we asked for.
        {
            attributes: {
                name:"tillage",
                status:"done",
                timestamp: ( new Date() ).toISOString(),
                geometry: { value: "LINE((1 2, 2 4, 5 6))" }},
            id: randomUUID()
        },
        // This is GeoJSON
        {
            attributes: {
                name:"tillage",
                status:"done",
                timestamp: ( new Date() ).toISOString(),
                geometry: { value: JSON.stringify(geoJsonExample) }
            },
            id: randomUUID()
        },
        // This is Stringified GeoJSON
        {
            attributes: {
                name:"tillage",
                status:"done",
                timestamp: ( new Date() ).toISOString(),
                geometry: { value: geoJsonExample }},
            id: randomUUID()
        },
        // This is a more nuanced error: the WKT provided does contain the keywords we want, but it is actualy a COLLECTION, which is not good because it can be anything, it is too loose.
        {
            attributes: {
                name:"tillage",
                status:"done",
                timestamp: ( new Date() ).toISOString(),
                geometry: "GEOMETRYCOLLECTION (POINT (40 10),LINESTRING (10 10, 20 20, 10 40),POLYGON ((40 40, 20 45, 45 30, 40 40)))",
                id: randomUUID()
        },
      },
        // One of our valid examples, but with a typo
        {
            attributes:
            {
                name:"tillage",
                status:"done",
                timestamp: ( new Date() ).toISOString(),
                geometry: { value: "POINT1,2" }
            },
            id: randomUUID() },
    ],
    baseSchemataFolder: baseSchemataFolder
});


let currentPattern = "((POLYGON)|(POINT))\\(((\\(?[0-9]* [0-9]*,*\\)?)*)\\)";
// let currentPattern = "^POLYGON\s*\\(\\(((\s*-?\d+(\.\d+)?\s+-?\d+(\.\d+)?\s*,?)+)\\)\\)$";


tillageLogActivity.setPattern( { attribute:"geometry.properties.value", pattern:currentPattern, description:"Test description." } );


// The best pattern should be ^((POINT)|(POLYGON))\((\(\d+(?:\.\d*)?,\d+(?:\.\d*)?\))+\)

let testResults = tillageLogActivity.testExamples();


// This is the schema we've written.
tillageLogActivity.schema;
// Particularly.
tillageLogActivity.schema.properties.attributes.properties.geometry;
tillageLogActivity.schema.properties.attributes.properties.geometry.properties.value;


// Let's look first at the correct examples, which are never too eloquent.
// We can see these have geometries looking as we intend.
testResults
    .validExamples
    .map( d => d.entity.attributes.geometry )
;

// All the errors we provided are detected
// These are their geometries
testResults
    .erroredExamples
    .map( d => d.entity.attributes.geometry )
;
// These are the error messages we get.
testResults.erroredExamples.map(d => d.errors );

// Message generated by our example error.
testResults.erroredExamples[0].entity;
testResults.erroredExamples[0].errors;
