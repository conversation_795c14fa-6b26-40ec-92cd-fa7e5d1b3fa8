path,path_simplified,name,required,type,title,maxLength,description,default,format,items.type,items.title,items.maxLength
properties.attributes.properties.name,attributes.name,name,false,string,Name,255,The name of the log. Leave this blank to automatically generate a name.,,,,,
properties.attributes.properties.timestamp,attributes.timestamp,timestamp,true,string,Timestamp,,Timestamp of the event being logged.,,date-time,,,
properties.attributes.properties.status,attributes.status,status,true,string,Status,255,Indicates the status of the log.,pending,,,,
properties.attributes.properties.is_termination,attributes.is_termination,is_termination,false,boolean,Is termination,,"If this log is a termination, then all assets referenced by it will be marked as terminated and archived at the time the log takes place. The log must be complete in order for the movement to take effect.",,,,,
properties.attributes.properties.data,attributes.data,data,false,string,Data,,,,,,,
properties.attributes.properties.notes.properties.value,attributes.notes.value,value,true,string,Text,,,,,,,
properties.attributes.properties.notes.properties.format,attributes.notes.format,format,false,string,Text format,,,,,,,
properties.attributes.properties.flag,attributes.flag,flag,false,array,Flags,,Add flags to enable better sorting and filtering of records.,,,string,Text value,
properties.attributes.properties.geometry.properties.value,attributes.geometry.value,value,false,string,Geometry,,,,,,,
properties.attributes.properties.geometry.properties.geo_type,attributes.geometry.geo_type,geo_type,false,string,Geometry Type,,,,,,,
properties.attributes.properties.geometry.properties.lat,attributes.geometry.lat,lat,false,number,Centroid Latitude,,,,,,,
properties.attributes.properties.geometry.properties.lon,attributes.geometry.lon,lon,false,number,Centroid Longitude,,,,,,,
properties.attributes.properties.geometry.properties.left,attributes.geometry.left,left,false,number,Left Bounding,,,,,,,
properties.attributes.properties.geometry.properties.top,attributes.geometry.top,top,false,number,Top Bounding,,,,,,,
properties.attributes.properties.geometry.properties.right,attributes.geometry.right,right,false,number,Right Bounding,,,,,,,
properties.attributes.properties.geometry.properties.bottom,attributes.geometry.bottom,bottom,false,number,Bottom Bounding,,,,,,,
properties.attributes.properties.geometry.properties.geohash,attributes.geometry.geohash,geohash,false,string,Geohash,,,,,,,
properties.attributes.properties.geometry.properties.latlon,attributes.geometry.latlon,latlon,false,string,LatLong Pair,,,,,,,
properties.attributes.properties.is_movement,attributes.is_movement,is_movement,false,boolean,Is movement,,"If this log is a movement, then all assets referenced by it will be located in the referenced locations and/or geometry at the time the log takes place. The log must be complete in order for the movement to take effect.",,,,,
properties.attributes.properties.surveystack_id,attributes.surveystack_id,surveystack_id,false,string,Surveystack ID,255,,,,,,
properties.attributes.properties.quick,attributes.quick,quick,false,array,Quick form,,References the quick form that was used to create this record.,,,string,Text value,255
properties.attributes.properties.lot_number,attributes.lot_number,lot_number,false,string,Lot number,255,"If this harvest is part of a batch or lot, enter the lot number here.",,,,,
properties.attributes.properties.method,attributes.method,method,false,string,Method,255,How was this input applied?,,,,,
properties.attributes.properties.purchase_date,attributes.purchase_date,purchase_date,false,string,Purchase date,,When was this input purchased (if applicable)?,,date-time,,,
properties.attributes.properties.source,attributes.source,source,false,string,Source,255,Where was this input obtained? Who manufactured it?,,,,,
properties.relationships.properties.file,relationships.file,file,,,,,,,,,,
properties.relationships.properties.image,relationships.image,image,,,,,,,,,,
properties.relationships.properties.location,relationships.location,location,,,,,,,,,,
properties.relationships.properties.asset,relationships.asset,asset,,,,,,,,,,
properties.relationships.properties.category,relationships.category,category,,,,,,,,,,
properties.relationships.properties.quantity,relationships.quantity,quantity,,,,,,,,,,
properties.relationships.properties.owner,relationships.owner,owner,,,,,,,,,,