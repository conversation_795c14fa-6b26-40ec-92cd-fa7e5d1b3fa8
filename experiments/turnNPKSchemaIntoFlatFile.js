// We will use a function that, given a complex JSON schema, will provide a flattened table allowing to analize it or create derivatives using a spreadhseet, such as the ones employed when working in Excel or LibreOffice's Calc. Notice the CSV library 'papaparse' is required (it is not a dependency of this repo otherwise).
const papa = require('papaparse');

//Notice the new function is included in the "convention_builder" library.
const { flattenJSONSchema } = require('convention_builder');


// Let's read and parse an example convention.
let schema = JSON.parse( fs.readFileSync("../input/collection/log/input/schema.json") );

// Flattening it will give us a set of mostly identical objects.
let flattenedSchema = flattenJSONSchema(schema);

// Let's have a look at one:
flattenedSchema[2];

//
// SHOWS
//

/*
* {
*     path: 'properties.log--input--irrigation.properties.attributes.properties.timestamp',
*     name: 'timestamp',
*     type: 'string',
*     title: 'Timestamp',
*     format: 'date-time',
*     description: 'Timestamp of the event being logged.'
* }
*/ 


// The function that stores it needs to know the names of all columns. Let's obtain it first (since not all types have the same columns).

let allColumns = Array.from( new Set( flattenedSchema.flatMap( d => Object.keys(d) ) ) )
.filter( d => !d.match("--") )
;

// Transform into a CSV with all our columns.
let csvVersion = papa.unparse( flattenedSchema, {columns: allColumns} );

// Store
fs.writeFileSync( "./flattenedLogInputEntity.csv", csvVersion );


