// We try to define a convention using JSON schema and test its validation.
// To learn about the JSON schema specification and get further resources, we recommend the book [Understanding JSON Schema]( https://json-schema.org/understanding-json-schema )
// After defining the schemas, I created a full and a minimal example to see how the data described by the schema looks like and how that interacts with the validator.

const { randomUUID } = require('crypto');
const {prepareDrupalSchemaForAJV, buildValidator} = require('convention_builder');
const standaloneCode = require("ajv/dist/standalone").default;
const fs = require('fs');

// While writing this JSON schema, I commented with the names of the fields you designed on the MIRO board, prepended by `MIRO:`
// I also prepended some other stuff with the comment `JSON schema` when the field is relevant.
// In this case, I designed the schema assuming the format will be similar to the one we use for FarmOS and farm conventions, in which the actual data is stored inside an "attributes" property, and the metadata is outside of it.
let cropActivity = {
    // 
    $id: "ghg/crop_activity_log",
    title: "Crop Activity Log",
    type: 'object',
    required: [ "attributes", "id" ],
    properties: {
        schema_type: { const: "crop_activity" },
        // MIRO: ID
        id: {
            type: "string",
            format: "uuid"
        },
        attributes: {
            type: "object",
            required: ['timestamp', 'geometry'],
            properties: {
                // MIRO: Timestamp
                timestamp: {
                    type: "string",
                    title: "Timestamp",
                    format: "date-time",
                    description: "Timestamp of the event being logged."
                },
                // MIRO: Activity is part of program enrollment (Y/N, if Yes, name program and practice code)
                program_enrollment_activity: {
                    "type": "number",
                    "title": "Program Enrollment Activity",
                    "oneOf": [
                        {
                            "const": 0,
                            "title": "No"
                        },
                        {
                            "const": 1,
                            "title": "Yes"
                        }
                    ],
                    "description": "Activity is part of program enrollment (Y/N, if Yes, name program and practice code)"
                },
                // MIRO: Data source (contract, survey, imputation, on-site investigation, etc)
                data_source: {
                    type: "string",
                    title: "Data Source",
                    enum: [
                        "contract",
                        "survey",
                        "imputation",
                        "on site investigation"
                    ],
                    description: "Data source (contract, survey, imputation, on-site investigation, etc)"
                },
                // Species (multiselect)
                // Cultivar (multiselect)
                // Is cover crop? Y/N
                // Is perennial? Y/N
                // Growth Stage
                // MIRO: Crop Event Type (Planting, Harvest/Termination, Pruning, Mulching, Invasive control, weeding, rotation?)
                crop_event_type: {
                    type: "string",
                    title: "Crop Event Type",
                    enum: [
                        "tillage",
                        "harvest",
                        "seeding"
                    ],
                    description: "One of the crop events that this schema represents."
                },
                // Yield
                // Seeding Method
                // Termination Method (Burning, grazing, mowing, harvesting, etc)
                // Residue Removal %
                //     Equipment ID
                // Equipment Settings ID
                // Planting Rate (seeds/ha)
                // Planting Depth (cm)
                // MIRO: Geom
                geometry: {
                    type: "string",
                    description: "Geometry is encoded as a WKT string. We are enforcing it to be a polygon or a point via RegExp matching.",
                    pattern: "((POLYGON)|(POINT))\\(((\\(?[0-9]* [0-9]*,*\\)?)*)\\)"
                }
            }
        },
    },
};

// Use our helper to get an AJV validator object, already pre configured.
let generalValidator = buildValidator({code: {source: true}});

let cropActivityValidator = generalValidator.compile(cropActivity);

// compile static validator module
let moduleCode = standaloneCode(generalValidator, cropActivityValidator);


// This example only has the mandatory (required) fields.
let minimalExample = {
    id: randomUUID(),
    attributes: {
        timestamp: ( new Date() ).toISOString(),
        geometry: "POLYGON(  0 0, 0 1, 1 1, 1 0 )"
    }
};

// let's check how it does with our validator
cropActivityValidator(minimalExample);
let minimalExampleErrors = cropActivityValidator.errors;

console.log("minimal valid example");
console.log(minimalExample);
console.log(`did the validator accept it? ${cropActivityValidator(minimalExample)}`);
console.log(`detected errors`);
console.log(minimalExampleErrors);


let fullExample = {
    id: randomUUID(),
    attributes: {
        timestamp: ( new Date() ).toISOString(),
        geometry: "POLYGON(  0 0, 0 1, 1 1, 1 0 )",
        program_enrollment_activity: 0,
        data_source:"survey",
        crop_event_type:"harvest",
    },
};

cropActivityValidator(fullExample);
let fullExampleErrors = cropActivityValidator.errors;

console.log("full valid example");
console.log(fullExample);
console.log(`did the validator accept it? ${cropActivityValidator(fullExample)}`);
console.log(`detected errors`);
console.log(fullExampleErrors);


// Now, let's see how errors are detected.

let errorExample = {
    // the id will be missing
    // id: randomUUID(),
    attributes: {
        // the timestamp will have a wrong format.
        timestamp: "1/1/1972",
        // The geometry will be a line.
        geometry: "LINE(  0 0, 0 1 )",
        // enrollment will be a string
        program_enrollment_activity: "yes, the new program from Idaho",
        // the data source is a ling
        data_source:"http://survey.com/myresult",
        // the crop event is of an unknown type
        crop_event_type:"polination",
    },
};

cropActivityValidator(errorExample);
let errorExampleErrors = cropActivityValidator.errors;

console.log("error example");
console.log(errorExample);
console.log(`did the validator accept it? ${cropActivityValidator(errorExample)}`);
console.log(`detected errors`);
console.log(errorExampleErrors);
