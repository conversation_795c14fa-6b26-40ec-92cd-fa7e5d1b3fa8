const patterns = {
  polygon: /^POLYGON\s*\(\(((\s*-?\d+(\.\d+)?\s+-?\d+(\.\d+)?\s*,?)+)\)\)$/,
  multipolygon: /^MULTIPOLYGON\s*\(\(\(\s*((\s*-?\d+(\.\d+)?\s+-?\d+(\.\d+)?\s*,?)+)\s*\)\)\)$/,
  geometrycollection_polygon_multipolygon: /^GEOMETRYCOLLECTION\s*\((MULTIPOLYGON\s*\(\(\(\s*((\s*-?\d+(\.\d+)?\s+-?\d+(\.\d+)?\s*,?)+)\s*\)\)\)|POLYGON\s*\(\(((\s*-?\d+(\.\d+)?\s+-?\d+(\.\d+)?\s*,?)+)\)\))\)$/,
  point: /^POINT\s*\((-?\d+(\.\d+)?\s+-?\d+(\.\d+)?)\)$/,
  multipoint: /^MULTIPOINT\s*\(((\s*-?\d+(\.\d+)?\s+-?\d+(\.\d+)?\s*,?)+)\)$/,
  geometrycollection_point_multipoint: /^GEOMETRYCOLLECTION\s*\((MULTIPOINT\s*\(((\s*-?\d+(\.\d+)?\s+-?\d+(\.\d+)?\s*,?)+)\)|POINT\s*\((-?\d+(\.\d+)?\s+-?\d+(\.\d+)?)\))\)$/
};

exports.patterns = patterns;

// Expanded example test strings that should match
const testStringsValid = {
  polygon: [
    "POLYGON ((30 10, 40 40, 20 40, 10 20, 30 10))",
    "POLYGON ((10 10, 20 20, 10 20, 10 10))" // Simplified but valid
  ],
  multipolygon: [
    "MULTIPOLYGON (((30 20, 45 40, 10 40, 30 20)))",
    "MULTIPOLYGON (((10 10, 20 20, 10 20, 10 10)),((30 30, 40 40, 30 40, 30 30)))" // Multiple polygons
  ],
  geometrycollection_polygon_multipolygon: [
    "GEOMETRYCOLLECTION (POLYGON ((30 10, 40 40, 20 40, 10 20, 30 10)))",
    "GEOMETRYCOLLECTION (MULTIPOLYGON (((30 20, 45 40, 10 40, 30 20))))"
  ],
  point: [
    "POINT (30 10)",
    "POINT (-73.935242 40.730610)" // Longitude and latitude of a real-world location
  ],
  multipoint: [
    "MULTIPOINT ((10 40), (40 30), (20 20), (30 10))",
    "MULTIPOINT ((-73.935242 40.730610), (-74.935242 41.730610))" // Real-world locations
  ]
};

// Test each pattern for valid strings (including technically passing but invalid WKT examples)
console.log("VALID EXAMPLES")
Object.entries(testStringsValid).forEach(([key, values]) => {
  values.forEach(value => {
    if (patterns[key].test(value)) {
      console.log(`${key} - "${value}": valid string passes validation.`);
    } else {
      console.log(`${key} - "${value}": valid string failed validation.`);
    }
  });
});

// Expanded example test strings that should not match
const testStringsInvalid = {
  polygon: [
    "POLYGON (30 10, 40 40, 20 40, 10 20, 30 10)", // Missing double parentheses
    "POLYGON ((30 10, 40 40, 20))", // Not closed
    "POLYGON ((30 10, 40 40, 20, 10 20, 30 10))" // Missing coordinate pair
  ],
  multipolygon: [
    "MULTIPOLYGON ((30 20, 45 40, 10 40, 30 20))", // Incorrect structure
    "MULTIPOLYGON (((30 20, 45 40, 10)))" // Incomplete polygon
  ],
  geometrycollection_polygon_multipolygon: [
    "GEOMETRYCOLLECTION (LINESTRING (30 10, 10 30, 40 40))", // Wrong geometry type
    "GEOMETRYCOLLECTION (POLYGON ((30 10, 40)))" // Invalid polygon in collection
  ],
  point: [
    "POINT 30 10", // Missing parentheses
    "POINT (30)" // Missing one coordinate
  ],
  multipoint: [
    "MULTIPOINT (10 40, 40 30, 20 20, 30 10)", // Missing double parentheses
    "MULTIPOINT ((30 10, 40))" // Incomplete point
  ]
};

// Test each pattern for invalid strings
console.log("INVALID EXAMPLES")
Object.entries(testStringsInvalid).forEach(([key, values]) => {
  values.forEach(value => {
    if (!patterns[key].test(value)) {
      console.log(`${key} - "${value}": invalid string correctly fails validation.`);
    } else {
      console.log(`${key} - "${value}": invalid string incorrectly passes validation.`);
    }
  });
});

const testStringsValidButActuallyInvalid = {
  polygon: [
    "POLYGON ((30 10, 40 40, 20 40, 30 10))", // Missing closing coordinate to properly close the polygon
    "POLYGON ((999999 999999, 888888 888888, 777777 777777, 999999 999999))" // Unrealistic coordinate values
  ],
  multipolygon: [
    "MULTIPOLYGON (((0 0, 1 1, 0 1, 0 0)),((2 2, 3 3, 2 3, 2 2)))" // Valid format, but too small to be realistic
  ],
  geometrycollection_polygon_multipolygon: [
    "GEOMETRYCOLLECTION (POLYGON ((-180 -90, 180 -90, 180 90, -180 90, -180 -90)))" // Encompasses the entire globe, technically possible but unusual
  ],
  point: [
    "POINT (999999 999999)" // Unrealistic coordinate values
  ],
  multipoint: [
    "MULTIPOINT ((-999999 -999999), (999999 999999))" // Unrealistic coordinate values
  ]
};

// Test each pattern for valid but actually invalid WKT examples
console.log("INVALID EXAMPLES THAT PASS")
Object.entries(testStringsValidButActuallyInvalid).forEach(([key, values]) => {
  values.forEach(value => {
    if (patterns[key].test(value)) {
      console.log(`${key} - "${value}": invalid WKT passes validation.`);
    } else {
      console.log(`${key} - "${value}": invalid WKT fails validation (unexpected).`);
    }
  });
});
