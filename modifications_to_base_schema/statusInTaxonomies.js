const builder = require("convention_builder");
const fs = require("fs");

// let originalSchemaExample = JSON.parse( fs.readFileSync( '/home/<USER>/OurSci/json_schema/node_modules/convention_builder/dist/node/../../../../input/collection/log/transplanting/schema.json' ) );

let statusAttribute = new builder.InputConventionChanges({
    attribute: "status",
    type: "taxonomy_term",
    overlayName: "Status in taxonomies.",
    overlayDescription: "Remove status from taxonomy terms."
});

statusAttribute.deleteAttribute("status");

let storageOperation = statusAttribute.store();
