const builder = require("convention_builder");
const fs = require("fs");

// let originalSchemaExample = JSON.parse( fs.readFileSync( '/home/<USER>/OurSci/json_schema/node_modules/convention_builder/dist/node/../../../../input/collection/log/transplanting/schema.json' ) );

let booleanIsMovement = new builder.InputConventionChanges({
    attribute: "is_movement",
    overlayName: "Fix bug in booleans.",
    overlayDescription: "Fix bug in the design for boolean attributes."
});
let booleanSectionMovement = {
    type:"boolean",
    title: 'Is movement',
    description:"Indicates whether the log represents an asset movement. This section has a bug, which this overlay fixes. It asked for booleans, which are  either `true` or `false`, but also for 0 or 1 as unique accepted options, causing a contradiction."
};
booleanIsMovement.setGenericSection({
    section: booleanSectionMovement
});
let storageOperation = booleanIsMovement.store();


// let modifiedSchemaExample = JSON.parse( fs.readFileSync( '/home/<USER>/OurSci/json_schema/node_modules/convention_builder/dist/node/../../../../input/collection/log/transplanting/schema.json' ) );


// // version in which, as of 4 April 2024, there is a bug that makes it impossible to create a valid entity
// originalSchemaExample.properties.attributes.properties.is_movement;
// // movement version
// modifiedSchemaExample.properties.attributes.properties.is_movement;


let booleanIsFixed = new builder.InputConventionChanges({
    attribute: "is_fixed",
    overlayName: "Fix bug in booleans.",
    overlayDescription: "Fix bug in the design for boolean attributes."
});
let booleanSectionFixed = {
    type:"boolean",
    title: 'Is fixed',
    description:"Indicates if the asset can displace or is fixed permanently in a position. This section has a bug, which this overlay fixes. It asked for booleans, which are  either `true` or `false`, but also for 0 or 1 as unique accepted options, causing a contradiction."
};
booleanIsFixed.setGenericSection({
    section: booleanSectionFixed
});
let storageOperationFixed = booleanIsFixed.store();


let booleanIsLocation = new builder.InputConventionChanges({
    attribute: "is_location",
    overlayName: "Fix bug in booleans.",
    overlayDescription: "Indicate whether the entity is a location able to contain other assets.Fix bug in the design for boolean attributes."
});
let booleanSectionLocation = {
    type:"boolean",
    title: 'Is location',
    description:"This section has a bug, which this overlay fixes. It asked for booleans, which are  either `true` or `false`, but also for 0 or 1 as unique accepted options, causing a contradiction."
};
booleanIsLocation.setGenericSection({
    section: booleanSectionLocation
});
let storageOperationLocation = booleanIsLocation.store();
