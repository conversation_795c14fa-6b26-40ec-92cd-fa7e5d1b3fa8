[submodule "definitions/energy"]
	path = definitions/energy
	url = https://gitlab.com/our-sci/conventions/common_farm_convention_definitions/energy.git
[submodule "definitions/fertilizer"]
	path = definitions/fertilizer
	url = https://gitlab.com/our-sci/conventions/common_farm_convention_definitions/fertilizer.git
[submodule "definitions/field_asset"]
	path = definitions/field_asset
	url = https://gitlab.com/our-sci/conventions/common_farm_convention_definitions/field_asset.git
[submodule "definitions/flaming"]
	path = definitions/flaming
	url = https://gitlab.com/our-sci/conventions/common_farm_convention_definitions/flaming.git
[submodule "definitions/global_overlays"]
	path = definitions/global_overlays
	url = https://gitlab.com/our-sci/conventions/common_farm_convention_definitions/global_overlays.git
[submodule "definitions/grazing"]
	path = definitions/grazing
	url = https://gitlab.com/our-sci/conventions/common_farm_convention_definitions/grazing.git
[submodule "definitions/harvest"]
	path = definitions/harvest
	url = https://gitlab.com/our-sci/conventions/common_farm_convention_definitions/harvest.git
[submodule "definitions/herbicide"]
	path = definitions/herbicide
	url = https://gitlab.com/our-sci/conventions/common_farm_convention_definitions/herbicide.git
[submodule "definitions/irrigation"]
	path = definitions/irrigation
	url = https://gitlab.com/our-sci/conventions/common_farm_convention_definitions/irrigation.git
[submodule "definitions/lime"]
	path = definitions/lime
	url = https://gitlab.com/our-sci/conventions/common_farm_convention_definitions/lime.git
[submodule "definitions/modus_lab_test"]
	path = definitions/modus_lab_test
	url = https://gitlab.com/our-sci/conventions/common_farm_convention_definitions/modus_lab_test.git
[submodule "definitions/mowing"]
	path = definitions/mowing
	url = https://gitlab.com/our-sci/conventions/common_farm_convention_definitions/mowing.git
[submodule "definitions/organic_matter"]
	path = definitions/organic_matter
	url = https://gitlab.com/our-sci/conventions/common_farm_convention_definitions/organic_matter.git
[submodule "definitions/planting"]
	path = definitions/planting
	url = https://gitlab.com/our-sci/conventions/common_farm_convention_definitions/planting.git
[submodule "definitions/seeding"]
	path = definitions/seeding
	url = https://gitlab.com/our-sci/conventions/common_farm_convention_definitions/seeding.git
[submodule "definitions/seedling_treatment"]
	path = definitions/seedling_treatment
	url = https://gitlab.com/our-sci/conventions/common_farm_convention_definitions/seedling_treatment.git
[submodule "definitions/seed_treatment"]
	path = definitions/seed_treatment
	url = https://gitlab.com/our-sci/conventions/common_farm_convention_definitions/seed_treatment.git
[submodule "definitions/soil_test"]
	path = definitions/soil_test
	url = https://gitlab.com/our-sci/conventions/common_farm_convention_definitions/soil_test.git
[submodule "definitions/solarization"]
	path = definitions/solarization
	url = https://gitlab.com/our-sci/conventions/common_farm_convention_definitions/solarization.git
[submodule "definitions/termination"]
	path = definitions/termination
	url = https://gitlab.com/our-sci/conventions/common_farm_convention_definitions/termination.git
[submodule "definitions/tillage"]
	path = definitions/tillage
	url = https://gitlab.com/our-sci/conventions/common_farm_convention_definitions/tillage.git
[submodule "definitions/transplanting"]
	path = definitions/transplanting
	url = https://gitlab.com/our-sci/conventions/common_farm_convention_definitions/transplanting.git
[submodule "definitions/tree_biomass"]
	path = definitions/tree_biomass
	url = https://gitlab.com/our-sci/conventions/common_farm_convention_definitions/tree_biomass.git
[submodule "definitions/waste_water"]
	path = definitions/waste_water
	url = https://gitlab.com/our-sci/conventions/common_farm_convention_definitions/waste_water.git
