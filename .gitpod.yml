# actions executed before offering the environment to the user
tasks:
  - name: Environment initialization
    init: |
      npm install
      mkdir input
      node scripts/getAllSchemas.js
      bash scripts/applyAttributeOverlays.sh
      mkdir -p output/collection
      mkdir output/collection/conventions
      mkdir output/collection/overlays
      mkdir output/collection/documentation
      node scripts/compileAllValidators.js
      bash scripts/rebuildCollection.sh
      node scripts/transpilation.js
    command: echo "Environment ready."

