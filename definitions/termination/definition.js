// Survey: https://app.surveystack.io/surveys/6346c1e467f839000121503e/edit
// Section: 11, planting_div_veg
const builder = require('convention_builder');
const fs = require('fs');
require('dotenv').config({ path: `${__dirname}/../../.env` });

//Main entity
let log__activity__termination = new builder.SchemaOverlay({
    typeAndBundle: 'log--activity',
    name: 'termination',
});
log__activity__termination.setMainDescription("Add termination event if an termination date is included in the planting information.");

log__activity__termination.setConstant({
    attribute:"status",
    value:"done",
    description: "The status should always be set to done to inherit the area."
});

log__activity__termination.setConstant({
    attribute:"is_termination",
    value:"true",
});

//Convention
// Object
let terminationConvention = new builder.ConventionSchema({
    title: "Termination",
    version: "0.0.3",
    schemaName:"log--activity--termination",
    repoURL:"www.gitlabrepo.com/version/farmos_conventions",
    description:`## Purpose\n
Termination log to be applied to a plant asset only if the farmer indicated the date of termination.\n
## Specification\n
text\n`,
});

////add local attributes
terminationConvention.addAttribute( { schemaOverlayObject:log__activity__termination, attributeName: "log--activity--termination", required: true } );

//add global attributes
terminationConvention.addAttribute( { schemaOverlayObject:"taxonomy_term--log_category", attributeName: "taxonomy_term--log_category", required: false } );
terminationConvention.addAttribute( { schemaOverlayObject:"taxonomy_term--log_category--termination", attributeName: "taxonomy_term--log_category--termination", required: true});
terminationConvention.addAttribute( { schemaOverlayObject:"asset--plant--planting", attributeName: "asset--plant--planting", required: false } );
terminationConvention.addAttribute( { schemaOverlayObject: "quantity--standard--area_percentage", attributeName: "quantity--standard--area_percentage", required: false});
terminationConvention.addAttribute( { schemaOverlayObject: "taxonomy_term--unit--%", attributeName: "taxonomy_term--unit--%", required: false});
terminationConvention.addAttribute( { schemaOverlayObject: "asset--land", attributeName: "asset--land", required: false});

////add relationships
terminationConvention.addRelationship( { containerEntity:"log--activity--termination" , relationName:"category" , mentionedEntity:"taxonomy_term--log_category" , required: false } );
terminationConvention.addRelationship( { containerEntity: "log--activity--termination", relationName:"category", mentionedEntity:"taxonomy_term--log_category--termination", required: true});
terminationConvention.addRelationship( { containerEntity:"log--activity--termination", relationName:"asset", mentionedEntity:"asset--plant--planting", required: false } );
terminationConvention.addRelationship( { containerEntity:"log--activity--termination" , relationName:"quantity" , mentionedEntity:"quantity--standard--area_percentage" , required: false } );
terminationConvention.addRelationship( { containerEntity:"quantity--standard--area_percentage" , relationName:"units" , mentionedEntity:"taxonomy_term--unit--%" , required: false } );
terminationConvention.addRelationship( { containerEntity:"log--activity--termination" , relationName:"location" , mentionedEntity:"asset--land" , required: false } );



terminationConvention.addExample({ path: `${__dirname}/examples/correct`, is_valid_example: true });
terminationConvention.addExample({ path: `${__dirname}/examples/incorrect`, is_valid_example: false });

let storageOperation = terminationConvention.store();
