// Survey: https://app.surveystack.io/surveys/6346c1e467f839000121503e/edit
// Section: 28, flaming
const builder = require('convention_builder');
const fs = require('fs');
require('dotenv').config({ path: `${__dirname}/../../.env` });

//Main entity
////overlay
let log__activity__flaming = new builder.SchemaOverlay({
    typeAndBundle: 'log--activity',
    name: 'flaming',
});
//LOOK review description
log__activity__flaming.setMainDescription("Flaming logs are used for termination or weed control and allow for a single date or start date, end date and frequency.");

log__activity__flaming.setConstant({
    attribute:"status",
    value:"done"
});

log__activity__flaming.setPattern({
    attribute:"name",
    pattern:"(flaming)",
    description:"Flaming must be included in the name."
});

//convention
let flamingConvention = new builder.ConventionSchema({
    title: "Flaming",
    version: "0.0.2",
    schemaName:"log--activity--flaming",
    description:`## Purpose\n
Flaming logs can be created for termination or weed control.\n
## Specification\n
text\n`,
});

//add local attributes
flamingConvention.addAttribute( { schemaOverlayObject:log__activity__flaming, attributeName: "log--activity--flaming", required: true } );

//add global attributes
flamingConvention.addAttribute( { schemaOverlayObject:"taxonomy_term--log_category", attributeName: "taxonomy_term--log_category", required: false } );
flamingConvention.addAttribute( { schemaOverlayObject:"taxonomy_term--log_category--weed_control", attributeName: "taxonomy_term--log_category--weed_control", required: true } );
flamingConvention.addAttribute({ schemaOverlayObject:"quantity--standard--area_percentage", attributeName: "quantity--standard--area_percentage", required: false});
flamingConvention.addAttribute({ schemaOverlayObject:"taxonomy_term--unit--%", attributeName: "taxonomy_term--unit--%", required: false});
flamingConvention.addAttribute( { schemaOverlayObject:"asset--plant--planting", attributeName: "asset--plant--planting", required: false } );
flamingConvention.addAttribute( { schemaOverlayObject:"asset--land", attributeName: "asset--land", required: false } );

//relationships
flamingConvention.addRelationship( { containerEntity:"log--activity--flaming" , relationName:"category" , mentionedEntity:"taxonomy_term--log_category" , required: false } );
flamingConvention.addRelationship( { containerEntity:"log--activity--flaming" , relationName:"category" , mentionedEntity:"taxonomy_term--log_category--weed_control" , required: true } );
flamingConvention.addRelationship( { containerEntity:"log--activity--flaming" , relationName:"quantity" , mentionedEntity:"quantity--standard--area_percentage" , required: false } );
flamingConvention.addRelationship( { containerEntity:"quantity--standard--area_percentage" , relationName:"units" , mentionedEntity:"taxonomy_term--unit--%" , required: false } );
flamingConvention.addRelationship( { containerEntity:"log--activity--flaming", relationName:"asset", mentionedEntity:"asset--plant--planting", required: false } );
flamingConvention.addRelationship( { containerEntity:"log--activity--flaming", relationName:"location", mentionedEntity:"asset--land", required: false } );


// Rewrite the example
// Errors: [no timestamp for log--activity--flaming, 'status' must be constant 'done', taxonomy_term--log_category--weed_control 'name'
// must be constant 'weed_control']


flamingConvention.addExample({ path: `${__dirname}/examples/correct`, is_valid_example: true });
flamingConvention.addExample({ path: `${__dirname}/examples/incorrect`, is_valid_example: false });

// let test = flamingConvention.testExamples();
let storageOperation = flamingConvention.store();
