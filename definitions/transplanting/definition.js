// Survey: https://app.surveystack.io/surveys/6346c1e467f839000121503e/edit
// Section: 11, planting_div_veg
const builder = require('convention_builder');
require('dotenv').config({ path: `${__dirname}/../../.env` });

//Main entity
let log__transplanting__transplanting = new builder.SchemaOverlay({
    typeAndBundle: 'log--transplanting',
    name: 'transplanting',
});
log__transplanting__transplanting.setMainDescription("If the crop was seeded and then moved a transplanting log is required.");

log__transplanting__transplanting.setConstant({
    attribute:"status",
    value:"done",
    description: "The status should always be set to done to inherit the area."
});
log__transplanting__transplanting.setConstant({
    attribute: "is_movement",
    value: "true"
});

//Convention
let transplantingConvention = new builder.ConventionSchema({
    title: "Transplanting",
    version: "0.0.1",
    schemaName:"log--transplanting--transplanting",
    repoURL:"www.gitlabrepo.com/version/farmos_conventions",
    description:`## Purpose\n
A transplanting log is required when crops are seeded and then moved.\n
## Specification\n
text\n`,
});

//add local attributes
transplantingConvention.addAttribute({ schemaOverlayObject:log__transplanting__transplanting, attributeName: "log--transplanting--transplanting", required: true});

//add global attributes
transplantingConvention.addAttribute( { schemaOverlayObject:"taxonomy_term--log_category", attributeName: "taxonomy_term--log_category", required: false } );
transplantingConvention.addAttribute({ schemaOverlayObject:"taxonomy_term--log_category--transplanting", attributeName: "taxonomy_term--log_category--transplanting", required: true});
transplantingConvention.addAttribute({ schemaOverlayObject:"quantity--standard--area_percentage", attributeName: "quantity--standard--area_percentage", required: true});
transplantingConvention.addAttribute({ schemaOverlayObject:"taxonomy_term--unit--%", attributeName: "taxonomy_term--unit--%", required: true});
transplantingConvention.addAttribute( { schemaOverlayObject:"asset--plant--planting", attributeName: "asset--plant--planting", required: true } );

//Add relationships
transplantingConvention.addRelationship({ containerEntity:"log--transplanting--transplanting", relationName:"category", mentionedEntity:"taxonomy_term--log_category--transplanting", required:true});
transplantingConvention.addRelationship({ containerEntity:"log--transplanting--transplanting", relationName:"quantity", mentionedEntity:"quantity--standard--area_percentage", required:true});
transplantingConvention.addRelationship({ containerEntity:"quantity--standard--area_percentage", relationName:"units", mentionedEntity:"taxonomy_term--unit--%", required:true});
transplantingConvention.addRelationship( { containerEntity:"log--transplanting--transplanting", relationName:"asset", mentionedEntity:"asset--plant--planting", required: true } );

transplantingConvention.addExample({ path: `${__dirname}/examples/correct`, is_valid_example: true });
transplantingConvention.addExample({ path: `${__dirname}/examples/incorrect`, is_valid_example: false });

let storageOperation = transplantingConvention.store();


// let error = test.failedExamples[0].errors[0];
// transplantingConvention.schema.properties['log--transplanting--transplanting'].properties.attributes.properties.timestamp;
