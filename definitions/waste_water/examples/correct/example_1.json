{"id": "2abff6f4-52fa-40cb-a4b1-700b5415202d", "type": "Object", "convention_taxonomy": {"id": "4cac38d3-2f5e-4601-82dd-a6aca14c9083", "attributes": {"name": "Waste Water", "external_uri": "https://gitlab.com/api/v4/projects/44311021/jobs/artifacts/main/raw/output/collection/conventions/log--lab_test--waste_water/schema.json?job=copy_schemas"}}, "asset--plant--planting": {"id": "a47fc92a-3261-4245-b9be-30e0e6eac304", "attributes": {"name": "example div veg plant asset", "status": "active"}}, "log--lab_test--waste_water": {"id": "261c4d95-6019-45aa-b706-b6211185504e", "attributes": {"name": "example waste water log", "status": "done", "timestamp": "2025-05-25T01:22:52.360Z"}, "relationships": {"convention": {"data": [{"type": "taxonomy_term--convention", "id": "4cac38d3-2f5e-4601-82dd-a6aca14c9083"}]}, "asset": {"data": [{"type": "asset--plant", "id": "a47fc92a-3261-4245-b9be-30e0e6eac304"}]}, "category": {"data": [{"type": "taxonomy_term--log_category", "id": "149174aa-ca72-4c3f-bb0c-d478746dc91b"}]}, "quantity": {"data": [{"type": "quantity--standard", "id": "90fa388d-6e67-4b6a-a341-a352ed572e4a"}, {"type": "quantity--test", "id": "49d035d0-a4ee-412e-8635-0c4cc1c98482"}, {"type": "quantity--test", "id": "5775a6c0-8d38-476f-81fc-749f7892fa89"}]}}}, "taxonomy_term--log_category": {"id": "149174aa-ca72-4c3f-bb0c-d478746dc91b", "attributes": {"name": "observation"}}, "quantity--standard--area_percentage": {"id": "90fa388d-6e67-4b6a-a341-a352ed572e4a", "attributes": {"label": "area"}, "relationships": {"units": {"data": [{"type": "taxonomy_term--unit", "id": "9ad5289e-26c4-4bb2-af39-012f82d16d4e"}]}}}, "quantity--test--waste_water_volume": {"id": "49d035d0-a4ee-412e-8635-0c4cc1c98482", "attributes": {"label": "waste water volume"}, "relationships": {"units": {"data": [{"type": "taxonomy_term--unit", "id": "fc55739c-cbbd-4520-afea-5bcd2c57c863"}]}, "test_method": {"data": [{"type": "taxonomy_term--test_method", "id": "8432b644-85c5-4ff5-b57e-142ebe84ae2e"}]}}}, "quantity--test--oxygen_demand": {"id": "5775a6c0-8d38-476f-81fc-749f7892fa89", "attributes": {"label": "oxygen demand"}, "relationships": {"units": {"data": [{"type": "taxonomy_term--unit", "id": "9fb8b8c5-9dbe-4603-a929-6432d0ae51a4"}]}, "test_method": {"data": [{"type": "taxonomy_term--test_method", "id": "f50d876f-1a9d-4534-ab2d-bed71c4900d8"}]}}}, "taxonomy_term--unit--%": {"id": "9ad5289e-26c4-4bb2-af39-012f82d16d4e", "attributes": {"name": "%"}}, "taxonomy_term--unit--volume": {"id": "fc55739c-cbbd-4520-afea-5bcd2c57c863", "attributes": {"name": "gallons_us"}}, "taxonomy_term--unit--oxygen_demand": {"id": "9fb8b8c5-9dbe-4603-a929-6432d0ae51a4", "attributes": {"name": "mg/litre"}}, "taxonomy_term--test_method--oxygen_demand": {"id": "f50d876f-1a9d-4534-ab2d-bed71c4900d8", "attributes": {"name": "chemical"}}, "taxonomy_term--test_method--treatment_process": {"id": "8432b644-85c5-4ff5-b57e-142ebe84ae2e", "attributes": {"name": "centralised"}}}