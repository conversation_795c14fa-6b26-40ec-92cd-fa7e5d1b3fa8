const builder = require('convention_builder');
const fs = require('fs');
require('dotenv').config({ path: `${__dirname}/../../.env` });

//Main entity

////overlay
let log__lab_test__waste_water = new builder.SchemaOverlay({
    typeAndBundle: 'log--lab_test',
    name: 'waste_water',
});
//LOOK review description
log__lab_test__waste_water.setMainDescription("Use this log type if you produce waste water containing organic matter");

log__lab_test__waste_water.setConstant({
    attribute: "status",
    value: "done"
});


////overlays and constants
let quantity__test__waste_water_volume = new builder.SchemaOverlay({
    typeAndBundle: "quantity--test",
    name: "waste_water_volume",
});
quantity__test__waste_water_volume.setMainDescription("Average density of trees per acre/hectare of your assessment area.");

////overlays and constants
let quantity__test__oxygen_demand = new builder.SchemaOverlay({
    typeAndBundle: "quantity--test",
    name: "oxygen_demand",
});
quantity__test__oxygen_demand.setMainDescription("Oxygen demand for waste water. Use IPCC defaults for COD if unknown.");


//units
////overlays and constants
let taxonomy_term__unit__volume = new builder.SchemaOverlay({
    typeAndBundle: "taxonomy_term--unit",
    name: "volume",
});
taxonomy_term__unit__volume.setMainDescription("Unit characterizing the volume of involved water.");

taxonomy_term__unit__volume.setEnum({
    attribute: "name",
    valuesArray: [
        "litres",
        "gallon_imperial",
        "cubic_metres",
        "gallons_us",
        "hectare_millimetres",
        "hectare_centimetres",
        "acre_inches",
        "cubic_centimetres"
    ],
    description: "Several units are available, both imperial and metric."
});

////overlays and constants
let taxonomy_term__unit__oxygen_demand = new builder.SchemaOverlay({
    typeAndBundle: "taxonomy_term--unit",
    name: "oxygen_demand",
});
taxonomy_term__unit__oxygen_demand.setMainDescription("Unit characterizing the oxygen demand metric.");
taxonomy_term__unit__volume.setMainDescription("Unit characterizing the oxygen demand metric.");

taxonomy_term__unit__oxygen_demand.setEnum({
    attribute: "name",
    valuesArray: [
        "kg/litre",
        "mg/litre",
        "g/litre"
    ],
});

////overlays and constants
let taxonomy_term__test_method__oxygen_demand = new builder.SchemaOverlay({
    typeAndBundle: "taxonomy_term--test_method",
    name: "oxygen_demand",
});
taxonomy_term__test_method__oxygen_demand.setEnum({
    attribute: "name",
    valuesArray: [
        "biochemical",
        "chemical"
    ],
});
taxonomy_term__test_method__oxygen_demand.setMainDescription("Unique characterization of the method used to calculate oxygen demand.");

////overlays and constants
let taxonomy_term__test_method__treatment_process = new builder.SchemaOverlay({
    typeAndBundle: "taxonomy_term--test_method",
    name: "treatment_process",
});
taxonomy_term__test_method__treatment_process.setMainDescription("Threatment method identifier.");
taxonomy_term__test_method__treatment_process.setEnum({
    attribute: "name",
    valuesArray: [
        "stagnant",
        "fast_flowing",
        "centralised",
        "sludge",
        "anaerobic_reactor",
        "anaerobic_lagoon_shallow",
        "anaerobic_lagoon_deep",
        "discharge_tier2_other",
        "discharge_tier2",
        "surface_flow",
        "horizontal_subsurface_flow",
        "vertical_subsurface_flow"
    ],
});

//convention
let wasteWaterConvention = new builder.ConventionSchema({
    title: "Waste Water",
    version: "0.0.1",
    schemaName:"log--lab_test--waste_water",
    repoURL:"www.gitlabrepo.com/version/farmos_conventions",
    description:`## Purpose\n
Cool Farm Tool waste water convention.\n
## Specification\n
text\n`,
});


//add local attributes
wasteWaterConvention.addAttribute( { schemaOverlayObject:log__lab_test__waste_water, attributeName: "log--lab_test--waste_water", required: true } );
wasteWaterConvention.addAttribute( { schemaOverlayObject:quantity__test__waste_water_volume, attributeName: "quantity--test--waste_water_volume", required: true } );
wasteWaterConvention.addAttribute( { schemaOverlayObject:quantity__test__oxygen_demand, attributeName: "quantity--test--oxygen_demand", required: true } );
wasteWaterConvention.addAttribute( { schemaOverlayObject:taxonomy_term__unit__volume, attributeName: "taxonomy_term--unit--volume", required: true } );
wasteWaterConvention.addAttribute( { schemaOverlayObject:taxonomy_term__unit__oxygen_demand, attributeName: "taxonomy_term--unit--oxygen_demand", required: true } );
wasteWaterConvention.addAttribute( { schemaOverlayObject:taxonomy_term__test_method__treatment_process, attributeName: "taxonomy_term--test_method--treatment_process", required: true } );
wasteWaterConvention.addAttribute( { schemaOverlayObject:taxonomy_term__test_method__oxygen_demand, attributeName: "taxonomy_term--test_method--oxygen_demand", required: true } );

//add global attributes
wasteWaterConvention.addAttribute( { schemaOverlayObject:"taxonomy_term--log_category", attributeName: "taxonomy_term--log_category", required: false } );
wasteWaterConvention.addAttribute({ schemaOverlayObject:"quantity--standard--area_percentage", attributeName: "quantity--standard--area_percentage", required: false});
wasteWaterConvention.addAttribute({ schemaOverlayObject:"taxonomy_term--unit--%", attributeName: "taxonomy_term--unit--%", required: false});
wasteWaterConvention.addAttribute( { schemaOverlayObject:"asset--plant--planting", attributeName: "asset--plant--planting", required: false } );

//relationships
wasteWaterConvention.addRelationship( { containerEntity:"log--lab_test--waste_water" , relationName:"category" , mentionedEntity:"taxonomy_term--log_category" , required: false } );
wasteWaterConvention.addRelationship( { containerEntity:"log--lab_test--waste_water" , relationName:"quantity" , mentionedEntity:"quantity--test--waste_water_volume" , required: true } );
wasteWaterConvention.addRelationship( { containerEntity:"log--lab_test--waste_water" , relationName:"quantity" , mentionedEntity:"quantity--test--oxygen_demand" , required: true } );
wasteWaterConvention.addRelationship( { containerEntity:"log--lab_test--waste_water" , relationName:"quantity" , mentionedEntity:"quantity--standard--area_percentage" , required: false } );
wasteWaterConvention.addRelationship( { containerEntity:"quantity--standard--area_percentage" , relationName:"units" , mentionedEntity:"taxonomy_term--unit--%" , required: false } );
wasteWaterConvention.addRelationship( { containerEntity:"log--lab_test--waste_water", relationName:"asset", mentionedEntity:"asset--plant--planting", required: true } );
wasteWaterConvention.addRelationship( { containerEntity:"quantity--test--waste_water_volume" , relationName:"units" , mentionedEntity:"taxonomy_term--unit--volume" , required: true } );
wasteWaterConvention.addRelationship( { containerEntity:"quantity--test--oxygen_demand" , relationName:"units" , mentionedEntity:"taxonomy_term--unit--oxygen_demand" , required: false } );
wasteWaterConvention.addRelationship( { containerEntity:"quantity--test--waste_water_volume" , relationName:"test_method" , mentionedEntity:"taxonomy_term--test_method--treatment_process" , required: true } );
wasteWaterConvention.addRelationship( { containerEntity:"quantity--test--oxygen_demand" , relationName:"test_method" , mentionedEntity:"taxonomy_term--test_method--oxygen_demand" , required: false } );



wasteWaterConvention.addExample({ path: `${__dirname}/examples/correct`, is_valid_example: true });
wasteWaterConvention.addExample({ path: `${__dirname}/examples/incorrect`, is_valid_example: false });

let storageOperation = wasteWaterConvention.store();
