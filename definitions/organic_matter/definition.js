// Survey: https://app.surveystack.io/surveys/6346c1e467f839000121503e/edit
// Section: 32, Organic Matter Inputs
const builder = require('convention_builder');
const fs = require('fs');
require('dotenv').config({ path: `${__dirname}/../../.env` });

//Main entity
let log__input_organic_matter = new builder.SchemaOverlay({
    typeAndBundle: 'log--input',
    name: 'organic_matter',
});
log__input_organic_matter.setMainDescription("This log includes any organic matter inputs including mulch, compost, manure, etc.");

log__input_organic_matter.setConstant({
    attribute:"status",
    value:"done"
});


//organic matter material type name
let taxonomy_term__material_type__organic_matter = new builder.SchemaOverlay({
    typeAndBundle: "taxonomy_term--material_type",
    name: "organic_matter",
});
taxonomy_term__material_type__organic_matter.setMainDescription("The type of organic matter is stored here.");

let organicMatterMaterialsTaxonomy = JSON.parse(fs.readFileSync(`${__dirname}/organic_matter_materials_taxonomy.json`));

taxonomy_term__material_type__organic_matter.setEnum({
    attribute: "name",
    valuesArray: organicMatterMaterialsTaxonomy,
    description: "List of types of organic matter material."
});

//Fertilizer quantity units
let taxonomy_term__unit__organic_matter = new builder.SchemaOverlay({
    typeAndBundle: "taxonomy_term--unit",
    name: "organic_matter",
});
taxonomy_term__unit__organic_matter.setEnum({
    attribute: "name",
    valuesArray: [
        "lbs",
        "in"
    ],
});
taxonomy_term__unit__organic_matter.setMainDescription("Organic matter is quantified in pounds or inches of material added.");

//Convention
let organicMatterConvention = new builder.ConventionSchema({
    title: "Organic Matter",
    version: "0.0.1",
    schemaName:"log--input--organic_matter",
    repoURL:"www.gitlabrepo.com/version/farmos_conventions",
    description:`## Purpose\n
This log records any organic matter inputs to a field or planting such as mulch, compost, manure, etc.\n
## Specification\n
text\n`,
});

//add local attributes
organicMatterConvention.addAttribute( { schemaOverlayObject:log__input_organic_matter, attributeName: "log--input--organic_matter", required: true } );
organicMatterConvention.addAttribute( { schemaOverlayObject:taxonomy_term__material_type__organic_matter, attributeName: "taxonomy_term--material_type--organic_matter", required: false});
organicMatterConvention.addAttribute( { schemaOverlayObject: taxonomy_term__unit__organic_matter, attributeName: "taxonomy_term--unit--organic_matter", required: false});

//add global attributes
// organicMatterConvention.addAttribute( { schemaOverlayObject:"taxonomy_term--log_category", attributeName: "taxonomy_term--log_category", required: false } );
organicMatterConvention.addAttribute( { schemaOverlayObject:"taxonomy_term--log_category--amendment", attributeName: "taxonomy_term--log_category--amendment", required: true } );
organicMatterConvention.addAttribute( { schemaOverlayObject:"asset--plant--planting", attributeName: "asset--plant--planting", required: false } );
organicMatterConvention.addAttribute({ schemaOverlayObject:"quantity--standard--area_percentage", attributeName: "quantity--standard--area_percentage", required: false});
organicMatterConvention.addAttribute({ schemaOverlayObject:"taxonomy_term--unit--%", attributeName: "taxonomy_term--unit--%", required: false});
organicMatterConvention.addAttribute( { schemaOverlayObject:"quantity--standard--moisture_percentage", attributeName: "quantity--standard--moisture_percentage", required: false } );
organicMatterConvention.addAttribute( { schemaOverlayObject:"quantity--material--nitrogen_percentage", attributeName: "quantity--material--nitrogen_percentage", required: false } );
organicMatterConvention.addAttribute( { schemaOverlayObject:"quantity--material--phosphorus_percentage", attributeName: "quantity--material--phosphorus_percentage", required: false } );
organicMatterConvention.addAttribute( { schemaOverlayObject:"quantity--material--potassium_percentage", attributeName: "quantity--material--potassium_percentage", required: false } );
organicMatterConvention.addAttribute( { schemaOverlayObject:"quantity--material--rate", attributeName: "quantity--material--rate", required: false } );
organicMatterConvention.addAttribute( { schemaOverlayObject:"asset--land", attributeName: "asset--land", required: false } );

//add relationships
// organicMatterConvention.addRelationship( { containerEntity:"log--input--organic_matter" , relationName:"category" , mentionedEntity:"taxonomy_term--log_category" , required: false } );
organicMatterConvention.addRelationship( { containerEntity:"log--input--organic_matter" , relationName:"category" , mentionedEntity:"taxonomy_term--log_category--amendment" , required: true } );
organicMatterConvention.addRelationship( { containerEntity:"log--input--organic_matter", relationName:"asset", mentionedEntity:"asset--plant--planting", required: false } );
organicMatterConvention.addRelationship( { containerEntity:"log--input--organic_matter" , relationName:"quantity" , mentionedEntity:"quantity--standard--area_percentage" , required: false } );
organicMatterConvention.addRelationship( { containerEntity:"quantity--standard--area_percentage" , relationName:"units" , mentionedEntity:"taxonomy_term--unit--%" , required: false } );
organicMatterConvention.addRelationship( { containerEntity:"log--input--organic_matter" , relationName:"quantity" , mentionedEntity:"quantity--standard--moisture_percentage" , required: false } );
organicMatterConvention.addRelationship( { containerEntity:"quantity--standard--moisture_percentage" , relationName:"units" , mentionedEntity:"taxonomy_term--unit--%" , required: false } );
organicMatterConvention.addRelationship( { containerEntity:"log--input--organic_matter" , relationName:"quantity" , mentionedEntity:"quantity--material--nitrogen_percentage" , required: false } );
organicMatterConvention.addRelationship( { containerEntity:"quantity--material--nitrogen_percentage" , relationName:"units" , mentionedEntity:"taxonomy_term--unit--%" , required: false } );
organicMatterConvention.addRelationship( { containerEntity:"log--input--organic_matter" , relationName:"quantity" , mentionedEntity:"quantity--material--phosphorus_percentage" , required: false } );
organicMatterConvention.addRelationship( { containerEntity:"quantity--material--phosphorus_percentage" , relationName:"units" , mentionedEntity:"taxonomy_term--unit--%" , required: false } );
organicMatterConvention.addRelationship( { containerEntity:"log--input--organic_matter" , relationName:"quantity" , mentionedEntity:"quantity--material--potassium_percentage" , required: false } );
organicMatterConvention.addRelationship( { containerEntity:"quantity--material--potassium_percentage" , relationName:"units" , mentionedEntity:"taxonomy_term--unit--%" , required: false } );
organicMatterConvention.addRelationship( { containerEntity:"log--input--organic_matter" , relationName:"quantity" , mentionedEntity:"quantity--material--rate" , required: false } );
organicMatterConvention.addRelationship( { containerEntity:"quantity--material--rate", relationName:"material_type" , mentionedEntity:"taxonomy_term--material_type--organic_matter" , required: false } );
organicMatterConvention.addRelationship( { containerEntity:"quantity--material--rate", relationName:"units" , mentionedEntity:"taxonomy_term--unit--organic_matter" , required: false } );
organicMatterConvention.addRelationship( { containerEntity:"log--input--organic_matter" , relationName:"location" , mentionedEntity:"asset--land" , required: false } );

organicMatterConvention.addExample({ path: `${__dirname}/examples/correct`, is_valid_example: true });
organicMatterConvention.addExample({ path: `${__dirname}/examples/incorrect`, is_valid_example: false });

let storageOperation = organicMatterConvention.store();
