// Gitlab issue: https://gitlab.com/our-sci/conventions/common_farm_conventions/-/issues/16
const builder = require('convention_builder');
const fs = require('fs');
require('dotenv').config({ path: `${__dirname}/../../.env` });

//update convention description
//SCRATCH THISneeds at least one quantity & define quantity schema overlay for which the test method is an enum list of MODUS IDs, quantity label will also have to be defined
//make 6 separate quantities with const label and test method based on MODUS list, then in schema specify that we must have one of these. You could also have
//location relationship is required, to any field

let log__lab_test__modus = new builder.SchemaOverlay({
    typeAndBundle: 'log--lab_test',
    name: 'modus',
    validExamples: [],
    erroredExamples: []
});

//ADD description
log__lab_test__modus.setMainDescription("General log for recording MODUS lab tests.");

//quantity
////overlays and constants
let quantity__test__modus = new builder.SchemaOverlay({
    typeAndBundle: "quantity--test",
    name: "modus",
    validExamples: [ ]
});
quantity__test__modus.setMainDescription("At least one quantity is required for MODUS lab test logs");
quantity__test__modus.setRequiredAttribute("value");

//MODUS test method
////Overlays and constants
let taxonomy_term__test_method__modus = new builder.SchemaOverlay({
    typeAndBundle: "taxonomy_term--test_method",
    name: "modus",
});

let knownModusTests = JSON.parse( fs.readFileSync(`${__dirname}/modus_lab_tests.json`) );

taxonomy_term__test_method__modus.setEnum({
    attribute: "name",
    valuesArray: knownModusTests,
    description: "List of accepted MODUS IDs."
});

//ADD
taxonomy_term__test_method__modus.setMainDescription("An accepted MODUS test method ID must be entered here.");

//Convention
// Object
let MODUSTestConvention = new builder.ConventionSchema({
    title: "MODUS Test",
    version: "0.0.1",
    schemaName:"log--lab_test--modus_lab_test",
    repoURL:"www.gitlabrepo.com/version/farmos_conventions",
    description:`## Purpose\n
add in later.\n
## Specification\n
text\n`,
});

MODUSTestConvention.addExample({ path: `${__dirname}/examples/correct`, is_valid_example: true});
MODUSTestConvention.addExample({path: `${__dirname}/examples/incorrect`, is_valid_example: false});

////add local attributes
MODUSTestConvention.addAttribute( { schemaOverlayObject:log__lab_test__modus, attributeName: "log--lab_test--modus", required: true } );
MODUSTestConvention.addAttribute( { schemaOverlayObject: quantity__test__modus, attributeName: "quantity--test--modus", required: true});
MODUSTestConvention.addAttribute( { schemaOverlayObject:taxonomy_term__test_method__modus, attributeName: "taxonomy_term--test_method--modus", required: true } );
// add trivial attributes (bare entities without any modifications)
MODUSTestConvention.addAttribute( { schemaOverlayObject:'taxonomy_term--plant_type', attributeName: "taxonomy_term--plant_type", required: true } );
MODUSTestConvention.addAttribute( { schemaOverlayObject:'taxonomy_term--season', attributeName: "taxonomy_term--season", required: true } );

//add global attributes
MODUSTestConvention.addAttribute( { schemaOverlayObject:"asset--plant--planting", attributeName: "asset--plant--planting", required: true } );
MODUSTestConvention.addAttribute( { schemaOverlayObject: "quantity--standard--area_percentage", attributeName: "quantity--standard--area_percentage", required: false});
MODUSTestConvention.addAttribute( { schemaOverlayObject: "taxonomy_term--unit--%", attributeName: "taxonomy_term--unit--%", required: false});

////add relationships
MODUSTestConvention.addRelationship( { containerEntity:"log--lab_test--modus", relationName:"asset", mentionedEntity:"asset--plant--planting", required: true } );
MODUSTestConvention.addRelationship( { containerEntity:"log--lab_test--modus" , relationName:"quantity" , mentionedEntity:"quantity--standard--area_percentage" , required: true } );
MODUSTestConvention.addRelationship( { containerEntity:"log--lab_test--modus" , relationName:"quantity" , mentionedEntity:"quantity--test--modus" , required: true } );
MODUSTestConvention.addRelationship( { containerEntity:"quantity--standard--area_percentage" , relationName:"units" , mentionedEntity:"taxonomy_term--unit--%" , required: false } );
MODUSTestConvention.addRelationship( { containerEntity:"quantity--test--modus" , relationName:"test_method" , mentionedEntity:"taxonomy_term--test_method--modus" , required: true } );
MODUSTestConvention.addRelationship( { containerEntity:"asset--plant--planting" , relationName:"plant_type" , mentionedEntity:"taxonomy_term--plant_type" , required: true } );
MODUSTestConvention.addRelationship( { containerEntity:"asset--plant--planting" , relationName:"season" , mentionedEntity:"taxonomy_term--season" , required: true } );


let test = MODUSTestConvention.testExamples();
let storageOperation = MODUSTestConvention.store();

// console.log( test.failedExamples[0].errors );


