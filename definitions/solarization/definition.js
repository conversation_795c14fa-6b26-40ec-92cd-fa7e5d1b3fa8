// Survey: https://app.surveystack.io/surveys/6346c1e467f839000121503e/edit
// Section: 30, solarization
const builder = require('convention_builder');
const fs = require('fs');
require('dotenv').config({ path: `${__dirname}/../../.env` });

//Main entity
let log__activity__solarization = new builder.SchemaOverlay({
    typeAndBundle: 'log--activity',
    name: 'solarization',
});
log__activity__solarization.setMainDescription("This log is used to record solairzation or tarping events.");

log__activity__solarization.setConstant({
    attribute:"status",
    value:"done"
});

log__activity__solarization.setPattern({
    attribute:"name",
    pattern:"(?=.*(start|end))(?=.*(solarization|tarping))",
    description:"There are several synonymous words for solarization and the log needs to represent the start or end date of the activity."
});

//Convention
let solarizationConvention = new builder.ConventionSchema({
    title: "Solarization",
    version: "0.0.3",
    schemaName:"log--activity--solarization",
    repoURL:"www.gitlabrepo.com/version/farmos_conventions",
    description:`## Purpose\n
Use this log to record a solarization event to a field or planting, typically used for weed control.\n
## Specification\n
text\n`,
});

//add local attributes
solarizationConvention.addAttribute( { schemaOverlayObject:log__activity__solarization, attributeName: "log--activity--solarization", required: true } );

//add global attributes
solarizationConvention.addAttribute( { schemaOverlayObject:"taxonomy_term--log_category", attributeName: "taxonomy_term--log_category", required: false } );
solarizationConvention.addAttribute( { schemaOverlayObject:"taxonomy_term--log_category--weed_control", attributeName: "taxonomy_term--log_category--weed_control", required: true } );
solarizationConvention.addAttribute( { schemaOverlayObject:"quantity--standard--area_percentage", attributeName: "quantity--standard--area_percentage", required: false});
solarizationConvention.addAttribute( { schemaOverlayObject:"taxonomy_term--unit--%", attributeName: "taxonomy_term--unit--%", required: false});
solarizationConvention.addAttribute( { schemaOverlayObject:"asset--plant--planting", attributeName: "asset--plant--planting", required: false } );
solarizationConvention.addAttribute( { schemaOverlayObject:"asset--land", attributeName: "asset--land", required: false } );

//relationships
solarizationConvention.addRelationship( { containerEntity:"log--activity--solarization" , relationName:"category" , mentionedEntity:"taxonomy_term--log_category" , required: false } );
solarizationConvention.addRelationship( { containerEntity:"log--activity--solarization" , relationName:"category" , mentionedEntity:"taxonomy_term--log_category--weed_control" , required: true } );
solarizationConvention.addRelationship( { containerEntity:"log--activity--solarization" , relationName:"quantity" , mentionedEntity:"quantity--standard--area_percentage" , required: false } );
solarizationConvention.addRelationship( { containerEntity:"quantity--standard--area_percentage" , relationName:"units" , mentionedEntity:"taxonomy_term--unit--%" , required: false } );
solarizationConvention.addRelationship( { containerEntity:"log--activity--solarization", relationName:"asset", mentionedEntity:"asset--plant--planting", required: false } );
solarizationConvention.addRelationship( { containerEntity:"log--activity--solarization", relationName:"location", mentionedEntity:"asset--land", required: false } );


solarizationConvention.addExample({ path: `${__dirname}/examples/correct`, is_valid_example: true });
solarizationConvention.addExample({ path: `${__dirname}/examples/incorrect`, is_valid_example: false });

let storageOperation = solarizationConvention.store();
