// Survey: https://app.surveystack.io/surveys/6346c1e467f839000121503e/edit
// Section: 25, mowing
const builder = require('convention_builder');
const fs = require('fs');
require('dotenv').config({ path: `${__dirname}/../../.env` });

//Main entity
////Overlays and constants
let log__activity__mowing = new builder.SchemaOverlay({
    typeAndBundle: 'log--activity',
    name: 'mowing',
});
log__activity__mowing.setMainDescription("This log includes any type of mowing, including mowing grass (for example, between rows in an orchard), mowing hay (in preparation for bailing), mowing for termination, etc.");

log__activity__mowing.setConstant({
    attribute:"status",
    value:"done"
});

log__activity__mowing.setPattern({
    attribute:"name",
    pattern:"(mowing)|(raking)|(tedding)",
    description:"There are several synonymous words for mowing, use pattern to accept any of them."
});

//convention
let mowingConvention = new builder.ConventionSchema({
    title: "Mowing",
    version: "0.0.3",
    schemaName:"log--activity--mowing",
    repoURL:"www.gitlabrepo.com/version/farmos_conventions",
    description:`## Purpose\n
Mowing logs can be created for termination or weed control.\n
## Specification\n
text\n`,
});

//add local attributes
mowingConvention.addAttribute( { schemaOverlayObject:log__activity__mowing, attributeName: "log--activity--mowing", required: true } );

//add global attributes
mowingConvention.addAttribute( { schemaOverlayObject:"taxonomy_term--log_category", attributeName: "taxonomy_term--log_category", required: false } );
mowingConvention.addAttribute({ schemaOverlayObject:"quantity--standard--area_percentage", attributeName: "quantity--standard--area_percentage", required: false});
mowingConvention.addAttribute({ schemaOverlayObject:"taxonomy_term--unit--%", attributeName: "taxonomy_term--unit--%", required: false});
mowingConvention.addAttribute( { schemaOverlayObject:"asset--plant--planting", attributeName: "asset--plant--planting", required: false } );
mowingConvention.addAttribute( { schemaOverlayObject:"asset--land", attributeName: "asset--land", required: false } );

//add relationships
mowingConvention.addRelationship( { containerEntity:"log--activity--mowing" , relationName:"category" , mentionedEntity:"taxonomy_term--log_category" , required: false } );
mowingConvention.addRelationship( { containerEntity:"log--activity--mowing" , relationName:"quantity" , mentionedEntity:"quantity--standard--area_percentage" , required: false } );
mowingConvention.addRelationship( { containerEntity:"quantity--standard--area_percentage" , relationName:"units" , mentionedEntity:"taxonomy_term--unit--%" , required: false } );
mowingConvention.addRelationship( { containerEntity:"log--activity--mowing", relationName:"asset", mentionedEntity:"asset--plant--planting", required: false } );
mowingConvention.addRelationship( { containerEntity:"log--activity--mowing", relationName:"location", mentionedEntity:"asset--land", required: false } );


mowingConvention.addExample({ path: `${__dirname}/examples/correct`, is_valid_example: true });
mowingConvention.addExample({ path: `${__dirname}/examples/incorrect`, is_valid_example: false });

let storageOperation = mowingConvention.store();
