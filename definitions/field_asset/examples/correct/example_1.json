{"id": "da16a9e8-ae76-49e2-92e2-b9532d836ad1", "convention_taxonomy": {"id": "f5ed9f3c-96ac-4135-9a25-bf7b120886a7", "attributes": {"name": "Field", "external_uri": "https://gitlab.com/api/v4/projects/44311021/jobs/artifacts/main/raw/output/collection/conventions/asset--land--field/schema.json?job=copy_schemas"}}, "asset--land--create_field": {"id": "0c552e8d-9aba-4be4-ac76-ac2f64a1affd", "attributes": {"name": "example create field", "status": "active", "is_location": true, "is_fixed": true, "land_type": "field"}, "relationships": {"convention": {"data": [{"type": "taxonomy_term--convention", "id": "f5ed9f3c-96ac-4135-9a25-bf7b120886a7"}]}}}}