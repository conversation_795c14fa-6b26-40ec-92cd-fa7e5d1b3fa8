// Survey: https://app.surveystack.io/surveys/6346c1e467f839000121503e/edit
// Section: 5, create field

const builder = require('convention_builder');
const { randomUUID } = require('crypto');
const fs = require('fs');
require('dotenv').config({ path: `${__dirname}/../../.env` });
//const { patterns } = require(`../experiments/wktRegexpPatterns.js`);

// ids for the examples
let asset__land__create_field_uuid = randomUUID();
let create_field_convention_uuid = randomUUID();
let taxonomy_term__convention_uuid = randomUUID();

// // Main entity: Land Asset
let asset__land__create_field_example = {
    id:asset__land__create_field_uuid,
    attributes: {
        name: "example create field",
        status:"active",
        is_location: true,
        is_fixed: true,
        land_type:"field"
    }
};

let asset__land__create_field_error = {
    id:asset__land__create_field_uuid,
    attributes: {
        name: "example create field",
        status:1,
        is_location:0,
        is_fixed: 1,
        land_type: "area"
    }
};

let asset__land__create_field = new builder.SchemaOverlay({
    typeAndBundle: 'asset--land',
    name: 'create_field',
    validExamples: [asset__land__create_field_example],
    erroredExamples: [asset__land__create_field_error]
});

asset__land__create_field.setMainDescription("Creating a field in surveystack creates a land asset in farmOS.");
asset__land__create_field.setConstant({
    attribute:"status",
    value:"active"
});
asset__land__create_field.setConstant({
    attribute:"is_location",
    value: true
});
asset__land__create_field.setConstant({
    attribute:"is_fixed",
    value:true
});
asset__land__create_field.setConstant({
    attribute:"land_type",
    value:"field"
});

// // Geometry for base convention is defined (not required)
// Valid patterns for field are a single polygon, a single multipolygon or geometry collection containing one polygon or one multipolygon
// Invalid patterns are point, multipoint, or geometry collection of point/multipoint, multiple polygons, multiple multipolygon or a geometry collection containing multiple poloygon/multipolygon


//let field_pattern = new RegExp(`${patterns.geometrycollection_polygon_multipolygon}|${patterns.polygon}|${patterns.multipolygon}`);

// doesn't work for recognizing lines / line segments as invalid 
let field_pattern = `^POLYGON\\s*\\(\\(((\\s*-?\\d+(\\.\\d+)?\\s+-?\\d+(\\.\\d+)?\\s*,?)+)\\)\\)` + `|` + `^MULTIPOLYGON\\s*\\(\\(\\(\\s*((\\s*-?\\d+(\\.\\d+)?\\s+-?\\d+(\\.\\d+)?\\s*,?)+)\\s*\\)\\)\\)` + `|` + `^GEOMETRYCOLLECTION\\s*\\((MULTIPOLYGON\\s*\\(\\(\\(\\s*((\\s*-?\\d+(\\.\\d+)?\\s+-?\\d+(\\.\\d+)?\\s*,?)+)\\s*\\)\\)\\)|POLYGON\\s*\\(\\(((\\s*-?\\d+(\\.\\d+)?\\s+-?\\d+(\\.\\d+)?\\s*,?)+)\\)\\))\\)`;

//: LOOK: Should this be both for geometry and intrinsic geometry?
asset__land__create_field.setPattern({
    attribute:"geometry",
    pattern: field_pattern,
    description: "Field asset geometry must be valid and passing WKT"
});


// // // Convention
// object
let createFieldConvention = new builder.ConventionSchema({
    title: "Field",
    version: "0.0.2",
    schemaName: "asset--land--field",
    repoURL: "www.gitlabrepo.com/version/farmos_conventions",
    description: `## Purpose\n
text\n
## Specification\n
text\n`,
    validExamples: [],
    erroredExamples: []
});


createFieldConvention.addExample({ path: `${__dirname}/examples/correct`, is_valid_example: true});
createFieldConvention.addExample({path: `${__dirname}/examples/incorrect`, is_valid_example: false});

createFieldConvention.addAttribute( { schemaOverlayObject:asset__land__create_field, attributeName: "asset--land--create_field", required: true } );

let test = createFieldConvention.testExamples();
let storageOperation = createFieldConvention.store();
