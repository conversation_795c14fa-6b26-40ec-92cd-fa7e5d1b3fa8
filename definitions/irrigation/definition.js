// Survey: https://app.surveystack.io/surveys/6346c1e467f839000121503e/edit
// Section: 40, irrigation
const builder = require('convention_builder');
const fs = require('fs');
require('dotenv').config({ path: `${__dirname}/../../.env` });

//Main entity
////overlays and constants
let log__input__irrigation = new builder.SchemaOverlay({
    typeAndBundle: 'log--input',
    name: 'irrigation',
});
//LOOK add description
log__input__irrigation.setMainDescription(
    "Irrigation logs are used to record watering events. It records % of field covered, water source, quantity, and general effectivness."
);



//quantities - total water
////Overlays and constants
let quantity__material__total_water = new builder.SchemaOverlay({
    typeAndBundle: "quantity--material",
    name: "total_water",
});

//LOOK set description
quantity__material__total_water.setMainDescription("This represents the total quantity of water added.");
//LOOK what should the measure be here?
quantity__material__total_water.setConstant({
    attribute: "measure",
    value: "value"
});
//totalWater.setConstant({
//    attribute: "label",
//    value:"total_water"
//});

//taxonomy term - units
let taxonomy_term__unit__total_water = new builder.SchemaOverlay({
    typeAndBundle: "taxonomy_term--unit",
    name: "total_water"
});
taxonomy_term__unit__total_water.setMainDescription("Units for total water added are recorded in inches.");
taxonomy_term__unit__total_water.setEnum({
    attribute: "name",
    valuesArray: [
        "inches",
    ],
});

//quantity - effectiveness
////overlays and examples
let quantity__standard__effectiveness = new builder.SchemaOverlay({
    typeAndBundle: "quantity--standard",
    name: "effectiveness",
});
quantity__standard__effectiveness.setConstant({
    attribute: "label",
    value: "effectiveness"
});
quantity__standard__effectiveness.setMainDescription("Estimate if the field was under watered, mostly watered, or well watered.");

//Taxonomy term -- unit
////overlays and constants
let taxonomy_term__unit__effectiveness_rating = new builder.SchemaOverlay({
    typeAndBundle: "taxonomy_term--unit",
    name: "effectiveness_rating",
});
taxonomy_term__unit__effectiveness_rating.setEnum({
    attribute: "name",
    valuesArray: [
        "Under watered (0)",
        "Mostly watered (1)",
        "Well watered (2)",
    ],
    description: "This is an effectiveness scale with three levels from under watered to well watered."
});
taxonomy_term__unit__effectiveness_rating.setMainDescription("This question does not use measured units, this uses a subjective scale where the user will have to use the knowledge of their operation to judge how well the field was watered.");


//pumping depth
////overlays and examples
let quantity__standard__pumping_depth = new builder.SchemaOverlay({
    typeAndBundle: "quantity--standard",
    name: "pumping_depth",
});
quantity__standard__pumping_depth.setMainDescription("Depth of the water table (optional).");

//pumping depth unit
////overlays and constants
let taxonomy_term__unit__length = new builder.SchemaOverlay({
    typeAndBundle: "taxonomy_term--unit",
    name: "length",
});
taxonomy_term__unit__length.setEnum({
    attribute: "name",
    valuesArray: [
        "meters",
        "centimeters",
        "inches",
        "feet",
        "millimeters"
    ]
});
taxonomy_term__unit__length.setMainDescription("Unit used to characterize depth.");


let quantity__standard__horizontal_distance = new builder.SchemaOverlay({
    typeAndBundle: "quantity--standard",
    name: "horizontal_distance",
});
quantity__standard__horizontal_distance.setMainDescription("Optional quantity for cft.");


//convention
let irrigationConvention = new builder.ConventionSchema({
    title: "Irrigation",
    version: "0.0.2",
    schemaName:"log--input--irrigation",
    repoURL:"www.gitlabrepo.com/version/farmos_conventions",
    //LOOK add description
    description:`## Purpose\n
Irrigation logs are used to record watering events.\n
## Specification\n
text\n`,
});

//add local attributes
irrigationConvention.addAttribute( { schemaOverlayObject:log__input__irrigation, attributeName: "log--input--irrigation", required: true } );
irrigationConvention.addAttribute( { schemaOverlayObject:quantity__material__total_water, attributeName: "quantity--material--total_water", required: false} );
irrigationConvention.addAttribute( { schemaOverlayObject:taxonomy_term__unit__total_water, attributeName: "taxonomy_term--unit--total_water", required: false} );
irrigationConvention.addAttribute( { schemaOverlayObject:quantity__standard__effectiveness, attributeName: "quantity--standard--effectiveness", required: false} );
irrigationConvention.addAttribute( { schemaOverlayObject:taxonomy_term__unit__effectiveness_rating, attributeName: "taxonomy_term--unit--effectiveness_rating", required: false} );
irrigationConvention.addAttribute( { schemaOverlayObject:quantity__standard__pumping_depth, attributeName: "quantity--standard--pumping_depth", required: false} );
irrigationConvention.addAttribute( { schemaOverlayObject:taxonomy_term__unit__length, attributeName: "taxonomy_term--unit--length", required: false} );
irrigationConvention.addAttribute( { schemaOverlayObject:quantity__standard__horizontal_distance, attributeName: "quantity--standard--horizontal_distance", required: false} );
irrigationConvention.addAttribute( { schemaOverlayObject:taxonomy_term__unit__length, attributeName: "taxonomy_term--unit--length", required: false} );

//add global attributes
irrigationConvention.addAttribute( { schemaOverlayObject:"taxonomy_term--log_category", attributeName: "taxonomy_term--log_category", required: false } );
irrigationConvention.addAttribute( { schemaOverlayObject:"asset--plant--planting", attributeName: "asset--plant--planting", required: false } );
irrigationConvention.addAttribute( { schemaOverlayObject:"quantity--standard--area_percentage", attributeName: "quantity--standard--area_percentage", required: false} );
irrigationConvention.addAttribute( { schemaOverlayObject:"taxonomy_term--unit--%", attributeName: "taxonomy_term--unit--%", required: false} );
irrigationConvention.addAttribute( { schemaOverlayObject:"taxonomy_term--log_category--irrigation", attributeName: "taxonomy_term--log_category--irrigation", required: false} );
irrigationConvention.addAttribute( { schemaOverlayObject:"asset--land", attributeName: "asset--land", required: false} );

//add relationships
irrigationConvention.addRelationship( { containerEntity:"log--input--irrigation" , relationName:"category" , mentionedEntity:"taxonomy_term--log_category" , required: false } );
irrigationConvention.addRelationship( { containerEntity:"log--input--irrigation", relationName:"asset", mentionedEntity:"asset--plant--planting", required: false } );
irrigationConvention.addRelationship( { containerEntity:"log--input--irrigation", relationName:"category", mentionedEntity:"taxonomy_term--log_category--irrigation", required: true } );
irrigationConvention.addRelationship( { containerEntity:"log--input--irrigation" , relationName:"quantity" , mentionedEntity:"quantity--standard--area_percentage" , required: false } );
irrigationConvention.addRelationship( { containerEntity:"quantity--standard--area_percentage" , relationName:"units" , mentionedEntity:"taxonomy_term--unit--%" , required: false } );
irrigationConvention.addRelationship( { containerEntity:"log--input--irrigation" , relationName:"quantity" , mentionedEntity:"quantity--material--total_water" , required: false } );
irrigationConvention.addRelationship( { containerEntity:"quantity--material--total_water" , relationName:"units" , mentionedEntity:"taxonomy_term--unit--total_water" , required: false } );
irrigationConvention.addRelationship( { containerEntity:"log--input--irrigation" , relationName:"quantity" , mentionedEntity:"quantity--standard--effectiveness" , required: false } );
irrigationConvention.addRelationship( { containerEntity:"quantity--standard--effectiveness" , relationName:"units" , mentionedEntity:"taxonomy_term--unit--effectiveness_rating" , required: false } );
irrigationConvention.addRelationship( { containerEntity:"log--input--irrigation" , relationName:"quantity" , mentionedEntity:"quantity--standard--pumping_depth" , required: false } );
irrigationConvention.addRelationship( { containerEntity:"quantity--standard--pumping_depth" , relationName:"units" , mentionedEntity:"taxonomy_term--unit--length" , required: false } );
irrigationConvention.addRelationship( { containerEntity:"log--input--irrigation" , relationName:"quantity" , mentionedEntity:"quantity--standard--horizontal_distance" , required: false } );
irrigationConvention.addRelationship( { containerEntity:"quantity--standard--horizontal_distance" , relationName:"units" , mentionedEntity:"taxonomy_term--unit--length" , required: false } );
irrigationConvention.addRelationship( { containerEntity:"log--input--irrigation" , relationName:"location" , mentionedEntity:"asset--land" , required: false } );

// Convention example



irrigationConvention.addExample({ path: `${__dirname}/examples/correct`, is_valid_example: true });
irrigationConvention.addExample({ path: `${__dirname}/examples/incorrect`, is_valid_example: false });

let storageOperation = irrigationConvention.store();
