const builder = require('convention_builder');
const fs = require('fs');
require('dotenv').config({ path: `${__dirname}/../../.env` });

//make a bunch of quantities that are not required

let log__lab_test__soil = new builder.SchemaOverlay({
    typeAndBundle: 'log--lab_test',
    name: 'soil',
});
log__lab_test__soil.setMainDescription("General log for soil testing results.");

log__lab_test__soil.setConstant({
    attribute: "status",
    value: "done",
    description: "The status should always be set to done to inherit the area."
});
log__lab_test__soil.setConstant({
    attribute: "lab_test_type",
    value: "soil"
});

//quantities
let quantity__test__soil_texture = new builder.SchemaOverlay({
    typeAndBundle: 'quantity--test',
    name: 'soil_texture',
});
quantity__test__soil_texture.setMainDescription("");

let quantity__test__soil_type = new builder.SchemaOverlay({
    typeAndBundle: 'quantity--test',
    name: 'soil_type',
});
quantity__test__soil_type.setMainDescription("");

let quantity__test__soil_organic_matter = new builder.SchemaOverlay({
    typeAndBundle: 'quantity--test',
    name: 'soil_organic_matter',
});
quantity__test__soil_organic_matter.setMainDescription("");

let quantity__test__soil_drainage = new builder.SchemaOverlay({
    typeAndBundle: 'quantity--test',
    name: 'soil_drainage',
});
quantity__test__soil_drainage.setMainDescription("");

let quantity__test__soil_ph = new builder.SchemaOverlay({
    typeAndBundle: 'quantity--test',
    name: 'soil_ph',
});
quantity__test__soil_ph.setMainDescription("");

//Convention
// Object
let soilTestConvention = new builder.ConventionSchema({
    title: "Soil Test",
    version: "0.0.1",
    schemaName:"log--lab_test--soil",
    repoURL:"www.gitlabrepo.com/version/farmos_conventions",
    description:`## Purpose\n
General log for soil testing.\n
## Specification\n
text\n`,
});

////add local attributes
soilTestConvention.addAttribute( { schemaOverlayObject:log__lab_test__soil, attributeName: "log--lab_test--soil", required: true});
soilTestConvention.addAttribute( { schemaOverlayObject:quantity__test__soil_texture, attributeName: "quantity--test--soil_texture", required: false});
soilTestConvention.addAttribute( { schemaOverlayObject:quantity__test__soil_type, attributeName: "quantity--test--soil_type", required: false});
soilTestConvention.addAttribute( { schemaOverlayObject:quantity__test__soil_drainage, attributeName: "quantity--test--soil_drainage", required: false});
soilTestConvention.addAttribute( { schemaOverlayObject:quantity__test__soil_organic_matter, attributeName: "quantity--test--soil_organic_matter", required: false});
soilTestConvention.addAttribute( { schemaOverlayObject:quantity__test__soil_ph, attributeName: "quantity--test--soil_ph", required: false});

//add global attributes
soilTestConvention.addAttribute( { schemaOverlayObject:"taxonomy_term--log_category", attributeName: "taxonomy_term--log_category", required: false});
soilTestConvention.addAttribute( { schemaOverlayObject:"asset--plant--planting", attributeName: "asset--plant--planting", required: true } );
soilTestConvention.addAttribute( { schemaOverlayObject: "quantity--standard--area_percentage", attributeName: "quantity--standard--area_percentage", required: false});
soilTestConvention.addAttribute( { schemaOverlayObject: "taxonomy_term--unit--%", attributeName: "taxonomy_term--unit--%", required: false});

//add relationships
soilTestConvention.addRelationship( { containerEntity:"log--lab_test--soil" , relationName:"category" , mentionedEntity:"taxonomy_term--log_category" , required: true } );
soilTestConvention.addRelationship( { containerEntity:"log--lab_test--soil", relationName:"asset", mentionedEntity:"asset--plant--planting", required: true } );
soilTestConvention.addRelationship( { containerEntity:"log--lab_test--soil" , relationName:"quantity" , mentionedEntity:"quantity--standard--area_percentage" , required: false } );
soilTestConvention.addRelationship( { containerEntity:"quantity--standard--area_percentage" , relationName:"units" , mentionedEntity:"taxonomy_term--unit--%" , required: false } );
soilTestConvention.addRelationship( { containerEntity:"log--lab_test--soil" , relationName:"quantity" , mentionedEntity:"quantity--test--soil_texture" , required: false } );
soilTestConvention.addRelationship( { containerEntity:"log--lab_test--soil" , relationName:"quantity" , mentionedEntity:"quantity--test--soil_type" , required: false } );
soilTestConvention.addRelationship( { containerEntity:"log--lab_test--soil" , relationName:"quantity" , mentionedEntity:"quantity--test--soil_drainage" , required: false } );
soilTestConvention.addRelationship( { containerEntity:"log--lab_test--soil" , relationName:"quantity" , mentionedEntity:"quantity--test--soil_organic_matter" , required: false } );
soilTestConvention.addRelationship( { containerEntity:"log--lab_test--soil" , relationName:"quantity" , mentionedEntity:"quantity--test--soil_ph" , required: false } );



soilTestConvention.addExample({ path: `${__dirname}/examples/correct`, is_valid_example: true });
soilTestConvention.addExample({ path: `${__dirname}/examples/incorrect`, is_valid_example: false });

let storageOperation = soilTestConvention.store();
