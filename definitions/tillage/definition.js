// Survey: https://app.surveystack.io/surveys/6346c1e467f839000121503e/edit
// Section: 19, soil disturbance
const builder = require('convention_builder');
const fs = require('fs');
require('dotenv').config({ path: `${__dirname}/../../.env` });

//main entity
let log__activity__tillage = new builder.SchemaOverlay({
    typeAndBundle: "log--activity",
    name: "tillage",
});

log__activity__tillage.setMainDescription("Must be related to a taxonomy_term--log_category named *tillage* and be related to an *asset--land*. Should have quantity--standard--stir, quantity--standard--residue, quantity--standard--tillage_depth. May have other taxonomy_term--log_category. Originally hosted in [link](https://gitlab.com/OpenTEAMAg/ag-data-wallet/openteam-convention/-/blob/main/descriptions/log--activity--tillage.md)");
// required attribute is status
log__activity__tillage.setConstant({
    attribute:"status",
    value:"done"
});


//stir quantity overlay
let quantity__standard__stir = new builder.SchemaOverlay({
    typeAndBundle: "quantity--standard",
    name: "stir"
});
quantity__standard__stir.setMainDescription("Must be labelled as *stir* and it's measure type is *ratio*. See documentation ADD LINK to get the standard specification for this ratio.");
quantity__standard__stir.setConstant( {
    attribute:"measure",
    value:"ratio",
} );

//depth quantity overlay
let quantity__standard__tillage_depth = new builder.SchemaOverlay({
    typeAndBundle: "quantity--standard",
    name: "tillage_depth"
});
quantity__standard__tillage_depth.setMainDescription("Must be labelled as *depth* and it's measure type is *length*.");
quantity__standard__tillage_depth.setConstant( {
    attribute:"label",
    value:"depth",
} );
quantity__standard__tillage_depth.setConstant( {
    attribute:"measure",
    value:"length",
} );

//LOOK do we need a depth unit if it's always inches?
let taxonomy_term__unit__depth = new builder.SchemaOverlay({
    typeAndBundle: "taxonomy_term--unit",
    name: "depth"
});
taxonomy_term__unit__depth.setMainDescription("Tillage depth is in inches");

taxonomy_term__unit__depth.setConstant( {
    attribute: "name",
    value: "in"
});

//speed overlay
let quantity__standard__tillage_speed = new builder.SchemaOverlay({
    typeAndBundle: "quantity--standard",
    name: "tillage_speed"
});
quantity__standard__tillage_speed.setMainDescription("Must be labelled as *speed* and it's measure type is *speed*.");
quantity__standard__tillage_speed.setConstant( {
    attribute:"label",
    value:"speed",
} );
quantity__standard__tillage_speed.setConstant( {
    attribute:"measure",
    value:"speed",
} );

//LOOK do we need a speed unit if it's always mph?
let taxonomy_term__unit__speed = new builder.SchemaOverlay({
    typeAndBundle: "taxonomy_term--unit",
    name: "speed"
});
taxonomy_term__unit__speed.setMainDescription("The speed for mechanized tillage should always be entered in mph");

taxonomy_term__unit__speed.setConstant( {
    attribute: "name",
    value: "mph"
});

let taxonomy_term__log_category__tillage = new builder.SchemaOverlay({
    typeAndBundle: "taxonomy_term--log_category",
    name:"tillage"
});

taxonomy_term__log_category__tillage.setMainDescription("The only identifier for a tillage log activity *MUST* be its tillage log category taxonomy term.");
taxonomy_term__log_category__tillage.setConstant( {
    attribute:"name",
    value:"tillage"
} );

//tillage convention
let tillageConvention = new builder.ConventionSchema({
    title: "Tillage",
    version:"0.0.1",
    schemaName:"log--activity--tillage",
    repoURL:"www.gitlabrepo.com/version/farmos_conventions",
    description:`## Purpose\n
A tillage log encompasses all information we can gather about a tillage operation.\n
## Specification\n
text\n`,
});

//add local attributes
tillageConvention.addAttribute( { schemaOverlayObject:log__activity__tillage, attributeName: "log--activity--tillage", required: true } );
tillageConvention.addAttribute( { schemaOverlayObject:quantity__standard__stir, attributeName: "quantity--standard--stir", required: false } );
tillageConvention.addAttribute( { schemaOverlayObject:quantity__standard__tillage_depth, attributeName: "quantity--standard--tillage_depth", required: false } );
tillageConvention.addAttribute({ schemaOverlayObject:taxonomy_term__log_category__tillage, attributeName: "taxonomy_term--log_category--tillage", required: false});
tillageConvention.addAttribute({ schemaOverlayObject:taxonomy_term__unit__depth, attributeName: "taxonomy_term--unit--depth", required: false});
tillageConvention.addAttribute({ schemaOverlayObject:quantity__standard__tillage_speed, attributeName: "quantity--standard--tillage_speed", required: false});
tillageConvention.addAttribute({ schemaOverlayObject:taxonomy_term__unit__speed, attributeName: "taxonomy_term--unit--speed", required: false});

//add global attributes
tillageConvention.addAttribute( { schemaOverlayObject:"taxonomy_term--log_category", attributeName: "taxonomy_term--log_category", required: false } );
tillageConvention.addAttribute( { schemaOverlayObject:"quantity--standard--area_percentage", attributeName: "quantity--standard--area_percentage", required: false});
tillageConvention.addAttribute( { schemaOverlayObject:"taxonomy_term--unit--%", attributeName: "taxonomy_term--unit--%", required: false});
tillageConvention.addAttribute( { schemaOverlayObject:"asset--plant--planting", attributeName: "asset--plant--planting", required: false } );
tillageConvention.addAttribute( { schemaOverlayObject:"asset--land", attributeName: "asset--land", required: false } );

//add relationships
tillageConvention.addRelationship( { containerEntity:"log--activity--tillage" , relationName:"category" , mentionedEntity:"taxonomy_term--log_category" , required: false } );
tillageConvention.addRelationship( { containerEntity:"log--activity--tillage" , relationName:"quantity" , mentionedEntity:"quantity--standard--stir" , required: false } );
tillageConvention.addRelationship( { containerEntity:"log--activity--tillage" , relationName:"quantity" , mentionedEntity:"quantity--standard--tillage_depth" , required: false } );
tillageConvention.addRelationship( { containerEntity:"quantity--standard--tillage_depth" , relationName:"units" , mentionedEntity:"taxonomy_term--unit--depth" , required: false } );
tillageConvention.addRelationship( { containerEntity:"log--activity--tillage" , relationName:"quantity" , mentionedEntity:"quantity--standard--area_percentage" , required: false } );
tillageConvention.addRelationship( { containerEntity:"quantity--standard--area_percentage" , relationName:"units" , mentionedEntity:"taxonomy_term--unit--%" , required: false } );
tillageConvention.addRelationship( { containerEntity:"log--activity--tillage" , relationName:"quantity" , mentionedEntity:"quantity--standard--tillage_speed" , required: false } );
tillageConvention.addRelationship( { containerEntity:"quantity--standard--tillage_speed" , relationName:"units" , mentionedEntity:"taxonomy_term--unit--speed" , required: false } );
tillageConvention.addRelationship( { containerEntity:"log--activity--tillage", relationName:"asset", mentionedEntity:"asset--plant--planting", required: false } );
tillageConvention.addRelationship( { containerEntity:"log--activity--tillage", relationName:"category", mentionedEntity:"taxonomy_term--log_category--tillage", required: true } );
tillageConvention.addRelationship( { containerEntity:"log--activity--tillage", relationName:"location", mentionedEntity:"asset--land", required: false } );


tillageConvention.addExample({ path: `${__dirname}/examples/correct`, is_valid_example: true });
tillageConvention.addExample({ path: `${__dirname}/examples/incorrect`, is_valid_example: false });

let storageOperation = tillageConvention.store();
