const builder = require('convention_builder');
const fs = require('fs');
require('dotenv').config({ path: `${__dirname}/../../.env` });

//Main entity
////overlays and constants
let log__input__seed_treatment = new builder.SchemaOverlay({
    typeAndBundle: 'log--input',
    name: 'seed_treatment',
});
log__input__seed_treatment.setMainDescription("This log records seed treatment inputs. This could include pesticides.");

log__input__seed_treatment.setConstant({
    attribute:"status",
    value:"done",
    description: "The status should always be set to done to inherit the area."
});


//Convention
// Object
let seedTreatmentConvention = new builder.ConventionSchema({
    title: "Seed Treatment",
    version: "0.0.2",
    schemaName:"log--input--seed_treatment",
    repoURL:"www.gitlabrepo.com/version/farmos_conventions",
    description:`## Purpose\n
Common farm seed treatment convention.\n
## Specification\n
text\n`,
});

////add local attributes
seedTreatmentConvention.addAttribute( { schemaOverlayObject: log__input__seed_treatment, attributeName: "log--input--seed_treatment", required: true } );

//add global attributes
seedTreatmentConvention.addAttribute( { schemaOverlayObject: "taxonomy_term--material_type--herbicide_or_pesticide", attributeName: "taxonomy_term--material_type--herbicide_or_pesticide", required: true});
seedTreatmentConvention.addAttribute( { schemaOverlayObject: "quantity--material--active_ingredient_percent", attributeName: "quantity--material--active_ingredient_percent", required: true});
seedTreatmentConvention.addAttribute( { schemaOverlayObject:"taxonomy_term--log_category", attributeName: "taxonomy_term--log_category", required: false } );
seedTreatmentConvention.addAttribute( { schemaOverlayObject:"asset--plant--planting", attributeName: "asset--plant--planting", required: true } );
seedTreatmentConvention.addAttribute( { schemaOverlayObject: "quantity--standard--area_percentage", attributeName: "quantity--standard--area_percentage", required: false});
seedTreatmentConvention.addAttribute( { schemaOverlayObject: "taxonomy_term--unit--%", attributeName: "taxonomy_term--unit--%", required: false});
//LOOK we always push quantity here even if there is no quantity specified, to ensure that the type or name information is stored in the material reference of the quantity
seedTreatmentConvention.addAttribute( { schemaOverlayObject: "quantity--material--rate", attributeName: "quantity--material--rate", required: true});
seedTreatmentConvention.addAttribute( { schemaOverlayObject: "taxonomy_term--unit--amount", attributeName: "taxonomy_term--unit--amount", required: true});


////add relationships
seedTreatmentConvention.addRelationship( { containerEntity:"log--input--seed_treatment" , relationName:"category" , mentionedEntity:"taxonomy_term--log_category" , required: false } );
seedTreatmentConvention.addRelationship( { containerEntity:"log--input--seed_treatment", relationName:"asset", mentionedEntity:"asset--plant--planting", required: true } );
seedTreatmentConvention.addRelationship( { containerEntity:"log--input--seed_treatment" , relationName:"quantity" , mentionedEntity:"quantity--standard--area_percentage" , required: false } );
seedTreatmentConvention.addRelationship( { containerEntity:"quantity--standard--area_percentage" , relationName:"units" , mentionedEntity:"taxonomy_term--unit--%" , required: false } );
seedTreatmentConvention.addRelationship( { containerEntity:"log--input--seed_treatment" , relationName:"quantity" , mentionedEntity:"quantity--material--rate" , required: false } );
seedTreatmentConvention.addRelationship( { containerEntity:"quantity--material--rate" , relationName:"units" , mentionedEntity:"taxonomy_term--unit--amount" , required: false } );
seedTreatmentConvention.addRelationship( { containerEntity:"quantity--material--rate", relationName:"material_type" , mentionedEntity:"taxonomy_term--material_type--herbicide_or_pesticide" , required: true } );
seedTreatmentConvention.addRelationship( { containerEntity:"log--input--seed_treatment" , relationName:"quantity" , mentionedEntity:"quantity--material--active_ingredient_percent" , required: false } );
seedTreatmentConvention.addRelationship( { containerEntity:"quantity--material--active_ingredient_percent" , relationName:"units" , mentionedEntity:"taxonomy_term--unit--%" , required: false } );



seedTreatmentConvention.addExample({ path: `${__dirname}/examples/correct`, is_valid_example: true });
seedTreatmentConvention.addExample({ path: `${__dirname}/examples/incorrect`, is_valid_example: false });

let storageOperation = seedTreatmentConvention.store();
