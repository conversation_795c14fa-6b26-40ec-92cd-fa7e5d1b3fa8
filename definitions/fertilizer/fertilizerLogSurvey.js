// Common Farm Convention
const builder = require('convention_builder');
const { randomUUID } = require('crypto');
const fs = require('fs');
require('dotenv').config({ path: `${__dirname}/../.env` });

//LOOK can we just use this for nonNPK as well? if we make quantities not required I think it would be the same
// Previously, nonNPK and NPK fertilizers were split up. NonNPK fertilizer has quantities NOT required. These are now combined here. 
// This is the previous description for NPK fertilizer: "This log records nitrogen, phosphorus and potassium fertilizers. It allows the user to create new and/or select multiple fertilizer names and brands. There is another survey question for non-NPK fertilizers. This log has the option to record frequency of application, brand, application method, rate, units, %N, %P, %K and other minerals."

// ids for the examples
let fertilizer_convention_uuid = randomUUID();
let quantity__standard__moisture_percentage_uuid = randomUUID();
let quantity__material__nitrogen_percentage_uuid = randomUUID();
let quantity__material__phosphorus_percentage_uuid = randomUUID();
let quantity__material__potassium_percentage_uuid = randomUUID();
let quantity__material__inhibition_id_uuid = randomUUID();
let quantity__standard__area_percentage_uuid = randomUUID();
let taxonomy_term__unit__percentage_uuid = randomUUID();
let log__input__fertilizer_uuid = randomUUID();
let taxonomy_term__unit__rate_uuid = randomUUID();
let taxonomy_term__material_type__fertilizer_uuid = randomUUID();
let taxonomy_term__material_type__phosphorus_uuid = randomUUID();
let taxonomy_term__material_type__potassium_uuid = randomUUID();

//examples
let log__input__fertilizer_example = {
    id:log__input__fertilizer_uuid,
    attributes: {
        name: "example fertilizer log",
        status:"done",
    }
};
let log__input__fertilizer_error = {
    id:log__input__fertilizer_uuid,
    attributes: {
        name: "example fertilizer log",
        status:"true",
    }
};

//Main entity
let log__input__fertilizer = new builder.SchemaOverlay({
    typeAndBundle: 'log--input',
    name: 'fertilizer',
    validExamples: [log__input__fertilizer_example],
    erroredExamples: [log__input__fertilizer_error]    
});

log__input__fertilizer.setMainDescription("This log records fertilizers including NPK (nitrogen, phosphorus, and potassium) and non-NPK fertilizers. This allows the user to create new an/or select multiple fertilizer names, brands and enter homemade options. This survey question includes non-NPK fertilizers, optionally allowing the user to record frequency of application, brand, application method, rate, units, %N, %P, %K, and other minerals.");

log__input__fertilizer.setConstant({
    attribute:"status",
    value:"done"
});

//fertilizer name
let taxonomy_term__material_type__fertilizer_example = {
    id:taxonomy_term__material_type__fertilizer_uuid,
    attributes: {
        name: "boron"
    }
};
let taxonomy_term__material_type__fertilizer_error = {
    id: taxonomy_term__material_type__fertilizer_uuid,
    attributes: {
        name: "home made"
    }
};

let taxonomy_term__material_type__fertilizer = new builder.SchemaOverlay({
    typeAndBundle: "taxonomy_term--material_type",
    name: "fertilizer",
    validExamples: [taxonomy_term__material_type__fertilizer_example],
    erroredExamples: [taxonomy_term__material_type__fertilizer_error]
});
taxonomy_term__material_type__fertilizer.setMainDescription("The name of the fertilizer is stored here.");

taxonomy_term__material_type__fertilizer.setEnum({
    attribute: "name",
    valuesArray: [
        "alfalfa",
        "alfalfameal",
        "algea_potash",
        "all_purpose_plant_food",
        "ammonia_sulfate",
        "aragonite",
        "azomite",
        "basalt",
        "bio_humus",
        "bio-cal",
        "biogenesis",
        "black_gypsum_dg",
        "bloodmeal",
        "borax",
        "boron",
        "boron_10",
        "boron_15",
        "brown_phosphate",
        "calphos",
        "calcium",
        "calcium_sulfate",
        "carbonotite",
        "cardboard",
        "chilean_nitrate",
        "consortium",
        "copper_sulfate",
        "elemental_sulfur",
        "epsom_salt",
        "fish_seaweed",
        "fish_emulsion",
        "fishbone_meal",
        "green_sand",
        "gypsum",
        "gypsum_pelletized",
        "hi-k",
        "holomac",
        "holomic",
        "humates",
        "iron_sulfate",
        "ferrous_sulphate",
        "kelp_meal",
        "kelpak",
        "k-mag",
        "lime",
        "magnesium_sulphate",
        "manganese_sulphate",
        "nitrogen",
        "peanut_meal",
        "phosphorus",
        "plastic",
        "potassium_sulfate",
        "rock_phosphate_pelletized",
        "rock_phosphate_soft",
        "rootsurge",
        "seasalt",
        "seashield",
        "seaweed",
        "sodium_molybdate",
        "solubor",
        "soybeanmeal",
        "soymeal",
        "spectrum",
        "sulphur_plus",
        "supercal_so4",
        "tenessee_brown_rock",
        "wood_chips",
        "worm_castings",
        "worm_castings",
        "zeolite",
        "zinc_sulfate",
        "ammonium_nitrate",
        "ammonium_nitrate_sulfate",
        "ammonium_phosphate",
        "ammonium_polyphosphate",
        "ammonium_thiosulfate",
        "anhydrous_ammonia",
        "aqua_ammonia",
        "calcium_ammonium_nitrate",
        "calcium_nitrate",
        "diammnoium_phosphate",
        "esn",
        "liquid_phosphoric_acid",
        "monoammonium_phosphate",
        "muriate_of_potash",
        "potassium_chloride",
        "potassium_nitrate",
        "single_super_phosphate",
        "sulpomag",
        "triple_super_phosphate",
        "urea",
        "urea_ammonium_nitrate",
        "zinc_ultra_che",
        "uan_28",
        "uan_32",
        "custom"                
    ]
});


let taxonomy_term__material_type__phosphorus_example ={
    id: taxonomy_term__material_type__phosphorus_uuid,
    attributes: {
        name: "K"
    }
};
let taxonomy_term__material_type__phosphorus_error = {
    id: taxonomy_term__material_type__phosphorus_uuid
};
let taxonomy_term__material_type__phosphorus = new builder.SchemaOverlay({
    typeAndBundle: "taxonomy_term--material_type",
    name: "phosphorus",
    validExamples: [taxonomy_term__material_type__phosphorus_example],
    erroredExamples: [taxonomy_term__material_type__phosphorus_error]
});
taxonomy_term__material_type__phosphorus.setMainDescription("The type of phosphorus in a custom fertilizer is stored here.");

taxonomy_term__material_type__phosphorus.setEnum({
    attribute: "name",
    valuesArray: [
        "P2O5",
        "phosphorus"               
    ]
});

let taxonomy_term__material_type__potassium_example ={
    id: taxonomy_term__material_type__potassium_uuid,
    attributes: {
        name: "P2O5"
    }
};
let taxonomy_term__material_type__potassium_error = {
    id: taxonomy_term__material_type__potassium_uuid
};
let taxonomy_term__material_type__potassium = new builder.SchemaOverlay({
    typeAndBundle: "taxonomy_term--material_type",
    name: "potassium",
    validExamples: [taxonomy_term__material_type__potassium_example],
    erroredExamples: [taxonomy_term__material_type__potassium_error]
});
taxonomy_term__material_type__potassium.setMainDescription("The type of potassium in a custom fertilizer is stored here.");

taxonomy_term__material_type__potassium.setEnum({
    attribute: "name",
    valuesArray: [
        "K2O",
        "K"               
    ]
});

//inhibition id
let quantity__material__inhibition_id = new builder.SchemaOverlay({
    typeAndBundle: "quantity--material",
    name: "inhibition_id",
    validExamples: [],
    erroredExamples: []
});
quantity__material__inhibition_id.setMainDescription("If the fertilizer has a nitrification inhibitor that information is stored here.");

quantity__material__inhibition_id.setEnum({
    attribute: "label",
    valuesArray: [
        "nitrification_inhibitor",
        "none"                
    ]
});

// LOOK: refactor examples from log--input--npk_fertilizer to log--input--fertilizer
//convention
let fertilizerConvention = new builder.ConventionSchema({
    title: "Fertilizer",
    version: "0.0.2",
    schemaName:"log--input--fertilizer",
    repoURL:"www.gitlabrepo.com/version/farmos_conventions",
    description:`## Purpose\n
The fertilizer log records all fertilizer applications, including both nitrogen, phosphorus and potassium (NPK) fertilizer applications and non-NPK applications.\n
## Specification\n
text\n`,
    validExamples: [],
    erroredExamples: []
});

//LOOK do we need method or brand (don't think so because there's nothing additional that we need beyond what's already specified in shcema). Debugger saying schema doesn't exist for quantity--standard--moisture_percentage
//add local attributes
fertilizerConvention.addAttribute( { schemaOverlayObject:log__input__fertilizer, attributeName: "log--input--fertilizer", required: true } );
fertilizerConvention.addAttribute( { schemaOverlayObject:taxonomy_term__material_type__fertilizer, attributeName: "taxonomy_term--material_type--fertilizer", required: true } );
fertilizerConvention.addAttribute( { schemaOverlayObject:quantity__material__inhibition_id, attributeName: "quantity--material--inhibition_id", required: false } );
fertilizerConvention.addAttribute( { schemaOverlayObject:taxonomy_term__material_type__phosphorus, attributeName: "taxonomy_term--material_type--phosphorus", required: false } );
fertilizerConvention.addAttribute( { schemaOverlayObject:taxonomy_term__material_type__potassium, attributeName: "taxonomy_term--material_type--potassium", required: false } );

//add global attributes
fertilizerConvention.addAttribute( { schemaOverlayObject:"taxonomy_term--log_category", attributeName: "taxonomy_term--log_category", required: false } );
fertilizerConvention.addAttribute( { schemaOverlayObject:"asset--plant--planting", attributeName: "asset--plant--planting", required: false } );
fertilizerConvention.addAttribute( { schemaOverlayObject:"quantity--standard--area_percentage", attributeName: "quantity--standard--area_percentage", required: false});
fertilizerConvention.addAttribute( { schemaOverlayObject:"taxonomy_term--unit--%", attributeName: "taxonomy_term--unit--%", required: false});
fertilizerConvention.addAttribute( { schemaOverlayObject:"quantity--standard--moisture_percentage", attributeName: "quantity--standard--moisture_percentage", required: false } );
fertilizerConvention.addAttribute( { schemaOverlayObject:"quantity--material--nitrogen_percentage", attributeName: "quantity--material--nitrogen_percentage", required: false } );
fertilizerConvention.addAttribute( { schemaOverlayObject:"quantity--material--phosphorus_percentage", attributeName: "quantity--material--phosphorus_percentage", required: false } );
fertilizerConvention.addAttribute( { schemaOverlayObject:"quantity--material--potassium_percentage", attributeName: "quantity--material--potassium_percentage", required: false } );
fertilizerConvention.addAttribute( { schemaOverlayObject:"quantity--material--rate", attributeName: "quantity--material--rate", required: false } );
fertilizerConvention.addAttribute( { schemaOverlayObject:"taxonomy_term--log_category--amendment", attributeName: "taxonomy_term--log_category--amendment", required: false } );
fertilizerConvention.addAttribute( { schemaOverlayObject:"taxonomy_term--unit--rate", attributeName: "taxonomy_term--unit--rate", required: false } );
fertilizerConvention.addAttribute( { schemaOverlayObject:"asset--land", attributeName: "asset--land", required: false } );

//add relationships
fertilizerConvention.addRelationship( { containerEntity:"log--input--fertilizer" , relationName:"category" , mentionedEntity:"taxonomy_term--log_category" , required: false } );
fertilizerConvention.addRelationship( { containerEntity:"log--input--fertilizer", relationName:"asset", mentionedEntity:"asset--plant--planting", required: false } );
fertilizerConvention.addRelationship( { containerEntity:"log--input--fertilizer" , relationName:"quantity" , mentionedEntity:"quantity--standard--area_percentage" , required: false } );
fertilizerConvention.addRelationship( { containerEntity:"quantity--standard--area_percentage" , relationName:"units" , mentionedEntity:"taxonomy_term--unit--%" , required: false } );
fertilizerConvention.addRelationship( { containerEntity:"log--input--fertilizer" , relationName:"quantity" , mentionedEntity:"quantity--standard--moisture_percentage" , required: false } );
fertilizerConvention.addRelationship( { containerEntity:"quantity--standard--moisture_percentage" , relationName:"units" , mentionedEntity:"taxonomy_term--unit--%" , required: false } );
fertilizerConvention.addRelationship( { containerEntity:"log--input--fertilizer" , relationName:"quantity" , mentionedEntity:"quantity--material--nitrogen_percentage" , required: false } );
fertilizerConvention.addRelationship( { containerEntity:"quantity--material--nitrogen_percentage" , relationName:"units" , mentionedEntity:"taxonomy_term--unit--%" , required: false } );
fertilizerConvention.addRelationship( { containerEntity:"log--input--fertilizer" , relationName:"quantity" , mentionedEntity:"quantity--material--phosphorus_percentage" , required: false } );
fertilizerConvention.addRelationship( { containerEntity:"quantity--material--phosphorus_percentage" , relationName:"units" , mentionedEntity:"taxonomy_term--unit--%" , required: false } );
fertilizerConvention.addRelationship( { containerEntity:"quantity--material--phosphorus_percentage" , relationName:"material_type" , mentionedEntity:"taxonomy_term--material_type--phosphorus" , required: false } );
fertilizerConvention.addRelationship( { containerEntity:"log--input--fertilizer" , relationName:"quantity" , mentionedEntity:"quantity--material--potassium_percentage" , required: false } );
fertilizerConvention.addRelationship( { containerEntity:"quantity--material--potassium_percentage" , relationName:"units" , mentionedEntity:"taxonomy_term--unit--%" , required: false } );
fertilizerConvention.addRelationship( { containerEntity:"quantity--material--potassium_percentage" , relationName:"material_type" , mentionedEntity:"taxonomy_term--material_type--potassium" , required: false } );
fertilizerConvention.addRelationship( { containerEntity:"log--input--fertilizer" , relationName:"quantity" , mentionedEntity:"quantity--material--rate" , required: false } );
fertilizerConvention.addRelationship( { containerEntity:"log--input--fertilizer" , relationName:"category" , mentionedEntity:"taxonomy_term--log_category--amendment" , required: true } );
fertilizerConvention.addRelationship( { containerEntity:"quantity--material--rate" , relationName:"units" , mentionedEntity:"taxonomy_term--unit--rate" , required: false } );
fertilizerConvention.addRelationship( { containerEntity:"quantity--material--rate" , relationName:"material_type" , mentionedEntity:"taxonomy_term--material_type--fertilizer" , required: false } );
fertilizerConvention.addRelationship( { containerEntity:"log--input--fertilizer" , relationName:"quantity" , mentionedEntity:"quantity--material--inhibition_id" , required: false } );
fertilizerConvention.addRelationship( { containerEntity:"log--input--fertilizer" , relationName:"location" , mentionedEntity:"asset--land" , required: false } );

//examples
let asset__plant__planting_attributes = fertilizerConvention.overlays['asset--plant--planting'].validExamples?.[0]?.attributes;
let asset__plant__planting_uuid = fertilizerConvention.overlays['asset--plant--planting'].validExamples?.[0]?.id;
let taxonomy_term__convention_uuid = randomUUID();

let quantity__material__rate_attributes = fertilizerConvention.overlays['quantity--material--rate'].validExamples?.[0]?.attributes;
let quantity__material__rate_uuid = fertilizerConvention.overlays['quantity--material--rate'].validExamples?.[0]?.id;

//let taxonomy_term__log_category__amendment_attributes = fertilizerConvention.overlays.log_category.validExamples?.[0]?.attributes;
let taxonomy_term__log_category__amendment_uuid = fertilizerConvention.overlays['taxonomy_term--log_category--amendment'].validExamples?.[0]?.id;

var fertilizerConventionExample = {
    id: fertilizer_convention_uuid,
    type: "Object",
    "convention_taxonomy": {
        id: taxonomy_term__convention_uuid,
        attributes: {
            name: 'Fertilizer',
            external_uri:'https://gitlab.com/api/v4/projects/44311021/jobs/artifacts/main/raw/output/collection/conventions/log--input--fertilizer/schema.json?job=copy_schemas'
        }
    },
    "asset--plant--planting": {
        id: asset__plant__planting_uuid,
        attributes: asset__plant__planting_attributes
    },
    "log--input--fertilizer": {
        id: log__input__fertilizer_uuid,
        attributes: {
            name: "example fertilizer log",
            status: "done",
            timestamp: ( new Date() ).toISOString(),
        },
        relationships: {
            convention: { data: [
                {
                    type: 'taxonomy_term--convention',
                    id: taxonomy_term__convention_uuid,
                }
            ]},
            asset: { data: [
                {
                    type: "asset--plant",
                    id: asset__plant__planting_uuid
                }
            ]},
            category: { data: [
                {
                    type: "taxonomy_term--log_category",
                    id: taxonomy_term__log_category__amendment_uuid
                }
            ]},
            quantity: { data: [
                {
                    type: "quantity--standard",
                    id: quantity__standard__area_percentage_uuid
                },
                {
                    type: "quantity--standard",
                    id: quantity__standard__moisture_percentage_uuid
                },
                {
                    type: "quantity--material",
                    id: quantity__material__nitrogen_percentage_uuid
                },
                {
                    type: "quantity--material",
                    id: quantity__material__phosphorus_percentage_uuid
                },
                {
                    type: "quantity--material",
                    id: quantity__material__potassium_percentage_uuid
                },
                {
                    type: "quantity--material",
                    id: quantity__material__inhibition_id_uuid
                },
                {
                    type: "quantity--material",
                    id: quantity__material__rate_uuid
                }
                ]} 
        },
    },
    "taxonomy_term--log_category--amendment": {
        id: taxonomy_term__log_category__amendment_uuid,
        attributes: {
            name: "amendment"
        }
    },
    "quantity--material--inhibition_id": {
        id: quantity__material__inhibition_id_uuid,
        attributes: {
            label: "none"
        }
    },
    "quantity--material--rate": {
        id: quantity__material__rate_uuid,
        attributes: quantity__material__rate_attributes,
        relationships: {
            units: { data: [
                {
                    type:"taxonomy_term--unit",
                    id: taxonomy_term__unit__rate_uuid
                }
            ] },
            material_type: { data: [
                {
                    type:"taxonomy_term--material_type",
                    id: taxonomy_term__material_type__fertilizer_uuid 
                }
            ]}
        }
    },
    "taxonomy_term--material_type--fertilizer":{
        id: taxonomy_term__material_type__fertilizer_uuid,
        attributes: {
            name: "ammonia_sulfate"
        }
    },
    "taxonomy_term--unit--rate":{
        id: taxonomy_term__unit__rate_uuid,
        attributes: {
            name: "g_acre"
        }
    },
    "quantity--standard--area_percentage": {
        id: quantity__standard__area_percentage_uuid,
        attributes: {
            label: "area"
        },
        relationships: {
            units: { data: [
                {
                    type:"taxonomy_term--unit",
                    id: taxonomy_term__unit__percentage_uuid
                }
            ] }
        }
    },
    "taxonomy_term--unit--%":  {
        id: taxonomy_term__unit__percentage_uuid,
        attributes: {
            name: "%"
        },   
    },
    "quantity--standard--moisture_percentage": {
        id: quantity__standard__moisture_percentage_uuid,
        attributes: {
            label: "moisture",
            measure: "water_content"
        },
        relationships: {
            units: { data: [
                {
                    type:"taxonomy_term--unit",
                    id: taxonomy_term__unit__percentage_uuid
                }
            ] }
        }
    },
    "quantity--material--nitrogen_percentage": {
        id: quantity__material__nitrogen_percentage_uuid,
        attributes: {
            label: "n",
            measure: "ratio"
        },
        relationships: {
            units: { data: [
                {
                    type:"taxonomy_term--unit",
                    id: taxonomy_term__unit__percentage_uuid
                }
            ] }
        }
    },
    "quantity--material--phosphorus_percentage": {
        id: quantity__material__phosphorus_percentage_uuid,
        attributes: {
            label: "p",
            measure: "ratio"
        },
        relationships: {
            units: { data: [
                {
                    type:"taxonomy_term--unit",
                    id: taxonomy_term__unit__percentage_uuid
                }
            ] }
        }
    },
    "quantity--material--potassium_percentage": {
        id: quantity__material__potassium_percentage_uuid,
        attributes: {
            label: "k",
            measure: "ratio"
        },
        relationships: {
            units: { data: [
                {
                    type:"taxonomy_term--unit",
                    id: taxonomy_term__unit__percentage_uuid
                }
            ] }
        }
    }
};

// Errors: [Missing or malformed taxonomy_term--material_type--fertilizer, 'status' must be constant 'done', missing required relationship 'category' ... 
// quantity--material--nitrogen_percentage must have const label 'n', quantity--material--phosphorus_percentage must have const label 'p' .... 
// quantity--material--potassium_percentage must have const label 'k']
var fertilizerConventionError = {
    id: fertilizer_convention_uuid,
    type: "Object",
    "convention_taxonomy": {
        id: taxonomy_term__convention_uuid,
        attributes: {
            name: 'Fertilizer',
            external_uri:'https://gitlab.com/api/v4/projects/44311021/jobs/artifacts/main/raw/output/collection/conventions/log--input--fertilizer/schema.json?job=copy_schemas'
        }
    },
    "asset--plant--planting": {
        id: asset__plant__planting_uuid,
        attributes: asset__plant__planting_attributes
    },
    "log--input--fertilizer": {
        id: log__input__fertilizer_uuid,
        attributes: {
            name: "example fertilizer log",
            status: "pending",
            timestamp: ( new Date() ).toISOString(),
        },
        relationships: {
            convention: { data: [
                {
                    type: 'taxonomy_term--convention',
                    id: taxonomy_term__convention_uuid,
                }
            ]},
            asset: { data: [
                {
                    type: "asset--plant",
                    id: asset__plant__planting_uuid
                }
            ]},
            quantity: { data: [
                {
                    type: "quantity--standard",
                    id: quantity__standard__area_percentage_uuid
                },
                {
                    type: "quantity--standard",
                    id: quantity__standard__moisture_percentage_uuid
                },
                {
                    type: "quantity--material",
                    id: quantity__material__nitrogen_percentage_uuid
                },
                {
                    type: "quantity--material",
                    id: quantity__material__phosphorus_percentage_uuid
                },
                {
                    type: "quantity--material",
                    id: quantity__material__potassium_percentage_uuid
                },
                {
                    type: "quantity--material",
                    id: quantity__material__rate_uuid
                }
                ]} 
        },
    },
    "quantity--material--rate": {
        id: quantity__material__rate_uuid,
        attributes: quantity__material__rate_attributes
    },
    "quantity--standard--area_percentage": {
        id: quantity__standard__area_percentage_uuid,
        attributes: {
            label: "area"
        },
        relationships: {
            units: { data: [
                {
                    type:"taxonomy_term--unit",
                    id: taxonomy_term__unit__percentage_uuid
                }
            ] }
        }
    },
    "taxonomy_term--unit--%":  {
        id: taxonomy_term__unit__percentage_uuid,
        attributes: {
            name: "%"
        },   
    },
    "quantity--material--nitrogen_percentage": {
        id: quantity__material__nitrogen_percentage_uuid,
        attributes: {
            label: "nitrogen",
            measure: "ratio"
        },
        relationships: {
            units: { data: [
                {
                    type:"taxonomy_term--unit",
                    id: taxonomy_term__unit__percentage_uuid
                }
            ] }
        }
    },
    "quantity--material--phosphorus_percentage": {
        id: quantity__material__phosphorus_percentage_uuid,
        attributes: {
            label: "phosphorus",
            measure: "ratio"
        },
        relationships: {
            units: { data: [
                {
                    type:"taxonomy_term--unit",
                    id: taxonomy_term__unit__percentage_uuid
                }
            ] }
        }
    },
    "quantity--material--potassium_percentage": {
        id: quantity__material__potassium_percentage_uuid,
        attributes: {
            label: "potassium",
            measure: "ratio"
        },
        relationships: {
            units: { data: [
                {
                    type:"taxonomy_term--unit",
                    id: taxonomy_term__unit__percentage_uuid
                }
            ] }
        }
    }
};

fertilizerConvention.validExamples = [fertilizerConventionExample];
fertilizerConvention.erroredExamples = [fertilizerConventionError];

let test = fertilizerConvention.testExamples();
let storageOperation = fertilizerConvention.store();
