// Common Farm Convention
const builder = require('convention_builder');
const { randomUUID } = require('crypto');
const fs = require('fs');
require('dotenv').config({ path: `${__dirname}/../../.env` });

//LOOK can we just use this for nonNPK as well? if we make quantities not required I think it would be the same
// Previously, nonNPK and NPK fertilizers were split up. NonNPK fertilizer has quantities NOT required. These are now combined here. 
// This is the previous description for NPK fertilizer: "This log records nitrogen, phosphorus and potassium fertilizers. It allows the user to create new and/or select multiple fertilizer names and brands. There is another survey question for non-NPK fertilizers. This log has the option to record frequency of application, brand, application method, rate, units, %N, %P, %K and other minerals."

//Main entity
let log__input__fertilizer = new builder.SchemaOverlay({
    typeAndBundle: 'log--input',
    name: 'fertilizer',
});

log__input__fertilizer.setMainDescription("This log records fertilizers including NPK (nitrogen, phosphorus, and potassium) and non-NPK fertilizers. This allows the user to create new an/or select multiple fertilizer names, brands and enter homemade options. This survey question includes non-NPK fertilizers, optionally allowing the user to record frequency of application, brand, application method, rate, units, %N, %P, %K, and other minerals.");

log__input__fertilizer.setConstant({
    attribute:"status",
    value:"done"
});

//fertilizer name
let taxonomy_term__material_type__fertilizer = new builder.SchemaOverlay({
    typeAndBundle: "taxonomy_term--material_type",
    name: "fertilizer",
});
taxonomy_term__material_type__fertilizer.setMainDescription("The name of the fertilizer is stored here.");

let fertilizerMaterialsList = JSON.parse( fs.readFileSync(`${__dirname}/fertilizer_taxonomy.json`) );

taxonomy_term__material_type__fertilizer.setEnum({
    attribute: "name",
    valuesArray: fertilizerMaterialsList
});

/// N, P and K sections
let taxonomy_term__material_type__phosphorus = new builder.SchemaOverlay({
    typeAndBundle: "taxonomy_term--material_type",
    name: "phosphorus",
});
taxonomy_term__material_type__phosphorus.setMainDescription("The type of phosphorus in a custom fertilizer is stored here.");

taxonomy_term__material_type__phosphorus.setEnum({
    attribute: "name",
    valuesArray: [
        "P2O5",
        "phosphorus"
    ]
});

let taxonomy_term__material_type__potassium = new builder.SchemaOverlay({
    typeAndBundle: "taxonomy_term--material_type",
    name: "potassium",
});
taxonomy_term__material_type__potassium.setMainDescription("The type of potassium in a custom fertilizer is stored here.");

taxonomy_term__material_type__potassium.setEnum({
    attribute: "name",
    valuesArray: [
        "K2O",
        "K"
    ]
});

//inhibition id
let quantity__material__inhibition_id = new builder.SchemaOverlay({
    typeAndBundle: "quantity--material",
    name: "inhibition_id",
});
quantity__material__inhibition_id.setMainDescription("If the fertilizer has a nitrification inhibitor that information is stored here.");

quantity__material__inhibition_id.setEnum({
    attribute: "label",
    valuesArray: [
        "nitrification_inhibitor",
        "none"
    ]
});

// LOOK: refactor examples from log--input--npk_fertilizer to log--input--fertilizer
//convention
let fertilizerConvention = new builder.ConventionSchema({
    title: "Fertilizer",
    version: "0.0.2",
    schemaName:"log--input--fertilizer",
    repoURL:"www.gitlabrepo.com/version/farmos_conventions",
    description:`## Purpose\n
The fertilizer log records all fertilizer applications, including both nitrogen, phosphorus and potassium (NPK) fertilizer applications and non-NPK applications.\n
## Specification\n
text\n`,
});

//LOOK do we need method or brand (don't think so because there's nothing additional that we need beyond what's already specified in shcema). Debugger saying schema doesn't exist for quantity--standard--moisture_percentage
//add local attributes
fertilizerConvention.addAttribute( { schemaOverlayObject:log__input__fertilizer, attributeName: "log--input--fertilizer", required: true } );
fertilizerConvention.addAttribute( { schemaOverlayObject:taxonomy_term__material_type__fertilizer, attributeName: "taxonomy_term--material_type--fertilizer", required: true } );
fertilizerConvention.addAttribute( { schemaOverlayObject:quantity__material__inhibition_id, attributeName: "quantity--material--inhibition_id", required: false } );
fertilizerConvention.addAttribute( { schemaOverlayObject:taxonomy_term__material_type__phosphorus, attributeName: "taxonomy_term--material_type--phosphorus", required: false } );
fertilizerConvention.addAttribute( { schemaOverlayObject:taxonomy_term__material_type__potassium, attributeName: "taxonomy_term--material_type--potassium", required: false } );

//add global attributes
fertilizerConvention.addAttribute( { schemaOverlayObject:"taxonomy_term--log_category", attributeName: "taxonomy_term--log_category", required: false } );
fertilizerConvention.addAttribute( { schemaOverlayObject:"asset--plant--planting", attributeName: "asset--plant--planting", required: false } );
fertilizerConvention.addAttribute( { schemaOverlayObject:"quantity--standard--area_percentage", attributeName: "quantity--standard--area_percentage", required: false});
fertilizerConvention.addAttribute( { schemaOverlayObject:"taxonomy_term--unit--%", attributeName: "taxonomy_term--unit--%", required: false});
fertilizerConvention.addAttribute( { schemaOverlayObject:"quantity--standard--moisture_percentage", attributeName: "quantity--standard--moisture_percentage", required: false } );
fertilizerConvention.addAttribute( { schemaOverlayObject:"quantity--material--nitrogen_percentage", attributeName: "quantity--material--nitrogen_percentage", required: false } );
fertilizerConvention.addAttribute( { schemaOverlayObject:"quantity--material--phosphorus_percentage", attributeName: "quantity--material--phosphorus_percentage", required: false } );
fertilizerConvention.addAttribute( { schemaOverlayObject:"quantity--material--potassium_percentage", attributeName: "quantity--material--potassium_percentage", required: false } );
fertilizerConvention.addAttribute( { schemaOverlayObject:"quantity--material--rate", attributeName: "quantity--material--rate", required: false } );
fertilizerConvention.addAttribute( { schemaOverlayObject:"taxonomy_term--log_category--amendment", attributeName: "taxonomy_term--log_category--amendment", required: false } );
fertilizerConvention.addAttribute( { schemaOverlayObject:"taxonomy_term--unit--rate", attributeName: "taxonomy_term--unit--rate", required: false } );
fertilizerConvention.addAttribute( { schemaOverlayObject:"asset--land", attributeName: "asset--land", required: false } );

//add relationships
fertilizerConvention.addRelationship( { containerEntity:"log--input--fertilizer" , relationName:"category" , mentionedEntity:"taxonomy_term--log_category" , required: false } );
fertilizerConvention.addRelationship( { containerEntity:"log--input--fertilizer", relationName:"asset", mentionedEntity:"asset--plant--planting", required: false } );
fertilizerConvention.addRelationship( { containerEntity:"log--input--fertilizer" , relationName:"quantity" , mentionedEntity:"quantity--standard--area_percentage" , required: false } );
fertilizerConvention.addRelationship( { containerEntity:"quantity--standard--area_percentage" , relationName:"units" , mentionedEntity:"taxonomy_term--unit--%" , required: false } );
fertilizerConvention.addRelationship( { containerEntity:"log--input--fertilizer" , relationName:"quantity" , mentionedEntity:"quantity--standard--moisture_percentage" , required: false } );
fertilizerConvention.addRelationship( { containerEntity:"quantity--standard--moisture_percentage" , relationName:"units" , mentionedEntity:"taxonomy_term--unit--%" , required: false } );
fertilizerConvention.addRelationship( { containerEntity:"log--input--fertilizer" , relationName:"quantity" , mentionedEntity:"quantity--material--nitrogen_percentage" , required: false } );
fertilizerConvention.addRelationship( { containerEntity:"quantity--material--nitrogen_percentage" , relationName:"units" , mentionedEntity:"taxonomy_term--unit--%" , required: false } );
fertilizerConvention.addRelationship( { containerEntity:"log--input--fertilizer" , relationName:"quantity" , mentionedEntity:"quantity--material--phosphorus_percentage" , required: false } );
fertilizerConvention.addRelationship( { containerEntity:"quantity--material--phosphorus_percentage" , relationName:"units" , mentionedEntity:"taxonomy_term--unit--%" , required: false } );
fertilizerConvention.addRelationship( { containerEntity:"quantity--material--phosphorus_percentage" , relationName:"material_type" , mentionedEntity:"taxonomy_term--material_type--phosphorus" , required: false } );
fertilizerConvention.addRelationship( { containerEntity:"log--input--fertilizer" , relationName:"quantity" , mentionedEntity:"quantity--material--potassium_percentage" , required: false } );
fertilizerConvention.addRelationship( { containerEntity:"quantity--material--potassium_percentage" , relationName:"units" , mentionedEntity:"taxonomy_term--unit--%" , required: false } );
fertilizerConvention.addRelationship( { containerEntity:"quantity--material--potassium_percentage" , relationName:"material_type" , mentionedEntity:"taxonomy_term--material_type--potassium" , required: false } );
fertilizerConvention.addRelationship( { containerEntity:"log--input--fertilizer" , relationName:"quantity" , mentionedEntity:"quantity--material--rate" , required: false } );
fertilizerConvention.addRelationship( { containerEntity:"log--input--fertilizer" , relationName:"category" , mentionedEntity:"taxonomy_term--log_category--amendment" , required: true } );
fertilizerConvention.addRelationship( { containerEntity:"quantity--material--rate" , relationName:"units" , mentionedEntity:"taxonomy_term--unit--rate" , required: false } );
fertilizerConvention.addRelationship( { containerEntity:"quantity--material--rate" , relationName:"material_type" , mentionedEntity:"taxonomy_term--material_type--fertilizer" , required: false } );
fertilizerConvention.addRelationship( { containerEntity:"log--input--fertilizer" , relationName:"quantity" , mentionedEntity:"quantity--material--inhibition_id" , required: false } );
fertilizerConvention.addRelationship( { containerEntity:"log--input--fertilizer" , relationName:"location" , mentionedEntity:"asset--land" , required: false } );

fertilizerConvention.addExample({ path: `${__dirname}/examples/correct`, is_valid_example: true});
fertilizerConvention.addExample({path: `${__dirname}/examples/incorrect`, is_valid_example: false});

let test = fertilizerConvention.testExamples();
let storageOperation = fertilizerConvention.store();
