const { randomUUID } = require('crypto');
const fs = require('fs');


//uuids
let log__input__energy_uuid = randomUUID();
let quantity__material__energy_uuid = randomUUID();
let taxonomy_term__unit__energy_uuid = randomUUID();
let taxonomy_term__material_type__energy_uuid = randomUUID();
let taxonomy_term__convention_uuid = randomUUID();

//examples
let log__input__energy_example = {
    id:log__input__energy_uuid,
    attributes: {
        name: "energy log",
        status:"done",
    },
    relationships: {
        convention: { data: [
            {
                type: 'taxonomy_term--convention',
                id: taxonomy_term__convention_uuid,
            }
        ]},
    }
};
let log__input__energy_error = {
    id:log__input__energy_uuid,
    attributes: {
        name: "energy log",
        status:"pending",
    }
};

exports.energy_example = log__input__energy_example;
exports.energy_error = log__input__energy_error;

//taxonomy term - units
let taxonomy_term__unit__energy_example = {
        id:taxonomy_term__unit__energy_uuid,
        attributes: {
            name: "liters",
        }
};
let taxonomy_term__unit__energy_error = {
    id:taxonomy_term__unit__energy_uuid,
    attributes: {
        name: "gallons",
    }
};

exports.energy_unit = taxonomy_term__unit__energy_example;
exports.enerty_unit_error = taxonomy_term__unit__energy_error;


let taxonomy_term__material_type__energy_example = {
    id:taxonomy_term__material_type__energy_uuid,
    attributes: {
        name: "liters",
    }
};
let taxonomy_term__material_type__energy_error = {
    id:taxonomy_term__material_type__energy_uuid,
    attributes: {
        name: "gallons",
    }
};

exports.material_type_example = taxonomy_term__material_type__energy_example;
exports.material_type_error = taxonomy_term__material_type__energy_error;
