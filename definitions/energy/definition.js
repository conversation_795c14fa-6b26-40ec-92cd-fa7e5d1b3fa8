const builder = require('convention_builder');
const { randomUUID } = require('crypto');
const fs = require('fs');
const examples = require("./energy_example_creation");
require('dotenv').config({ path: `${__dirname}/../.env` });

////overlays and constants
let log__input__energy = new builder.SchemaOverlay({
    typeAndBundle: 'log--input',
    name: 'energy',
    validExamples: [examples.energy_example],
    erroredExamples: [examples.energy_error]
});
//LOOK add description
log__input__energy.setMainDescription(
    "Log for detailing any on-farm energy use."
    );

log__input__energy.setConstant({
    attribute:"status",
    value:"done"
});


////Overlays and constants
let quantity__material__energy = new builder.SchemaOverlay({
    typeAndBundle: "quantity--material",
    name: "energy",
    validExamples: [  ],
    erroredExamples: [  ]
});
quantity__material__energy.setMainDescription("This represents the total quantity of energy used.");


let taxonomy_term__unit__energy = new builder.SchemaOverlay({
    typeAndBundle: "taxonomy_term--unit",
    name: "energy",
    validExamples: [examples.energy_unit],
    erroredExamples: [examples.enerty_unit_error]
});
taxonomy_term__unit__energy.setMainDescription("Units for energy added are recorded in liters.");
taxonomy_term__unit__energy.setEnum({
    attribute: "name",
    valuesArray: [
        "liters",
    ],
});

//taxonomy term - material_type
let taxonomy_term__material_type__energy = new builder.SchemaOverlay({
    typeAndBundle: "taxonomy_term--material_type",
    name: "energy",
    validExamples: [examples.material_type_example],
    erroredExamples: [examples.material_type_error]
});
taxonomy_term__material_type__energy.setMainDescription("The type of fuel is recorded here.");
taxonomy_term__material_type__energy.setEnum({
    attribute: "name",
    valuesArray: [
        "diesel (average biofuel blend)",
        "diesel (100% mineral diesel)",
        "petrol (average biofuel blend)",
        "petrol (100% mineral petrol)"
    ],
});


//equipment asset
//let asset__equipment__energy_example = {
//    id:asset__plant__planting_uuid,
//    attributes: {
//        name: "example div veg plant asset",
//        geometry: null,
//        status:"active"
//    }
//};
//let asset__equipment__energy_error = {
//    id:asset__plant__planting_uuid,
//    attributes: {
//        name: "example div veg plant asset",
//        geometry: "[91.93934434,-40.345345]",
//        status:"true"
//    }
//};
//let asset__equipment__energy = new builder.SchemaOverlay({
//    typeAndBundle: 'asset--equipment',
//    name: 'energy',
//    validExamples: [asset__equipment__energy_example],
//    erroredExamples: [asset__equipment__energy_error]    
//});

//asset__equipment__energy.setMainDescription("The planting is the main asset that all management logs will reference.");

//need to add equipment type
//taxonomy_term__machine_type__energy.setEnum({
//    attribute: "name",
//    valuesArray: [
//        "baler (250 kg round bale)",
//        "chisel plough",
//       "disc harrow",
//        "grain drill",
//        "grain drill-notill",
//        "hoe drill",
//        "moldboard plough",
//        "pneumatic drill",
//        "rod weeder",
//        "roller harrow",
//        "roller packer",
//        "rotary hoe/bed tiller",
//        "row crop cultivator",
//        "row crop planter",
//        "subsoiler",
//        "sweep plough",
//        "tine harrow/seed handling transport",
//        "tooth harrow",
//        "herbicide spraying",
//        "fertiliser spraying",
//        "fertiliser spreading",
//        "beet harvester",
//        "combine",
//        "corn combine",
//        "forage harvester",
//        "manure spreader",
//        "mower/grader",
//        "mower-conditioner",
//        "potato harvester",
//        "potato windrower",
//        "windrower/swather",
//        "biocide spraying",
//        "disc harrowing",
//        "roller harrowing",
//        "potato planting",
//        "forage harvester - corn hoeing with chipper",
//        "mower/grader - rotary mower - cultivation",
//        "peas harvester",
//        "pressing of dry crop (straw/hay) high-density pick-up baler (13 kg/bale)",
//        "liming",
//        "pump tank truck - drag hose",
//        "vacuum tanker",
//        "wagon for ventilated hay",
//        "wagon for wilted material",
//        "wrapping of bales",
//        "crop protection - syringe (orchards)",
//        "fertiliser spraying (orchards)",
//        "mulching - flail mulcher (orchards)",
//        "winter cut - flail mulcher",
//        "scrubbing",
//        "flaming",
//        "grooming",
//        "hoeing and grooming",
//        "potato lifter loader",
//        "self-propelling potato lifter loader",
//        "self-propelling potato harvester",
//        "combination rotary harrow/ sower",
//        "combination spike drum/sower",
//        "mulching - seeding - corn",
//        "combine harrow (harrow + seed drill)",
//        "subsoiling (tillage)",
//        "all-around weeder",
//        "chopping wine wood",
//        "crop protection - syringe (viniculture)",
//        "foliage cut",
//        "grape harvester",
//        "grass seeding",
//        "grubbing",
//        "hilling of mounds",
//        "milling",
//        "mowing - disc mower",
//        "mulching - flail mulcher (viniculture)",
//        "roatry cultivation of tramlines",
//        "subsoiling (viniculture)",
//        "baler (250 kg square bale)",
//        "baler (silage)",
//        "beet harvester - standard",
//        "mower-conditioner - self-propelling rotary mower with conditioner",
//        "windrower/swather - rotary tedder",
//        "potato planting - potato planter - semiautomatic",
//        "forage harvester - grassland",
//        "coulter",
//        "disc gang",
//        "disk bedder",
//        "field cultivator/ridger",
//        "land plane/destoner",
//        "cotton picker",
//        "cotton stripper/potato topper",
//        "forage blower/washer",
//        "rake",
//        "tomato harvester",
//        "ridging",
//        "power harrow",
//        "manure injections",
//        "potato destoner"
//    ],
//});


//convention
let energyConvention = new builder.ConventionSchema({
    title: "Energy",
    version: "0.0.1",
    schemaName:"log--input--energy",
    repoURL:"www.gitlabrepo.com/version/farmos_conventions",
    //LOOK add description
    description:`## Purpose\n
    This convention allows the addition of energy consumption information to any 'log', and expected energy consumption levels to any 'asset'.  This convention will frequently overlap with other conventions - for example, a Tillage Convention log may also be an Energy Convention log.
    This convention uses 'quantity' to store the type and amount of energy used for any given 'log'. The convention specifies enum lists for the 'name' in the 'taxonomy_term' for units and material type related to the 'quantity'. This enforcement of lists improves comparability across energy consumption types (gallons of gasoline, kWh of electricity, etc.).
    This convention also specifies 'attributes' in 'assets' which can be used to calculate the energy-related 'quantity' information.  These 'attributes' include 'fuel_use', 'fuel_type', etc.
    Using both of these strategies, two compatible data collection methods are possible: 1) direct measurement of use (i.e. measuring fuel use in the tractor, energy use in the dryer, etc.) by putting those energy values as 'quantity' in the 'log'; 2) calculated use (i.e. calculating use based on the fuel efficiency and miles driven, efficiency rating of the dryer, etc.) by calculating the energy values in 'quantity' from the 'attributes' of the 'asset' being utilized.\n
## Specification\n
text\n`,
    validExamples: [],
    erroredExamples: []
});

//add local attributes
energyConvention.addAttribute( { schemaOverlayObject:log__input__energy, attributeName: "log--input--energy", required: true } );
energyConvention.addAttribute({ schemaOverlayObject:quantity__material__energy, attributeName: "quantity--material--energy", required: true});
energyConvention.addAttribute({ schemaOverlayObject:taxonomy_term__unit__energy, attributeName: "taxonomy_term--unit--energy", required: true});
energyConvention.addAttribute({ schemaOverlayObject:taxonomy_term__material_type__energy, attributeName: "taxonomy_term--material_type--energy", required: true});

//add global attributes
energyConvention.addAttribute( { schemaOverlayObject:"taxonomy_term--log_category", attributeName: "taxonomy_term--log_category", required: false } );
energyConvention.addAttribute( { schemaOverlayObject:"asset--plant--planting", attributeName: "asset--plant--planting", required: true } );
energyConvention.addAttribute({ schemaOverlayObject:"quantity--standard--area_percentage", attributeName: "quantity--standard--area_percentage", required: false});
energyConvention.addAttribute({ schemaOverlayObject:"taxonomy_term--unit--%", attributeName: "taxonomy_term--unit--%", required: false});

//add relationships
energyConvention.addRelationship( { containerEntity:"log--input--energy", relationName:"category" , mentionedEntity:"taxonomy_term--log_category" , required: false } );
energyConvention.addRelationship( { containerEntity:"log--input--energy", relationName:"asset", mentionedEntity:"asset--plant--planting", required: true } );
//add equipment asset relationship
energyConvention.addRelationship( { containerEntity:"log--input--energy", relationName:"quantity" , mentionedEntity:"quantity--standard--area_percentage" , required: false } );
energyConvention.addRelationship( { containerEntity:"quantity--standard--area_percentage" , relationName:"units" , mentionedEntity:"taxonomy_term--unit--%" , required: false } );
energyConvention.addRelationship( { containerEntity:"log--input--energy" , relationName:"quantity" , mentionedEntity:"quantity--material--energy" , required: true } );
energyConvention.addRelationship( { containerEntity:"quantity--material--energy" , relationName:"units" , mentionedEntity:"taxonomy_term--unit--energy" , required: true } );
energyConvention.addRelationship( { containerEntity:"quantity--material--energy" , relationName:"material_type" , mentionedEntity:"taxonomy_term--material_type--energy" , required: true } );

let asset__plant__planting_attributes = energyConvention.overlays['asset--plant--planting'].validExamples?.[0]?.attributes;
let asset__plant__planting_uuid = energyConvention.overlays['asset--plant--planting'].validExamples?.[0]?.id;
