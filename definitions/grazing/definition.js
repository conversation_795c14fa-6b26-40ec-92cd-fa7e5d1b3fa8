// Survey: https://app.surveystack.io/surveys/6346c1e467f839000121503e/edit
// Section: 41, grazing
const builder = require('convention_builder');
const fs = require('fs');
require('dotenv').config({ path: `${__dirname}/../../.env` });

//Main entity
////overlays and constants
let log__activity__grazing = new builder.SchemaOverlay({
    typeAndBundle: 'log--activity',
    name: 'grazing',
});
//LOOK review description
log__activity__grazing.setMainDescription("Grazing logs are used to track when a herd was moved in and/or out of an area. This log records field, % of area covered, type of livestock, grazing type, animal count, average weight, and grazing purpose");
log__activity__grazing.setConstant({
    attribute:"status",
    value:"done"
});

// Animal Weight
////overlays and constants
let quantity__standard__animal_weight = new builder.SchemaOverlay({
    typeAndBundle: "quantity--standard",
    name: "animal_weight",
});
quantity__standard__animal_weight.setMainDescription("Animal weight is an average weight of the herd involved in the grazing event. The measure attribute is set to weight.");
quantity__standard__animal_weight.setConstant({
    attribute: "measure",
    value:"weight"
});

//Weight unit
////overlays and constants
let taxonomy_term__unit__weight = new builder.SchemaOverlay({
    typeAndBundle: "taxonomy_term--unit",
    name: "weight",
});
//LOOK from taxonomy list in surveystack beta management question 22, list needs to be updated
taxonomy_term__unit__weight.setEnum({
    attribute: "name",
    valuesArray: [
        "lbs"
    ],
    description: "Units used to measure the average weight of the grazing herd."
});
taxonomy_term__unit__weight.setMainDescription("Units in kg or lb.");

//quantity - count LOOK do we need units for these quantities? they are just numbers so I would assume not
//Animal Count
////overlays and constants
let quantity__standard__animal_count = new builder.SchemaOverlay({
    typeAndBundle: "quantity--standard",
    name: "animal_count",
});
quantity__standard__animal_count.setMainDescription("Animal count refers to the number of animals involved in this grazing event.");
quantity__standard__animal_count.setConstant({
    attribute: "measure",
    value: "count"
});

//quantity - sub-paddocks
let quantity__standard__subpaddocks = new builder.SchemaOverlay({
    typeAndBundle: "quantity--standard",
    name: "subpaddocks",
});
quantity__standard__subpaddocks.setMainDescription("The number of subpaddocks or sections the grazed field is split into.");
quantity__standard__subpaddocks.setConstant({
    attribute: "measure",
    value:"count"
});

//convention
let grazingConvention = new builder.ConventionSchema({
    title: "Grazing log",
    version: "0.0.2",
    schemaName:"log--activity--grazing",
    repoURL:"www.gitlabrepo.com/version/farmos_conventions",
    //LOOK set description
    description:`## Purpose\n
Grazing logs can be created for planting termination or for tracking general herd managment activities.\n
## Specification\n
text\n`,
});

//add local attributes
grazingConvention.addAttribute( { schemaOverlayObject:log__activity__grazing, attributeName: "log--activity--grazing", required: true } );
grazingConvention.addAttribute( { schemaOverlayObject:quantity__standard__animal_count, attributeName: "quantity--standard--animal_count", required: false } );
grazingConvention.addAttribute( { schemaOverlayObject:quantity__standard__animal_weight, attributeName: "quantity--standard--animal_weight", required: false } );
grazingConvention.addAttribute( { schemaOverlayObject:taxonomy_term__unit__weight, attributeName: "taxonomy_term--unit--weight", required: false } );
grazingConvention.addAttribute( { schemaOverlayObject:quantity__standard__subpaddocks, attributeName: "quantity--standard--subpaddocks", required: false } );

//add global attributes
grazingConvention.addAttribute( { schemaOverlayObject:"taxonomy_term--log_category", attributeName: "taxonomy_term--log_category", required: false } );
grazingConvention.addAttribute({ schemaOverlayObject:"taxonomy_term--log_category--grazing", attributeName: "taxonomy_term--log_category--grazing", required: true  });
grazingConvention.addAttribute({ schemaOverlayObject:"quantity--standard--area_percentage", attributeName: "quantity--standard--area_percentage", required: false});
grazingConvention.addAttribute({ schemaOverlayObject:"taxonomy_term--unit--%", attributeName: "taxonomy_term--unit--%", required: false});
grazingConvention.addAttribute( { schemaOverlayObject:"asset--plant--planting", attributeName: "asset--plant--planting", required: false } );
grazingConvention.addAttribute( { schemaOverlayObject:"asset--land", attributeName: "asset--land", required: false } );

//relationships
grazingConvention.addRelationship( { containerEntity:"log--activity--grazing" , relationName:"category" , mentionedEntity:"taxonomy_term--log_category" , required: false } );
grazingConvention.addRelationship( { containerEntity:"log--activity--grazing" , relationName:"category" , mentionedEntity:"taxonomy_term--log_category--grazing" , required: true } );
grazingConvention.addRelationship( { containerEntity:"log--activity--grazing" , relationName:"quantity" , mentionedEntity:"quantity--standard--area_percentage" , required: false } );
grazingConvention.addRelationship( { containerEntity:"quantity--standard--area_percentage" , relationName:"units" , mentionedEntity:"taxonomy_term--unit--%" , required: false } );
grazingConvention.addRelationship( { containerEntity:"log--activity--grazing" , relationName:"quantity" , mentionedEntity:"quantity--standard--animal_count" , required: false } );
grazingConvention.addRelationship( { containerEntity:"log--activity--grazing" , relationName:"quantity" , mentionedEntity:"quantity--standard--animal_weight" , required: false } );
grazingConvention.addRelationship( { containerEntity:"log--activity--grazing" , relationName:"quantity" , mentionedEntity:"quantity--standard--subpaddocks" , required: false } );
grazingConvention.addRelationship( { containerEntity:"log--activity--grazing", relationName:"asset", mentionedEntity:"asset--plant--planting", required: false } );
grazingConvention.addRelationship( { containerEntity:"quantity--standard--animal_weight" , relationName:"units" , mentionedEntity:"taxonomy_term--unit--weight" , required: false } );
grazingConvention.addRelationship( { containerEntity:"log--activity--grazing", relationName:"location", mentionedEntity:"asset--land", required: false } );

grazingConvention.addExample({ path: `${__dirname}/examples/correct`, is_valid_example: true });
grazingConvention.addExample({ path: `${__dirname}/examples/incorrect`, is_valid_example: false });

// let test = grazingConvention.testExamples();
let storageOperation = grazingConvention.store();
