// Survey: https://app.surveystack.io/surveys/6346c1e467f839000121503e/edit
// Section: 39, Lime
const builder = require('convention_builder');
const fs = require('fs');
require('dotenv').config({ path: `${__dirname}/../../.env` });

//Main entity
////Overlays and constants
let log__input__lime = new builder.SchemaOverlay({
    typeAndBundle: 'log--input',
    name: 'lime',
});

//LOOK add description
log__input__lime.setMainDescription("Lime logs are used to record a lime application.");

log__input__lime.setConstant({
    attribute: "status",
    value: "done"
});

//lime quantity
////Overlays and constants
let quantity__material__lime = new builder.SchemaOverlay({
    typeAndBundle: "quantity--material",
    name: "lime",
});

//LOOK set description
quantity__material__lime.setMainDescription("This represents the total quantity of lime applied. This quantity is always pushed even if empty in the survey.");
//LOOK what should the measure be here?
quantity__material__lime.setConstant({
    attribute: "measure",
    value: "value"
});
quantity__material__lime.setConstant({
    attribute: "label",
    value: "lime"
});

//Lime quantity units
////Overlays and constants
let taxonomy_term__unit__lime = new builder.SchemaOverlay({
    typeAndBundle: "taxonomy_term--unit",
    name: "lime",
});
taxonomy_term__unit__lime.setEnum({
    attribute: "name",
    valuesArray: [
        "lbs_acres",
        "tons_acre",
        "kg_ha",
        "tons_ha"
    ],
});
taxonomy_term__unit__lime.setMainDescription("Lime units are recorded in weight per area.");

let taxonomy_term__material_type__lime = new builder.SchemaOverlay({
    typeAndBundle: "taxonomy_term--material_type",
    name: "lime",
});
taxonomy_term__material_type__lime.setMainDescription("A quantity name must be set to lime.");
taxonomy_term__material_type__lime.setConstant({
    attribute: "name",
    value:"lime"
});

let taxonomy_term__material_type__lime_specific = new builder.SchemaOverlay({
    typeAndBundle: "taxonomy_term--material_type",
    name: "lime_specific",
});
taxonomy_term__material_type__lime_specific.setMainDescription("The type or brand of lime applied is stored here.");

taxonomy_term__material_type__lime_specific.setEnum({
    attribute: "name",
    valuesArray: [
        "calcitic_lime",
        "crushed_lime",
        "dolomitic_lime",
        "high_cal",
        "nutralime_op_hi_cal",
        "nutralime_op_hi_mag",
        "pelletized_lime",
        "supercal_98g"
    ],
    description: "The type or brand name of the lime applied. SurveyStack has an ontology list of these types."
});

//Convention
let limeConvention = new builder.ConventionSchema({
    title: "Lime",
    version: "0.0.1",
    schemaName:"log--input--lime",
    repoURL:"www.gitlabrepo.com/version/farmos_conventions",
    //LOOK add description
    description:`## Purpose\n
Lime logs are created to record a lime input to a field or planting.\n
## Specification\n
text\n`,
});

//add local attributes
limeConvention.addAttribute( { schemaOverlayObject:log__input__lime, attributeName: "log--input--lime", required: true } );
limeConvention.addAttribute( { schemaOverlayObject:quantity__material__lime, attributeName: "quantity--material--lime", required: false } );
limeConvention.addAttribute( { schemaOverlayObject:taxonomy_term__unit__lime, attributeName: "taxonomy_term--unit--lime", required: false } );
limeConvention.addAttribute( { schemaOverlayObject: taxonomy_term__material_type__lime, attributeName: "taxonomy_term--material_type--lime", required: true});
limeConvention.addAttribute( { schemaOverlayObject: taxonomy_term__material_type__lime_specific, attributeName: "taxonomy_term--material_type--lime_specific", required: false});

//add global attributes
limeConvention.addAttribute( { schemaOverlayObject:"taxonomy_term--log_category", attributeName: "taxonomy_term--log_category", required: false } );
limeConvention.addAttribute( { schemaOverlayObject:"asset--plant--planting", attributeName: "asset--plant--planting", required: false } );
limeConvention.addAttribute({ schemaOverlayObject:"quantity--standard--area_percentage", attributeName: "quantity--standard--area_percentage", required: false});
limeConvention.addAttribute({ schemaOverlayObject:"taxonomy_term--unit--%", attributeName: "taxonomy_term--unit--%", required: false});
limeConvention.addAttribute( { schemaOverlayObject:"taxonomy_term--log_category--amendment", attributeName: "taxonomy_term--log_category--amendment", required: false } );
limeConvention.addAttribute( { schemaOverlayObject:"asset--land", attributeName: "asset--land", required: false } );

//add relationships
limeConvention.addRelationship( { containerEntity:"log--input--lime" , relationName:"category" , mentionedEntity:"taxonomy_term--log_category" , required: false } );
limeConvention.addRelationship( { containerEntity:"log--input--lime", relationName:"asset", mentionedEntity:"asset--plant--planting", required: false } );
limeConvention.addRelationship( { containerEntity:"log--input--lime" , relationName:"quantity" , mentionedEntity:"quantity--standard--area_percentage" , required: false } );
limeConvention.addRelationship( { containerEntity:"quantity--standard--area_percentage" , relationName:"units" , mentionedEntity:"taxonomy_term--unit--%" , required: false } );
limeConvention.addRelationship( { containerEntity:"log--input--lime" , relationName:"quantity" , mentionedEntity:"quantity--material--lime" , required: false } );
limeConvention.addRelationship( { containerEntity:"quantity--material--lime" , relationName:"units" , mentionedEntity:"taxonomy_term--unit--lime" , required: false } );
limeConvention.addRelationship( { containerEntity:"log--input--lime" , relationName:"category" , mentionedEntity:"taxonomy_term--log_category--amendment" , required: true } );
limeConvention.addRelationship( { containerEntity:"quantity--material--lime", relationName:"material_type" , mentionedEntity:"taxonomy_term--material_type--lime" , required: true } );
limeConvention.addRelationship( { containerEntity:"quantity--material--lime", relationName:"material_type" , mentionedEntity:"taxonomy_term--material_type--lime_specific" , required: false } );
limeConvention.addRelationship( { containerEntity:"log--input--lime" , relationName:"location" , mentionedEntity:"asset--land" , required: false } );

limeConvention.addExample({ path: `${__dirname}/examples/correct`, is_valid_example: true });
limeConvention.addExample({ path: `${__dirname}/examples/incorrect`, is_valid_example: false });

let storageOperation = limeConvention.store();
