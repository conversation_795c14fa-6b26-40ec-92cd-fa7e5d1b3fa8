// Survey: https://app.surveystack.io/surveys/6346c1e467f839000121503e/edit
// Section: 11, planting
const builder = require('convention_builder');
const fs = require('fs');
require('dotenv').config({ path: `${__dirname}/../../.env` });

//Main entity
////overlays and constants
let log__harvest__harvest = new builder.SchemaOverlay({
    typeAndBundle: 'log--harvest',
    name: 'harvest',
});
//LOOK review description
log__harvest__harvest.setMainDescription("Harvest logs are used to record harvest of a plant asset.");
log__harvest__harvest.setConstant({
    attribute: "status",
    value: "done"
});

/// Yield Quantity
//yield could be a material or a standard quantity, if material require product type
////overlays and constants
let quantity__standard__yield = new builder.SchemaOverlay({
    typeAndBundle: "quantity--standard",
    name: "yield",
});
quantity__standard__yield.setMainDescription("Yield quantity from a plant asset.");


////overlays and constants
let quantity__material__yield = new builder.SchemaOverlay({
    typeAndBundle: "quantity--material",
    name: "yield",
});
quantity__material__yield.setMainDescription("Yield quantity from a plant asset.");

//yield units
////overlays and constants
let taxonomy_term__unit__yield = new builder.SchemaOverlay({
    typeAndBundle: "taxonomy_term--unit",
    name: "yield",
});
taxonomy_term__unit__yield.setMainDescription("");

let yieldUnitsTaxonomy = JSON.parse(fs.readFileSync(`${__dirname}/yield_units_taxonomy.json`));

taxonomy_term__unit__yield.setEnum({
    attribute: "name",
    valuesArray: yieldUnitsTaxonomy,
    description: "Several units are available, both imperial and metric."
});


//residue removed quantity with % units
//residue removed quantity with % units
////overlays and constants
let quantity__standard__residue_removed = new builder.SchemaOverlay({
    typeAndBundle: "quantity--standard",
    name: "residue_removed",
});
quantity__standard__residue_removed.setMainDescription("Residue removed when a plant asset is harvested.");

//any log category is acceptable

//Convention
// Object
let harvestConvention = new builder.ConventionSchema({
    title: "Harvest",
    version: "0.0.2",
    schemaName:"log--harvest--harvest",
    repoURL:"www.gitlabrepo.com/version/farmos_conventions",
    description:`## Purpose\n
Harvest log to be applied to a plant asset.\n
## Specification\n
text\n`,
});

////add local attributes
harvestConvention.addAttribute( { schemaOverlayObject:log__harvest__harvest, attributeName: "log--harvest--harvest", required: true } );
harvestConvention.addAttribute( { schemaOverlayObject:quantity__standard__yield, attributeName: "quantity--standard--yield", required: false } );
harvestConvention.addAttribute( { schemaOverlayObject:quantity__material__yield, attributeName: "quantity--material--yield", required: false } );
harvestConvention.addAttribute( { schemaOverlayObject:quantity__standard__residue_removed, attributeName: "quantity--standard--residue_removed", required: false } );
harvestConvention.addAttribute( { schemaOverlayObject:taxonomy_term__unit__yield, attributeName: "taxonomy_term--unit--yield", required: true } );

//add global attributes
harvestConvention.addAttribute( { schemaOverlayObject:"taxonomy_term--log_category", attributeName: "taxonomy_term--log_category", required: true } );
harvestConvention.addAttribute( { schemaOverlayObject:"asset--plant--planting", attributeName: "asset--plant--planting", required: false } );
harvestConvention.addAttribute( { schemaOverlayObject: "quantity--standard--area_percentage", attributeName: "quantity--standard--area_percentage", required: false});
harvestConvention.addAttribute( { schemaOverlayObject: "taxonomy_term--unit--%", attributeName: "taxonomy_term--unit--%", required: false});
harvestConvention.addAttribute( { schemaOverlayObject: "asset--land", attributeName: "asset--land", required: false});

////add relationships
harvestConvention.addRelationship( { containerEntity:"log--harvest--harvest" , relationName:"category" , mentionedEntity:"taxonomy_term--log_category" , required: true } );
harvestConvention.addRelationship( { containerEntity:"log--harvest--harvest", relationName:"asset", mentionedEntity:"asset--plant--planting", required: false } );
harvestConvention.addRelationship( { containerEntity:"log--harvest--harvest" , relationName:"quantity" , mentionedEntity:"quantity--standard--area_percentage" , required: false } );
harvestConvention.addRelationship( { containerEntity:"quantity--standard--area_percentage" , relationName:"units" , mentionedEntity:"taxonomy_term--unit--%" , required: false } );
harvestConvention.addRelationship( { containerEntity:"log--harvest--harvest" , relationName:"quantity" , mentionedEntity:"quantity--standard--yield" , required: false } );
harvestConvention.addRelationship( { containerEntity:"log--harvest--harvest" , relationName:"quantity" , mentionedEntity:"quantity--material--yield" , required: false } );
harvestConvention.addRelationship( { containerEntity:"log--harvest--harvest" , relationName:"quantity" , mentionedEntity:"quantity--standard--residue_removed" , required: false } );
harvestConvention.addRelationship( { containerEntity:"quantity--standard--yield" , relationName:"units" , mentionedEntity:"taxonomy_term--unit--yield" , required: false } );
harvestConvention.addRelationship( { containerEntity:"quantity--material--yield" , relationName:"units" , mentionedEntity:"taxonomy_term--unit--yield" , required: false } );
harvestConvention.addRelationship( { containerEntity:"quantity--standard--residue_removed" , relationName:"units" , mentionedEntity:"taxonomy_term--unit--%" , required: false } );
harvestConvention.addRelationship( { containerEntity:"log--harvest--harvest" , relationName:"location" , mentionedEntity:"asset--land" , required: false } );

harvestConvention.addExample({ path: `${__dirname}/examples/correct`, is_valid_example: true });
harvestConvention.addExample({ path: `${__dirname}/examples/incorrect`, is_valid_example: false });


// let test = harvestConvention.testExamples();
let storageOperation = harvestConvention.store();
