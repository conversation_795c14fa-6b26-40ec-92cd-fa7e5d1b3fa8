{"id": "f655161b-5191-470f-9b5e-b3a2b5e86929", "type": "Object", "convention_taxonomy": {"id": "c50cd4ce-e9f7-4bdd-b28e-d173efc0834b", "attributes": {"name": "Seedling Treatment", "external_uri": "https://gitlab.com/api/v4/projects/44311021/jobs/artifacts/main/raw/output/collection/conventions/log--input--seedling_treatment/schema.json?job=copy_schemas"}}, "asset--plant--planting": {"id": "9c8a5885-256f-4b4b-903c-059111f5460f", "attributes": {"name": "example div veg plant asset", "status": "active"}}, "log--input--seedling_treatment": {"id": "53ae9c40-bfc4-4825-be70-42945a243ece", "attributes": {"name": "example seedling treatment log", "status": "done", "timestamp": "2025-05-25T01:21:57.506Z"}, "relationships": {"convention": {"data": [{"type": "taxonomy_term--convention", "id": "c50cd4ce-e9f7-4bdd-b28e-d173efc0834b"}]}, "quantity": {"data": [{"type": "quantity--standard", "id": "a03b3686-6573-47dd-a5ca-b152b46b2c4a"}]}, "asset": {"data": [{"type": "asset--plant", "id": "9c8a5885-256f-4b4b-903c-059111f5460f"}]}, "category": {"data": [{"type": "taxonomy_term--log_category", "id": "6e6a891d-9ae7-49b3-bb50-ec0f5eb5fab0"}]}}}, "taxonomy_term--unit--%": {"id": "85355ad3-a78f-43c1-a4b1-430b33320b5f", "attributes": {"name": "%"}}}