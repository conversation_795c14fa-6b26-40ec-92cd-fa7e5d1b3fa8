// Survey: https://app.surveystack.io/surveys/6346c1e467f839000121503e/edit
// Section: 11, planting_div_veg
const builder = require('convention_builder');
const fs = require('fs');
require('dotenv').config({ path: `${__dirname}/../../.env` });

//Main entity
let log__input__seedling_treatment = new builder.SchemaOverlay({
    typeAndBundle: 'log--input',
    name: 'seedling_treatment',
});
log__input__seedling_treatment.setMainDescription("This adds a seedling treatment that records the application of fungicide, insecticide, or a combination of both if one is indicated as used in the planting information.");

log__input__seedling_treatment.setConstant({
    attribute:"status",
    value:"done",
    description: "The status should always be set to done to inherit the area."
});

//Convention
let seedlingTreatmentConvention = new builder.ConventionSchema({
    title: "Seedling Treatment",
    version: "0.0.1",
    schemaName:"log--input--seedling_treatment",
    repoURL:"www.gitlabrepo.com/version/farmos_conventions",
    description:`## Purpose\n
A seedling treatment log is applied to a plant asset only if the farmer selected one.\n
## Specification\n
text\n`,
});

////add local attributes
seedlingTreatmentConvention.addAttribute( { schemaOverlayObject:log__input__seedling_treatment, attributeName: "log--input--seedling_treatment", required: true } );

//add global attributes
seedlingTreatmentConvention.addAttribute( { schemaOverlayObject:"taxonomy_term--log_category", attributeName: "taxonomy_term--log_category", required: false } );
seedlingTreatmentConvention.addAttribute( { schemaOverlayObject:"taxonomy_term--log_category--amendment", attributeName: "taxonomy_term--log_category--amendment", required: true } );
seedlingTreatmentConvention.addAttribute( { schemaOverlayObject:"asset--plant--planting", attributeName: "asset--plant--planting", required: true } );
seedlingTreatmentConvention.addAttribute( { schemaOverlayObject:"quantity--standard--area_percentage", attributeName: "quantity--standard--area_percentage", required: true } );
seedlingTreatmentConvention.addAttribute( { schemaOverlayObject:"taxonomy_term--unit--%", attributeName: "taxonomy_term--unit--%", required: true } );

////add relationships
seedlingTreatmentConvention.addRelationship( { containerEntity:"log--input--seedling_treatment" , relationName:"category" , mentionedEntity:"taxonomy_term--log_category" , required: false } );
seedlingTreatmentConvention.addRelationship({ containerEntity: "log--input--seedling_treatment", relationName:"category", mentionedEntity:"taxonomy_term--log_category--amendment", required: true});
seedlingTreatmentConvention.addRelationship( { containerEntity:"log--input--seedling_treatment", relationName:"asset", mentionedEntity:"asset--plant--planting", required: true } );
seedlingTreatmentConvention.addRelationship( { containerEntity:"log--input--seedling_treatment" , relationName:"quantity" , mentionedEntity:"quantity--standard--area_percentage" , required: true } );
seedlingTreatmentConvention.addRelationship( { containerEntity:"quantity--standard--area_percentage" , relationName:"units" , mentionedEntity:"taxonomy_term--unit--%" , required: true } );



seedlingTreatmentConvention.addExample({ path: `${__dirname}/examples/correct`, is_valid_example: true });
seedlingTreatmentConvention.addExample({ path: `${__dirname}/examples/incorrect`, is_valid_example: false });

let storageOperation = seedlingTreatmentConvention.store();
