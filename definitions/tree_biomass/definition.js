const builder = require('convention_builder');
const fs = require('fs');
require('dotenv').config({ path: `${__dirname}/../../.env` });

//Main entity

////overlay
let log__observation__tree_biomass = new builder.SchemaOverlay({
    typeAndBundle: 'log--observation',
    name: 'tree_biomass',
});
//LOOK review description
log__observation__tree_biomass.setMainDescription("Add annual changes for the biomass of trees growing within or immediately adjacent to the field assessment area. Ensure these are not counted in adjoining crop assessments to prevent double counting. As per GHG Protocol, you should only account for increased biomass if the biomass is permanent and additional. Trees or orchards that have been there > 20 years or the lifecycle of the perennial crop should not be included. If unsure, be conservative and do not include.");

log__observation__tree_biomass.setConstant({
    attribute:"status",
    value:"done"
});

////overlays and constants
let quantity__standard__density_last_year = new builder.SchemaOverlay({
    typeAndBundle: "quantity--standard",
    name: "density_last_year",
});
quantity__standard__density_last_year.setMainDescription("Average density of trees per acre/hectare of your assessment area.");


////overlays and constants
let quantity__standard__size_last_year = new builder.SchemaOverlay({
    typeAndBundle: "quantity--standard",
    name: "size_last_year",
});
quantity__standard__size_last_year.setMainDescription("Diameter at chest height this year.");

////overlays and constants
let quantity__standard__size_this_year = new builder.SchemaOverlay({
    typeAndBundle: "quantity--standard",
    name: "size_this_year",
});
quantity__standard__size_this_year.setMainDescription("Diameter at chest height this year.");


//taxonomy term for quantity standard size
////overlays and constants
let taxonomy_term__unit__tree_size = new builder.SchemaOverlay({
    typeAndBundle: "taxonomy_term--unit",
    name: "tree_size",
});
taxonomy_term__unit__tree_size.setMainDescription("");

taxonomy_term__unit__tree_size.setEnum({
    attribute: "name",
    valuesArray: [
        "meters",
        "centimeters",
        "inches",
        "feet",
        "millimeters"
    ],
    description: "Several units are available, both imperial and metric."
});


////overlays and constants
let quantity__standard__delta_trees = new builder.SchemaOverlay({
    typeAndBundle: "quantity--standard",
    name: "delta_trees",
});
quantity__standard__delta_trees.setMainDescription("Net change in trees over the past year.");


//taxonomy term for quantity standard size
////overlays and constants
let taxonomy_term__unit__tree_density = new builder.SchemaOverlay({
    typeAndBundle: "taxonomy_term--unit",
    name: "tree_density",
});
taxonomy_term__unit__tree_density.setMainDescription("");

taxonomy_term__unit__tree_density.setEnum({
    attribute: "name",
    valuesArray: [
        "trees/hectare",
        "trees/acre"
    ],
    description: "Trees per unit of area."
});

//convention
let treeBiomassConvention = new builder.ConventionSchema({
    title: "Tree Biomass",
    version: "0.0.1",
    schemaName:"log--observation--tree_biomass",
    repoURL:"www.gitlabrepo.com/version/farmos_conventions",
    description:`## Purpose\n
Cool Farm Tool tree biomass convention.\n
## Specification\n
text\n`,
});

//add local attributes
treeBiomassConvention.addAttribute( { schemaOverlayObject:log__observation__tree_biomass, attributeName: "log--observation--tree_biomass", required: true } );
treeBiomassConvention.addAttribute( { schemaOverlayObject:quantity__standard__size_last_year, attributeName: "quantity--standard--size_last_year", required: true } );
treeBiomassConvention.addAttribute( { schemaOverlayObject:quantity__standard__density_last_year, attributeName: "quantity--standard--density_last_year", required: true } );
treeBiomassConvention.addAttribute( { schemaOverlayObject:quantity__standard__size_this_year, attributeName: "quantity--standard--size_this_year", required: true } );
treeBiomassConvention.addAttribute( { schemaOverlayObject:quantity__standard__delta_trees, attributeName: "quantity--standard--delta_trees", required: true } );
treeBiomassConvention.addAttribute( { schemaOverlayObject:taxonomy_term__unit__tree_size, attributeName: "taxonomy_term--unit--tree_size", required: true } );
treeBiomassConvention.addAttribute( { schemaOverlayObject:taxonomy_term__unit__tree_density, attributeName: "taxonomy_term--unit--tree_density", required: true } );

//add global attributes
treeBiomassConvention.addAttribute( { schemaOverlayObject:"taxonomy_term--log_category", attributeName: "taxonomy_term--log_category", required: false } );
treeBiomassConvention.addAttribute({ schemaOverlayObject:"quantity--standard--area_percentage", attributeName: "quantity--standard--area_percentage", required: false});
treeBiomassConvention.addAttribute({ schemaOverlayObject:"taxonomy_term--unit--%", attributeName: "taxonomy_term--unit--%", required: false});
treeBiomassConvention.addAttribute( { schemaOverlayObject:"asset--plant--planting", attributeName: "asset--plant--planting", required: false } );

//relationships
treeBiomassConvention.addRelationship( { containerEntity:"log--observation--tree_biomass" , relationName:"category" , mentionedEntity:"taxonomy_term--log_category" , required: false } );
treeBiomassConvention.addRelationship( { containerEntity:"log--observation--tree_biomass" , relationName:"quantity" , mentionedEntity:"quantity--standard--size_last_year" , required: true } );
treeBiomassConvention.addRelationship( { containerEntity:"log--observation--tree_biomass" , relationName:"quantity" , mentionedEntity:"quantity--standard--density_last_year" , required: true } );
treeBiomassConvention.addRelationship( { containerEntity:"log--observation--tree_biomass" , relationName:"quantity" , mentionedEntity:"quantity--standard--size_this_year" , required: true } );
treeBiomassConvention.addRelationship( { containerEntity:"log--observation--tree_biomass" , relationName:"quantity" , mentionedEntity:"quantity--standard--delta_trees" , required: true } );
treeBiomassConvention.addRelationship( { containerEntity:"log--observation--tree_biomass" , relationName:"quantity" , mentionedEntity:"quantity--standard--area_percentage" , required: false } );
treeBiomassConvention.addRelationship( { containerEntity:"quantity--standard--area_percentage" , relationName:"units" , mentionedEntity:"taxonomy_term--unit--%" , required: false } );
treeBiomassConvention.addRelationship( { containerEntity:"log--observation--tree_biomass", relationName:"asset", mentionedEntity:"asset--plant--planting", required: true } );
treeBiomassConvention.addRelationship( { containerEntity:"quantity--standard--size_last_year" , relationName:"units" , mentionedEntity:"taxonomy_term--unit--tree_size" , required: true } );
treeBiomassConvention.addRelationship( { containerEntity:"quantity--standard--density_last_year" , relationName:"units" , mentionedEntity:"taxonomy_term--unit--tree_density" , required: false } );
treeBiomassConvention.addRelationship( { containerEntity:"quantity--standard--size_this_year" , relationName:"units" , mentionedEntity:"taxonomy_term--unit--tree_size" , required: true } );
treeBiomassConvention.addRelationship( { containerEntity:"quantity--standard--delta_trees" , relationName:"units" , mentionedEntity:"taxonomy_term--unit--tree_density" , required: false } );

treeBiomassConvention.addExample({ path: `${__dirname}/examples/correct`, is_valid_example: true });
treeBiomassConvention.addExample({ path: `${__dirname}/examples/incorrect`, is_valid_example: false });

let storageOperation = treeBiomassConvention.store();
