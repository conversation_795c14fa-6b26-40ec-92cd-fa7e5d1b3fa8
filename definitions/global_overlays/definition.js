const builder = require('convention_builder');
const { randomUUID } = require('crypto');
const fs = require('fs');


//taxonomy_term--log_category--grazing
let taxonomy_term__log_category__grazing_uuid = randomUUID();
let taxonomy_term__log_category__grazing_example = {
    id: taxonomy_term__log_category__grazing_uuid,
    attributes: { name: "grazing" }
};
let taxonomy_term__log_category__grazing_error = {
    id: taxonomy_term__log_category__grazing_uuid,
    attributes: { label: "seeding" }
};
let taxonomy_term__log_category__grazing = new builder.SchemaOverlay({
    typeAndBundle: "taxonomy_term--log_category",
    name: "grazing",
    validExamples: [taxonomy_term__log_category__grazing_example],
    erroredExamples: [taxonomy_term__log_category__grazing_error]
});
taxonomy_term__log_category__grazing.setMainDescription("Log category is set to grazing.");
taxonomy_term__log_category__grazing.setConstant({ attribute: "name", value: "grazing" });
taxonomy_term__log_category__grazing.store();


//taxonomy_term--log_category--weed_control
let taxonomy_term__log_category__weed_control_uuid = randomUUID();
let taxonomy_term__log_category__weed_control_example = {
    id: taxonomy_term__log_category__weed_control_uuid,
    attributes: { name: "weed_control" }
};
let taxonomy_term__log_category__weed_control_error = {
    id: taxonomy_term__log_category__weed_control_uuid,
    attributes: { label: "seeding" }
};
let taxonomy_term__log_category__weed_control = new builder.SchemaOverlay({
    typeAndBundle: "taxonomy_term--log_category",
    name: "weed_control",
    validExamples: [taxonomy_term__log_category__weed_control_example],
    erroredExamples: [taxonomy_term__log_category__weed_control_error]
});
taxonomy_term__log_category__weed_control.setMainDescription("Log category is set to weed_control.");
taxonomy_term__log_category__weed_control.setConstant({ attribute: "name", value: "weed_control" });
taxonomy_term__log_category__weed_control.store();


//taxonomy_term--log_category--irrigation
let taxonomy_term__log_category__irrigation_uuid = randomUUID();
let taxonomy_term__log_category__irrigation_example = {
    id: taxonomy_term__log_category__irrigation_uuid,
    attributes: { name: "irrigation" }
};
let taxonomy_term__log_category__irrigation_error = {
    id: taxonomy_term__log_category__irrigation_uuid,
    attributes: { name: "activity" }
};
let taxonomy_term__log_category__irrigation = new builder.SchemaOverlay({
    typeAndBundle: "taxonomy_term--log_category",
    name: "irrigation",
    validExamples: [taxonomy_term__log_category__irrigation_example],
    erroredExamples: [taxonomy_term__log_category__irrigation_error]
});
taxonomy_term__log_category__irrigation.setMainDescription("Log category is set to irrigation.");
taxonomy_term__log_category__irrigation.setConstant({ attribute: "name", value: "irrigation" });
taxonomy_term__log_category__irrigation.store();

//log_category--amendment
let taxonomy_term__log_category__amendment_uuid = randomUUID();
let taxonomy_term__log_category__amendment_example = {
    id: taxonomy_term__log_category__amendment_uuid,
    attributes: { name: "amendment" }
};
let taxonomy_term__log_category__amendment_error = {
    id: taxonomy_term__log_category__amendment_uuid,
    attributes: { name: "seeding" }
};
let taxonomy_term__log_category__amendment = new builder.SchemaOverlay({
    typeAndBundle: "taxonomy_term--log_category",
    name: "amendment",
    validExamples: [taxonomy_term__log_category__amendment_example],
    erroredExamples: [taxonomy_term__log_category__amendment_error]
});
taxonomy_term__log_category__amendment.setMainDescription("Log category is set to amendment.");
taxonomy_term__log_category__amendment.setConstant({ attribute: "name", value: "amendment" });
taxonomy_term__log_category__amendment.store();


//taxonomy_term--log_category--pest_disease_control
let taxonomy_term__log_category__pest_disease_control_uuid = randomUUID();
let taxonomy_term__log_category__pest_disease_control_example = {
    id: taxonomy_term__log_category__pest_disease_control_uuid,
    attributes: { name: "pest_disease_control" }
};
let taxonomy_term__log_category__pest_disease_control_error = {
    id: taxonomy_term__log_category__pest_disease_control_uuid,
    attributes: { name: "activity" }
};
let taxonomy_term__log_category__pest_disease_control = new builder.SchemaOverlay({
    typeAndBundle: "taxonomy_term--log_category",
    name: "pest_disease_control",
    validExamples: [taxonomy_term__log_category__pest_disease_control_example],
    erroredExamples: [taxonomy_term__log_category__pest_disease_control_error]
});
taxonomy_term__log_category__pest_disease_control.setMainDescription("The log category is set to pest disease control and/or weed control.");
taxonomy_term__log_category__pest_disease_control.setConstant({ attribute: "name", value: "pest_disease_control" });
taxonomy_term__log_category__pest_disease_control.store();

//taxonomy term - log_category--seeding
let taxonomy_term__log_category__seeding_uuid = randomUUID();
let taxonomy_term__log_category__seeding_example = {
    id: taxonomy_term__log_category__seeding_uuid,
    attributes: { name: "seeding" }
};
let taxonomy_term__log_category__seeding_error = {
    id: taxonomy_term__log_category__seeding_uuid,
    attributes: { name: "activity" }
};
let taxonomy_term__log_category__seeding = new builder.SchemaOverlay({
    typeAndBundle: "taxonomy_term--log_category",
    name: "seeding",
    validExamples: [taxonomy_term__log_category__seeding_example],
    erroredExamples: [taxonomy_term__log_category__seeding_error]
});
taxonomy_term__log_category__seeding.setMainDescription("The log category is set to seeding.");
taxonomy_term__log_category__seeding.setConstant({ attribute: "name", value: "seeding" });
taxonomy_term__log_category__seeding.store();


//taxonomy_term--log_category--transplanting
let taxonomy_term__log_category__transplanting_uuid = randomUUID();
let taxonomy_term__log_category__transplanting_example = {
    id: taxonomy_term__log_category__transplanting_uuid,
    attributes: { name: "transplanting" }
};
let taxonomy_term__log_category__transplanting_error = {
    id: taxonomy_term__log_category__transplanting_uuid,
    attributes: { label: "activity" }
};
let taxonomy_term__log_category__transplanting = new builder.SchemaOverlay({
    typeAndBundle: "taxonomy_term--log_category",
    name: "transplanting",
    validExamples: [taxonomy_term__log_category__transplanting_example],
    erroredExamples: [taxonomy_term__log_category__transplanting_error]
});
taxonomy_term__log_category__transplanting.setMainDescription("Log category is set to transplanting.");
taxonomy_term__log_category__transplanting.setConstant({ attribute: "name", value: "transplanting" });
taxonomy_term__log_category__transplanting.store();

//taxonomy_term--log_category--termination
let taxonomy_term__log_category__termination_uuid = randomUUID();
let taxonomy_term__log_category__termination_example = {
    id: taxonomy_term__log_category__termination_uuid,
    attributes: { name: "termination" }
};
let taxonomy_term__log_category__termination_error = {
    id: taxonomy_term__log_category__termination_uuid,
    attributes: { name: "done" }
};
let taxonomy_term__log_category__termination = new builder.SchemaOverlay({
    typeAndBundle: "taxonomy_term--log_category",
    name: "termination",
    validExamples: [taxonomy_term__log_category__termination_example],
    erroredExamples: [taxonomy_term__log_category__termination_error]
});
taxonomy_term__log_category__termination.setMainDescription("Log category is set to termination.");
taxonomy_term__log_category__termination.setConstant({ attribute: "name", value: "termination" });
taxonomy_term__log_category__termination.store();

//quantity--material--amount
//generic material quantity
let quantity__material__amount_uuid = randomUUID();
let quantity__material__amount_example = {
    id: quantity__material__amount_uuid,
    attributes: { label: "amount" }
};
let quantity__material__amount_error = {
    id: quantity__material__amount_uuid,
    attributes: { label: null }
};
////overlays and constants
let quantity__material__amount = new builder.SchemaOverlay({
    typeAndBundle: "quantity--material",
    name: "amount",
    validExamples: [ quantity__material__amount_example ],
    erroredExamples: [ quantity__material__amount_error ]
});
quantity__material__amount.setMainDescription("A material and optionally the amount applied");
quantity__material__amount.setRequiredAttribute("label");
quantity__material__amount.store();


//taxonomy_term__material_type__material
//generic material taxonomy term
let taxonomy_term__material_type__material_uuid = randomUUID();
let taxonomy_term__material_type__material_example = {
    id: taxonomy_term__material_type__material_uuid,
    attributes: {
        name: "material"
    }
};
let taxonomy_term__material_type__material_error = {
    id: taxonomy_term__material_type__material_uuid,
    attributes: {
        label: null
    }
};
let taxonomy_term__material_type__material = new builder.SchemaOverlay({
    typeAndBundle: "taxonomy_term--material_type",
    name: "material",
    validExamples: [taxonomy_term__material_type__material_example],
    erroredExamples: [taxonomy_term__material_type__material_error],
});
taxonomy_term__material_type__material.setMainDescription("A material associated with a quantity");
taxonomy_term__material_type__material.setRequiredAttribute("name");
taxonomy_term__material_type__material.store();

//quantity--material--pesticide
let quantity__material__pesticide_uuid = randomUUID();
let quantity__material__pesticide_example = {
    id: quantity__material__pesticide_uuid,
    attributes: {
        label: "pesticide",
    }
};
let quantity__material__pesticide_error = {
    id: quantity__material__pesticide_uuid,
    attributes: {
        label: "pesticide_specific_name",
    }
};
let quantity__material__pesticide = new builder.SchemaOverlay({
    typeAndBundle: "quantity--material",
    name: "pesticide",
    validExamples: [ quantity__material__pesticide_example ],
    erroredExamples: [quantity__material__pesticide_error]
});
quantity__material__pesticide.setMainDescription("The pesticide quantity indicates the type of pesticide and optionally the amount used either as a rate or a fixed quantity.");
quantity__material__pesticide.setConstant({
    attribute: "label",
    value: "pesticide"
});
quantity__material__pesticide.store();

//quantity--material--fertilizer
let quantity__material__fertilizer_uuid = randomUUID();
let quantity__material__fertilizer_example = {
    id: quantity__material__fertilizer_uuid,
    attributes: {
        label: "fertilizer",
    }
};
let quantity__material__fertilizer_error = {
    id: quantity__material__fertilizer_uuid,
    attributes: {
        label: "fertilizer_specific_name",
    }
};
let quantity__material__fertilizer = new builder.SchemaOverlay({
    typeAndBundle: "quantity--material",
    name: "fertilizer",
    validExamples: [ quantity__material__fertilizer_example ],
    erroredExamples: [quantity__material__fertilizer_error]
});
quantity__material__fertilizer.setMainDescription("The fertilizer quantity indicates the type of fertilizer and optionally the amount used either as a rate or a fixed quantity.");
quantity__material__fertilizer.setConstant({
    attribute: "label",
    value: "fertilizer"
});
quantity__material__fertilizer.store();

//moisture percentage
let quantity__standard__moisture_percentage_uuid = randomUUID();
let quantity__standard__moisture_percentage_example = {
    id: quantity__standard__moisture_percentage_uuid,
    attributes: { label: "moisture" }
};
let quantity__standard__moisture_percentage_error = {
    id: quantity__standard__moisture_percentage_uuid,
    attributes: { label: "water_content" }
};
let quantity__standard__moisture_percentage = new builder.SchemaOverlay({
    typeAndBundle: "quantity--standard",
    name: "moisture_percentage",
    validExamples: [quantity__standard__moisture_percentage_example],
    erroredExamples: [quantity__standard__moisture_percentage_error]
});
quantity__standard__moisture_percentage.setMainDescription("The percent moisture present in the organic matter material.");
quantity__standard__moisture_percentage.setConstant({ attribute: "label", value: "moisture" });
quantity__standard__moisture_percentage.store();

//nitrogen percentage
let quantity__material__nitrogen_percentage_uuid = randomUUID();
let quantity__material__nitrogen_percentage_example = {
    id: quantity__material__nitrogen_percentage_uuid,
    attributes: { label: "n", measure: "ratio" }
};
let quantity__material__nitrogen_percentage_error = {
    id: quantity__material__nitrogen_percentage_uuid,
    attributes: { label: "p", measure: "area" }
};
let quantity__material__nitrogen_percentage = new builder.SchemaOverlay({
    typeAndBundle: "quantity--material",
    name: "nitrogen_percentage",
    validExamples: [quantity__material__nitrogen_percentage_example],
    erroredExamples: [quantity__material__nitrogen_percentage_error]
});
quantity__material__nitrogen_percentage.setMainDescription("The percent of nitrogen present in the organic matter material.");
quantity__material__nitrogen_percentage.setConstant({ attribute: "measure", value: "ratio" });
quantity__material__nitrogen_percentage.setConstant({ attribute: "label", value: "n" });
quantity__material__nitrogen_percentage.store();

//phosphorus percentage
let quantity__material__phosphorus_percentage_uuid = randomUUID();
let quantity__material__phosphorus_percentage_example = {
    id: quantity__material__phosphorus_percentage_uuid,
    attributes: { label: "p", measure: "ratio" }
};
let quantity__material__phosphorus_percentage_error = {
    id: quantity__material__phosphorus_percentage_uuid,
    attributes: { label: "n", measure: "area" }
};
let quantity__material__phosphorus_percentage = new builder.SchemaOverlay({
    typeAndBundle: "quantity--material",
    name: "phosphorus_percentage",
    validExamples: [quantity__material__phosphorus_percentage_example],
    erroredExamples: [quantity__material__phosphorus_percentage_error]
});
quantity__material__phosphorus_percentage.setMainDescription("The percent of phosphorus present in the organic matter material.");
quantity__material__phosphorus_percentage.setConstant({ attribute: "measure", value: "ratio" });
quantity__material__phosphorus_percentage.setConstant({ attribute: "label", value: "p" });
quantity__material__phosphorus_percentage.store();

//potassium percentage
let quantity__material__potassium_percentage_uuid = randomUUID();
let quantity__material__potassium_percentage_example = {
    id: quantity__material__potassium_percentage_uuid,
    attributes: { label: "k", measure: "ratio" }
};
let quantity__material__potassium_percentage_error = {
    id: quantity__material__potassium_percentage_uuid,
    attributes: { label: "n", measure: "moisture" }
};
let quantity__material__potassium_percentage = new builder.SchemaOverlay({
    typeAndBundle: "quantity--material",
    name: "potassium_percentage",
    validExamples: [quantity__material__potassium_percentage_example],
    erroredExamples: [quantity__material__potassium_percentage_error]
});
quantity__material__potassium_percentage.setMainDescription("The percent of potassium present in the organic matter material.");
quantity__material__potassium_percentage.setConstant({ attribute: "measure", value: "ratio" });
quantity__material__potassium_percentage.setConstant({ attribute: "label", value: "k" });
quantity__material__potassium_percentage.store();

//plant asset
let asset__plant__planting_uuid = randomUUID();
let asset__plant__planting_example = {
    id: asset__plant__planting_uuid,
    attributes: { name: "example div veg plant asset", geometry: undefined, status: "active" }
};
let asset__plant__planting_error = {
    id: asset__plant__planting_uuid,
    attributes: { name: "example div veg plant asset", geometry: "[91.93934434,-40.345345]", status: "true" }
};
let asset__plant__planting = new builder.SchemaOverlay({
    typeAndBundle: 'asset--plant',
    name: 'planting',
    validExamples: [asset__plant__planting_example],
    erroredExamples: [asset__plant__planting_error]
});
asset__plant__planting.setMainDescription("The planting is the main asset that all management logs will reference.");
// asset__plant__planting.setConstant({ attribute: "status", value: "active" }); // need to allow for historical records for which plantings may no longer be active
asset__plant__planting.setConstant({ attribute: "geometry", value: undefined });
asset__plant__planting.store();

//area percentage
let quantity__standard__area_percentage_uuid = randomUUID();
let quantity__standard__area_percentage_example = {
    id: quantity__standard__area_percentage_uuid,
    attributes: { label: "area", measure: "area" }
};
let quantity__standard__area_percentage_error = {
    id: quantity__standard__area_percentage_uuid,
    attributes: { label: "percent", measure: "moisture" }
};
let quantity__standard__area_percentage = new builder.SchemaOverlay({
    typeAndBundle: "quantity--standard",
    name: "area_percentage",
    validExamples: [quantity__standard__area_percentage_example],
    erroredExamples: [quantity__standard__area_percentage_error]
});
quantity__standard__area_percentage.setMainDescription("Area is the percentage of the field that the seeding applies to.");
quantity__standard__area_percentage.setConstant({ attribute: "measure", value: "area" });
quantity__standard__area_percentage.setConstant({ attribute: "label", value: "area" });
quantity__standard__area_percentage.store();

//taxonomy_term--unit--%
let taxonomy_term__unit__percentage_uuid = randomUUID();
let taxonomy_term__unit__percentage_example = {
    id: taxonomy_term__unit__percentage_uuid,
    attributes: { name: "%" }
};
let taxonomy_term__unit__percentage_error = {
    id: taxonomy_term__unit__percentage_uuid,
    attributes: { name: "acres" }
};
let taxonomy_term__unit__percentage = new builder.SchemaOverlay({
    typeAndBundle: "taxonomy_term--unit",
    name: "%",
    validExamples: [taxonomy_term__unit__percentage_example],
    erroredExamples: [taxonomy_term__unit__percentage_error]
});
taxonomy_term__unit__percentage.setMainDescription("Units for area are: % of acres.");
taxonomy_term__unit__percentage.setConstant({ attribute: "name", value: "%" });
taxonomy_term__unit__percentage.store();

//taxonomy_term--unit--amount
let taxonomy_term__unit__amount_uuid = randomUUID();
let taxonomy_term__unit__amount_example = {
    id: taxonomy_term__unit__amount_uuid,
    attributes: { name: "oz_acre" }
};
let taxonomy_term__unit__amount_error = {
    id: taxonomy_term__unit__amount_uuid,
    attributes: { name: "blah" }
};
let taxonomy_term__unit__amount = new builder.SchemaOverlay({
    typeAndBundle: "taxonomy_term--unit",
    name: "amount",
    validExamples: [taxonomy_term__unit__amount_example],
    erroredExamples: [taxonomy_term__unit__amount_error]
});
taxonomy_term__unit__amount.setMainDescription("The units refer to the application amount in a mass to area ratio.");

taxonomy_term__unit__amount.setEnum({
    attribute: "name",
    valuesArray: [
        "cubic_ft",
        "cubic_yard",
        "gallons_acre",
        "g_acre",
        "g_bedft",
        "g_rowft",
        "g_sqft",
        "in",
        "kg_acre",
        "kg_ha",
        "kg_rowmeter",
        "kg_sqm",
        "lbs_acre",
        "lbs_bedft",
        "lbs_rowft",
        "lbs_sqft",
        "liters_acre",
        "metric_tons_acre",
        "metric_tons_ha",
        "oz_acre",
        "oz_bedft",
        "oz_rowft",
        "oz_sqft",
        "gallons",
        "g",
        "kg",
        "lbs",
        "liters",
        "metric_tons",
        "oz",
        "us_tons",
        "us_tons_acre"
    ],
    description: "Several units are available, both imperial and metric. They are all compatible mass to area ratios."
});
taxonomy_term__unit__amount.store();

//quantity--material--active_ingredient_percent
let quantity__material__active_ingredient_percent_uuid = randomUUID();
let quantity__material__active_ingredient_percent_example = {
    id: quantity__material__active_ingredient_percent_uuid,
    attributes: { label: "active_ingredient_percent", measure: "ratio" }
};
let quantity__material__active_ingredient_percent_error = {
    id: quantity__material__active_ingredient_percent_uuid,
    attributes: { label: "name_of_material", measure: "ratio" }
};
let quantity__material__active_ingredient_percent = new builder.SchemaOverlay({
    typeAndBundle: "quantity--material",
    name: "active_ingredient_percent",
    validExamples: [quantity__material__active_ingredient_percent_example],
    erroredExamples: [quantity__material__active_ingredient_percent_error]
});
quantity__material__active_ingredient_percent.setMainDescription("The % of the active ingredient in the product.");
quantity__material__active_ingredient_percent.setConstant({ attribute: "measure", value: "ratio" });
quantity__material__active_ingredient_percent.setConstant({ attribute: "label", value: "active_ingredient_percent" });
quantity__material__active_ingredient_percent.store();

//taxonomy_term--material_type--pesticide
//from taxonomy list in surveystack beta management question 22
let taxonomy_term__unit__pesticide_uuid = randomUUID();
let taxonomy_term__unit__pesticide_example = {
    id: taxonomy_term__unit__pesticide_uuid,
    attributes: { name: "achieve" }
};
let taxonomy_term__unit__pesticide_error = {
    id: taxonomy_term__unit__pesticide_uuid,
    attributes: { name: "something_else" }
};
let taxonomy_term__material_type__pesticide = new builder.SchemaOverlay({
    typeAndBundle: "taxonomy_term--material_type",
    name: "pesticide",
    validExamples: [taxonomy_term__unit__pesticide_example],
    erroredExamples: [taxonomy_term__unit__pesticide_error]
});
taxonomy_term__material_type__pesticide.setMainDescription("The specific name of the pesticide is stored here.");

taxonomy_term__material_type__pesticide.setEnum({
    attribute: "name",
    valuesArray: [
        "2_4_d",
        "achieve",
        "aero",
        "aim",
        "assert_300sc",
        "assure",
        "basagran_forte",
        "broadleaf",
        "brotex_240",
        "caramba",
        "centurion",
        "citric_acid",
        "clethodim",
        "coragen",
        "cornerstone_plus",
        "delaro",
        "everest",
        "express_pro",
        "fluroxypyr",
        "fulvic_acid",
        "glyphosate",
        "grow_ttf",
        "liberty",
        "lorsban",
        "matador_120ec",
        "mcpa_amine_600",
        "prosaro",
        "puma_advance",
        "quilt",
        "roundup",
        "silencer",
        "spartan_charge",
        "startup_540",
        "stellar",
        "toledo_3.6f",
        "toledo_45wp",
        "victor",
        "widematch",
        "zidua"
    ],
    description: "This product name information is stored in a SurveyStack ontology list of herbicides, insecticides and fungicides."
});
taxonomy_term__material_type__pesticide.store();

//taxonomy_term--material_type--application_class
//from taxonomy list in surveystack beta management question 22
let taxonomy_term__material_type__application_class_uuid = randomUUID();
let taxonomy_term__material_type__application_class_example = {
    id: taxonomy_term__material_type__application_class_uuid,
    attributes: { name: "insecticide" }
};
let taxonomy_term__material_type__application_class_error = {
    id: taxonomy_term__material_type__application_class_uuid,
    attributes: { name: "something_else" }
};
let taxonomy_term__material_type__application_class = new builder.SchemaOverlay({
    typeAndBundle: "taxonomy_term--material_type", 
    name: "application_class",
    validExamples: [taxonomy_term__material_type__application_class_example],
    erroredExamples: [taxonomy_term__material_type__application_class_error]
});
taxonomy_term__material_type__application_class.setMainDescription("The class of application is stored here.");

taxonomy_term__material_type__application_class.setEnum({
    attribute: "name",
    valuesArray: [
        "pesticide",
        "herbicide",
        "insecticide",
        "fungicide",
        "rodenticide",
        "nematicide",
        "bactericide",
        "rodenticide",
        "molluscicide",
        "biological"
    ],
    description: "The class of application based on the type of pest that is being addressed or benefit applied with this input."
});
taxonomy_term__material_type__application_class.store();