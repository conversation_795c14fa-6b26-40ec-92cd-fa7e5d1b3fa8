// Survey: https://app.surveystack.io/surveys/6346c1e467f839000121503e/edit
// Section: 22, Pesticides
const builder = require('convention_builder');
const fs = require('fs');
require('dotenv').config({ path: `${__dirname}/../../.env` });

//Main entity
////overlays and constants
let log__input__pesticide = new builder.SchemaOverlay({
    typeAndBundle: 'log--input',
    name: 'pesticide',
});
log__input__pesticide.setMainDescription("This log records pesticide applications");

log__input__pesticide.setConstant({
    attribute:"status",
    value:"done",
    description: "The status should always be set to done to inherit the area."
});


//Convention
// Object
let pesticideConvention = new builder.ConventionSchema({
    title: "Pesticide",
    version: "0.0.3",
    schemaName:"log--input--pesticide",
    repoURL:"www.gitlabrepo.com/version/farmos_conventions",
    description:`## Purpose\n
Pesticide input log to be applied to a plant asset only if the farmer indicated so in their planting records. In other words, this log needs to be assigned to a planting.\n
## Specification\n
text\n`,
});

////add local attributes
pesticideConvention.addAttribute( { schemaOverlayObject: log__input__pesticide, attributeName: "log--input--pesticide", required: true } );

//add global attributes
pesticideConvention.addAttribute( { schemaOverlayObject: "taxonomy_term--material_type--pesticide", attributeName: "taxonomy_term--material_type--pesticide", required: true});
pesticideConvention.addAttribute( { schemaOverlayObject: "quantity--material--active_ingredient_percent", attributeName: "quantity--material--active_ingredient_percent", required: false});
pesticideConvention.addAttribute( { schemaOverlayObject:"taxonomy_term--log_category", attributeName: "taxonomy_term--log_category", required: false } );
pesticideConvention.addAttribute( { schemaOverlayObject:"asset--plant--planting", attributeName: "asset--plant--planting", required: false } );
pesticideConvention.addAttribute( { schemaOverlayObject: "quantity--standard--area_percentage", attributeName: "quantity--standard--area_percentage", required: false});
pesticideConvention.addAttribute( { schemaOverlayObject: "taxonomy_term--unit--%", attributeName: "taxonomy_term--unit--%", required: false});
//LOOK we always push quantity here even if there is no quantity specified, to ensure that the type or name information is stored in the material reference of the quantity
pesticideConvention.addAttribute( { schemaOverlayObject: "quantity--material--pesticide", attributeName: "quantity--material--pesticide", required: true});
pesticideConvention.addAttribute( { schemaOverlayObject: "taxonomy_term--unit--amount", attributeName: "taxonomy_term--unit--amount", required: true});
pesticideConvention.addAttribute( { schemaOverlayObject:"taxonomy_term--log_category--pest_disease_control", attributeName: "taxonomy_term--log_category--pest_disease_control", required: true } );
pesticideConvention.addAttribute( { schemaOverlayObject:"asset--land", attributeName: "asset--land", required: false } );

////add relationships
pesticideConvention.addRelationship( { containerEntity:"log--input--pesticide" , relationName:"category" , mentionedEntity:"taxonomy_term--log_category" , required: false } );
pesticideConvention.addRelationship( { containerEntity:"log--input--pesticide", relationName:"asset", mentionedEntity:"asset--plant--planting", required: false } );
pesticideConvention.addRelationship( { containerEntity:"log--input--pesticide" , relationName:"quantity" , mentionedEntity:"quantity--standard--area_percentage" , required: false } );
pesticideConvention.addRelationship( { containerEntity:"quantity--standard--area_percentage" , relationName:"units" , mentionedEntity:"taxonomy_term--unit--%" , required: false } );
pesticideConvention.addRelationship( { containerEntity:"log--input--pesticide" , relationName:"quantity" , mentionedEntity:"quantity--material--pesticide" , required: false } );
pesticideConvention.addRelationship( { containerEntity:"quantity--material--pesticide" , relationName:"units" , mentionedEntity:"taxonomy_term--unit--amount" , required: false } );
pesticideConvention.addRelationship( { containerEntity:"log--input--pesticide" , relationName:"category" , mentionedEntity:"taxonomy_term--log_category--pest_disease_control" , required: true } );
pesticideConvention.addRelationship( { containerEntity:"quantity--material--pesticide", relationName:"material_type" , mentionedEntity:"taxonomy_term--material_type--pesticide" , required: false } );
pesticideConvention.addRelationship( { containerEntity:"quantity--material--pesticide", relationName:"material_type" , mentionedEntity:"taxonomy_term--material_type--application_class" , required: true } );
pesticideConvention.addRelationship( { containerEntity:"log--input--pesticide" , relationName:"quantity" , mentionedEntity:"quantity--material--active_ingredient_percent" , required: false } );
pesticideConvention.addRelationship( { containerEntity:"quantity--material--active_ingredient_percent" , relationName:"units" , mentionedEntity:"taxonomy_term--unit--%" , required: false } );
pesticideConvention.addRelationship( { containerEntity:"log--input--pesticide" , relationName:"location" , mentionedEntity:"asset--land" , required: false } );


pesticideConvention.addExample({ path: `${__dirname}/examples/correct`, is_valid_example: true });
pesticideConvention.addExample({ path: `${__dirname}/examples/incorrect`, is_valid_example: false });

let test = pesticideConvention.testExamples();
let storageOperation = pesticideConvention.store();
