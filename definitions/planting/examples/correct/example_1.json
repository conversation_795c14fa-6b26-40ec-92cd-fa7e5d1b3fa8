{"id": "83b0b6cb-be02-4302-98aa-bb91eb4c308c", "convention_taxonomy": {"id": "dad2954b-4308-470a-a58f-94119ea64fbf", "attributes": {"name": "Planting", "external_uri": ["https://gitlab.com/api/v4/projects/44311021/jobs/artifacts/main/raw/output/collection/conventions/asset--plant--planting/schema.json?job=copy_schemas"]}}, "asset--plant--planting": {"id": "1843daa8-b225-49a9-a7f9-596693b020f3", "attributes": {"name": "Example plant asset", "status": "active"}, "relationships": {"plant_type": {"data": [{"type": "taxonomy_term--plant_type", "id": "7c465583-394a-4370-8012-c75d73bc4e5e"}, {"type": "taxonomy_term--plant_type", "id": "a04201bc-79ed-4f3b-9fec-3c4e355b2c41"}]}, "season": {"data": [{"type": "taxonomy_term--season", "id": "bab8cc91-da21-4feb-9aa3-751db753e4e1"}]}, "convention": {"data": [{"type": "taxonomy_term--convention", "id": "dad2954b-4308-470a-a58f-94119ea64fbf"}]}}}, "taxonomy_term--season--season": {"id": "bab8cc91-da21-4feb-9aa3-751db753e4e1", "attributes": {"name": "spring, 2022"}}, "taxonomy_term--plant_type--species": {"id": "7c465583-394a-4370-8012-c75d73bc4e5e", "attributes": {"name": "kale"}}, "taxonomy_term--plant_type--variety": {"id": "a04201bc-79ed-4f3b-9fec-3c4e355b2c41", "attributes": {"name": "laccinato"}}}