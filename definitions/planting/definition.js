// Survey: https://app.surveystack.io/surveys/6346c1e467f839000121503e/edit
// Section: 11, planting_div_veg
const builder = require('convention_builder');
const fs = require('fs');
require('dotenv').config({ path: `${__dirname}/../../.env` });

// Taxonomy Terms

let conventionTaxonomyTerm = new builder.SchemaOverlay({
    typeAndBundle: 'taxonomy_term--convention',
    name: 'convention',
});

conventionTaxonomyTerm.setMainDescription("Identifies this set of entities as components in a convention and provides said convention's URL.");

let taxonomy_term__plant_type__variety = new builder.SchemaOverlay({
    typeAndBundle: "taxonomy_term--plant_type",
    name: 'variety',
});
taxonomy_term__plant_type__variety.setMainDescription("The variety of the vegetable planted.");

// species
let taxonomy_term__plant_type__species = new builder.SchemaOverlay({
    typeAndBundle: "taxonomy_term--plant_type",
    name: "species",
});
taxonomy_term__plant_type__species.setMainDescription("The species of the vegetable planted.");

// season
let taxonomy_term__season__season = new builder.SchemaOverlay({
    typeAndBundle: "taxonomy_term--season",
    name: "season",
});
taxonomy_term__season__season.setMainDescription("A season taxonomy term should exist for each working period, and logs and assets should be associated to the related seasons. It is up to the user to decide how they want to categorize their seasons - ex: 2016, or Fall, 2022.");


// // // Convention
// object
let plantingConvention = new builder.ConventionSchema({
    title: "Planting",
    version: "0.0.1",
    schemaName:"asset--plant--planting",
    repoURL:"www.gitlabrepo.com/version/farmos_conventions",
    description:`## Purpose\n
This is the main entity that represents a cultivar which will be referenced by all subsequent managment logs.\n
## Specification\n
text\n`,
});

//add local attributes
plantingConvention.addAttribute( { schemaOverlayObject:conventionTaxonomyTerm, attributeName: "convention_taxonomy", required: true } );
plantingConvention.addAttribute( { schemaOverlayObject:taxonomy_term__plant_type__species, attributeName: "taxonomy_term--plant_type--species", required: false } );
plantingConvention.addAttribute( { schemaOverlayObject:taxonomy_term__plant_type__variety, attributeName: "taxonomy_term--plant_type--variety", required: false } );
plantingConvention.addAttribute( { schemaOverlayObject:taxonomy_term__season__season, attributeName: "taxonomy_term--season--season", required: false } );

//add global attributes
plantingConvention.addAttribute( { schemaOverlayObject:"asset--plant--planting", attributeName: "asset--plant--planting", required: true } );
//plantingConvention.addAttribute( { schemaOverlayObject:season, attributeName: "season_taxonomy", required: true } );

plantingConvention.addRelationship( { containerEntity:"asset--plant--planting" , relationName:"plant_type" , mentionedEntity:"taxonomy_term--plant_type--species" , required: true } );
plantingConvention.addRelationship( { containerEntity:"asset--plant--planting" , relationName:"plant_type" , mentionedEntity:"taxonomy_term--plant_type--variety" , required: false } );
plantingConvention.addRelationship( { containerEntity:"asset--plant--planting" , relationName:"season" , mentionedEntity:"taxonomy_term--season--season" , required: true } );
plantingConvention.addRelationship( { containerEntity:"asset--plant--planting" , relationName:"convention" , mentionedEntity:"convention_taxonomy" , required: true } );


plantingConvention.addExample({ path: `${__dirname}/examples/correct`, is_valid_example: true });
plantingConvention.addExample({ path: `${__dirname}/examples/incorrect`, is_valid_example: false });

let storageOperation = plantingConvention.store();
