// Survey: https://app.surveystack.io/surveys/6346c1e467f839000121503e/edit
// Section: 11, planting_div_veg
const builder = require('convention_builder');
const fs = require('fs');
require('dotenv').config({ path: `${__dirname}/../../.env` });


// // Main entity: Seeding Log
let log__seeding__seeding = new builder.SchemaOverlay({
    typeAndBundle: 'log--seeding',
    name: 'seeding',
});

log__seeding__seeding.setMainDescription("A seeding or transplanting log sets the location of the plant asset.");

// required attribute is status
log__seeding__seeding.setConstant({
    attribute:"status",
    value:"done"
});

// required to set location of asset
log__seeding__seeding.setConstant({
    attribute:"is_movement",
    value:"false"
});

// seeding rate quantity
let quantity__standard__seeding_rate = new builder.SchemaOverlay({
    typeAndBundle: "quantity--standard",
    name: "seeding_rate",
});
quantity__standard__seeding_rate.setMainDescription("The seeding rate indicates the mass of seed used for every surface unit.");
quantity__standard__seeding_rate.setConstant({
    attribute: "measure",
    value:"rate"
});
quantity__standard__seeding_rate.setConstant({
    attribute: "label",
    value:"seeding rate"
});

// seeding rate unit
let taxonomy_term__unit__seeding_rate = new builder.SchemaOverlay({
    typeAndBundle: "taxonomy_term--unit",
    name: "seeding_rate",
});
taxonomy_term__unit__seeding_rate.setEnum({
    attribute: "name",
    valuesArray: [
        "lbs/ac",
        "seeds/acre",
        "kg/hec",
        "seeds/hec",
        "plants per sq ft",
        "plants per sq m"
    ],
    description: "Several units are available, both imperial and metric. They are all compatible mass to area ratios."
});
taxonomy_term__unit__seeding_rate.setMainDescription("Unit used to measure the rate quantity.");

// bed width
let quantity__standard__bed_width = new builder.SchemaOverlay({
    typeAndBundle: "quantity--standard",
    name: "bed_width",
});
quantity__standard__bed_width.setMainDescription("The bed width indicates the size of permanent beds.");
quantity__standard__bed_width.setConstant({
    attribute: "measure",
    value:"length/depth"
});
quantity__standard__bed_width.setConstant({
    attribute: "label",
    value:"bed width"
});

// bed width unit
let taxonomy_term__unit__bed_width = new builder.SchemaOverlay({
    typeAndBundle: "taxonomy_term--unit",
    name: "bed_width",
});
taxonomy_term__unit__bed_width.setEnum({
    attribute: "name",
    valuesArray: [
        "feet",
        "meters"
    ],
    description: "Several units are available, both imperial and metric."
});
taxonomy_term__unit__bed_width.setMainDescription("Unit used to measure the bed width.");


// Convention
let seedingConvention = new builder.ConventionSchema({
    title: "Seeding",
    version: "0.0.1",
    schemaName:"log--seeding--seeding",
    repoURL:"www.gitlabrepo.com/version/farmos_conventions",
    description:`## Purpose\n
A seeding log is required for every plant asset.\n
## Specification\n
text\n`,
});

//add local attributes
seedingConvention.addAttribute( { schemaOverlayObject:log__seeding__seeding, attributeName: "log--seeding--seeding", required: true } );
seedingConvention.addAttribute( {schemaOverlayObject:quantity__standard__seeding_rate, attributeName:"quantity--standard--seeding_rate", required: false} );
seedingConvention.addAttribute( {schemaOverlayObject:taxonomy_term__unit__seeding_rate, attributeName:"taxonomy_term--unit--seeding_rate", required: false} );
seedingConvention.addAttribute( {schemaOverlayObject:quantity__standard__bed_width, attributeName:"quantity--standard--bed_width", required: false} );
seedingConvention.addAttribute( {schemaOverlayObject:taxonomy_term__unit__bed_width, attributeName:"taxonomy_term--unit--bed_width", required: false} );

//add global attributes
seedingConvention.addAttribute( { schemaOverlayObject:"taxonomy_term--log_category", attributeName: "taxonomy_term--log_category", required: false } );
seedingConvention.addAttribute( { schemaOverlayObject:"taxonomy_term--log_category--seeding", attributeName: "taxonomy_term--log_category--seeding", required: true } );
seedingConvention.addAttribute( { schemaOverlayObject:"quantity--standard--area_percentage", attributeName: "quantity--standard--area_percentage", required: true } );
seedingConvention.addAttribute( { schemaOverlayObject:"taxonomy_term--unit--%", attributeName: "taxonomy_term--unit--%", required: true } );
// NEW We can add a relationship to an existing overlay using it's name, like here. In this case, we checked in collection/overlays and saw a folder called `asset--plant--planting`. We can call that overlay using that exact name. This is exactly equivalent to using an overlay we've just written in this file. Below (line 342), I will add a relationship to it.
seedingConvention.addAttribute( { schemaOverlayObject:"asset--plant--planting", attributeName: "asset--plant--planting", required: false } );

//add relationships
seedingConvention.addRelationship( { containerEntity:"log--seeding--seeding" , relationName:"category" , mentionedEntity:"taxonomy_term--log_category" , required: false } );
seedingConvention.addRelationship( { containerEntity:"log--seeding--seeding" , relationName:"category" , mentionedEntity:"taxonomy_term--log_category--seeding" , required: true } );
seedingConvention.addRelationship( { containerEntity:"log--seeding--seeding" , relationName:"quantity" , mentionedEntity:"quantity--standard--area_percentage" , required: true } );
seedingConvention.addRelationship( { containerEntity:"quantity--standard--area_percentage" , relationName:"units" , mentionedEntity:"taxonomy_term--unit--%" , required: true } );
seedingConvention.addRelationship( { containerEntity:"quantity--standard--seeding_rate" , relationName:"units" , mentionedEntity:"taxonomy_term--unit--seeding_rate" , required: true } );
seedingConvention.addRelationship( { containerEntity:"quantity--standard--bed_width" , relationName:"units" , mentionedEntity:"taxonomy_term--unit--bed_width" , required: true } );
// Let's write the relationship between our main entity for this convention (the seeding log) and the asset--plant--planting overlay.
seedingConvention.addRelationship(
    {
        // The containerEntity is the main entity, the "log--seeding--seeding"
        containerEntity:"log--seeding--seeding" ,
        // Looking into the schema for the log--seeding and it's "relationship" fields, we can relate the land asset under the field "location" (LOOK shouldn't this be asset?)
        relationName:"asset",
        // The mentioned entity is added using it's attribute name, as when invokint addAttribute. We've effectively used "addAttribute", so it is the same as if we had just created the overlay.
        // We used `attributeName: "asset--plant--planting"`
        mentionedEntity:"asset--plant--planting",
        required: true
    } );



seedingConvention.addExample({ path: `${__dirname}/examples/correct`, is_valid_example: true });
seedingConvention.addExample({ path: `${__dirname}/examples/incorrect`, is_valid_example: false });

let storageOperation = seedingConvention.store();
