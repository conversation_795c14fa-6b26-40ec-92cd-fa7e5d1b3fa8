{"name": "json_schema_experiments", "version": "1.7.0", "main": "./dist/node/farmos_schemata_validator.js", "browser": "./dist/browser/farmos_schemata_validator.js", "module": "./dist/module/farmos_schemata_validator.js", "files": ["./dist/node/farmos_schemata_validator.js", "./dist/browser/farmos_schemata_validator.js", "./dist/module/farmos_schemata_validator.js", "./dist/node/convention_schemata_validator.js", "./dist/browser/convention_schemata_validator.js", "./dist/module/convention_schemata_validator.js"], "description": "Some experiments to understand json schema features and how to better encode the data we need to deal with.", "scripts": {"test": "jest"}, "repository": {"type": "git", "url": "https://gitlab.com/our-sci/conventions/common_farm_conventions"}, "jest": {"reporters": ["default", "jest-junit"]}, "jest-junit": {"outputDirectory": "./test/test_results", "outputName": "junit_test_results.xml", "classNameTemplate": "{suitename}"}, "keywords": ["json", "schema", "data", "validation", "consistency", "conventions", "farmos"], "author": "<PERSON><PERSON><PERSON>", "license": "GPL-3.0-or-later", "bugs": {"url": "https://gitlab.com/our-sci/conventions/common_farm_conventions/issues"}, "homepage": "https://gitlab.com/our-sci/conventions/common_farm_conventions#readme", "dependencies": {"ajv": "^8.12.0", "ajv-formats": "^2.1.1", "axios": "^1.6.2", "convention_builder": "^1.8.5", "dotenv": "^16.0.3", "esbuild": "^0.17.11", "farmos": "^2.0.0-beta.16", "jsdoc": "^4.0.2"}, "devDependencies": {"@types/node": "^20.2.5", "jest": "^29.4.3", "jest-junit": "^15.0.0", "papaparse": "^5.4.1"}}